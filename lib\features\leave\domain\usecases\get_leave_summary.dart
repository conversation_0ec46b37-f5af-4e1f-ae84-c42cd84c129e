import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/leave_balance.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class GetLeaveSummary implements UseCase<LeaveBalance, NoParams> {
  final LeaveRepository repository;

  GetLeaveSummary(this.repository);

  @override
  Future<Either<Failure, LeaveBalance>> call(NoParams params) async {
    return await repository.getLeaveSummary();
  }
}
