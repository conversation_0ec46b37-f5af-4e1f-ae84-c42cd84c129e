import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class CancelLeaveRequest implements UseCase<void, CancelLeaveRequestParams> {
  final LeaveRepository repository;

  CancelLeaveRequest(this.repository);

  @override
  Future<Either<Failure, void>> call(CancelLeaveRequestParams params) async {
    return await repository.cancelLeaveRequest(params.id);
  }
}

class CancelLeaveRequestParams extends Equatable {
  final String id;

  const CancelLeaveRequestParams({required this.id});

  @override
  List<Object> get props => [id];
}
