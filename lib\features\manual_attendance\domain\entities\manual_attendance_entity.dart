import 'package:equatable/equatable.dart';

class ManualAttendanceEntity extends Equatable {
  final String id;
  final String userId;
  final String fullName;
  final String reason;
  final String? failureImage;
  final Map<String, dynamic> deviceInfo;
  final bool isCheckIn;
  final DateTime timestamp;
  final ManualAttendanceStatus status;
  final String? adminNote;
  final String? reviewedBy;
  final DateTime? reviewedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserInfo? userInfo;
  final UserInfo? reviewerInfo;

  const ManualAttendanceEntity({
    required this.id,
    required this.userId,
    required this.fullName,
    required this.reason,
    this.failureImage,
    required this.deviceInfo,
    required this.isCheckIn,
    required this.timestamp,
    required this.status,
    this.adminNote,
    this.reviewedBy,
    this.reviewedAt,
    required this.createdAt,
    required this.updatedAt,
    this.userInfo,
    this.reviewerInfo,
  });

  @override
  List<Object?> get props => [
        id,
        userId,
        fullName,
        reason,
        failureImage,
        deviceInfo,
        isCheckIn,
        timestamp,
        status,
        adminNote,
        reviewedBy,
        reviewedAt,
        createdAt,
        updatedAt,
        userInfo,
        reviewerInfo,
      ];
}

enum ManualAttendanceStatus {
  pending,
  approved,
  rejected,
}

extension ManualAttendanceStatusExtension on ManualAttendanceStatus {
  String get displayName {
    switch (this) {
      case ManualAttendanceStatus.pending:
        return 'Chờ duyệt';
      case ManualAttendanceStatus.approved:
        return 'Đã duyệt';
      case ManualAttendanceStatus.rejected:
        return 'Từ chối';
    }
  }

  String get value {
    switch (this) {
      case ManualAttendanceStatus.pending:
        return 'pending';
      case ManualAttendanceStatus.approved:
        return 'approved';
      case ManualAttendanceStatus.rejected:
        return 'rejected';
    }
  }

  static ManualAttendanceStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return ManualAttendanceStatus.pending;
      case 'approved':
        return ManualAttendanceStatus.approved;
      case 'rejected':
        return ManualAttendanceStatus.rejected;
      default:
        return ManualAttendanceStatus.pending;
    }
  }
}

class UserInfo extends Equatable {
  final String id;
  final String fullName;
  final String email;
  final String? department;

  const UserInfo({
    required this.id,
    required this.fullName,
    required this.email,
    this.department,
  });

  @override
  List<Object?> get props => [id, fullName, email, department];
}

class ManualAttendancePagination extends Equatable {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;

  const ManualAttendancePagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
  });

  @override
  List<Object> get props => [currentPage, totalPages, totalItems, itemsPerPage];
}
