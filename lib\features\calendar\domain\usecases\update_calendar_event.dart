import 'package:dartz/dartz.dart';
import 'package:golderhr/core/usecases/usecase.dart';

import '../../../../core/error/failures.dart';

import '../entities/calendar_event.dart';
import '../params/update_event_params.dart';
import '../repositories/calendar_repository.dart';

class UpdateCalendarEvent implements UseCase<CalendarEvent, UpdateEventParams> {
  final CalendarRepository repository;
  UpdateCalendarEvent(this.repository);

  @override
  Future<Either<Failure, CalendarEvent>> call(UpdateEventParams params) async {
    return await repository.updateCalendarEvent(params.eventId!, params);
  }

}