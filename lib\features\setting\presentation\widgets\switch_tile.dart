// features/setting/presentation/widgets/switch_tile.dart

import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class SwitchTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color; // <PERSON><PERSON><PERSON> này sẽ được dùng cho icon VÀ switch
  final bool value;
  final ValueChanged<bool> onChanged;

  const SwitchTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsive = context.responsive;

    return Padding(
      padding: responsive.padding(all: 16),
      child: Row(
        children: [
          Container(
            padding: responsive.padding(all: 10),
            decoration: BoxDecoration(
              // Dùng màu được truyền vào với độ mờ
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            // Icon cũng dùng màu này
            child: Icon(icon, color: color, size: responsive.fontSize(20)),
          ),
          SizedBox(width: responsive.widthPercentage(4)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.hintColor,
                  ),
                ),
              ],
            ),
          ),
          // SỬA Ở ĐÂY: Áp dụng màu cho Switch
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: color, // Màu của switch khi ở trạng thái bật
          ),
        ],
      ),
    );
  }
}
