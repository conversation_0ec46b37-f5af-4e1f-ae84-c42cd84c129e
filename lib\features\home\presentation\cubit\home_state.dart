import '../../../notification/data/model/notification_model.dart';

abstract class HomeState {}

class HomeInitial extends HomeState {}

class HomeLoading extends HomeState {}

class HomeNotificationEmpty extends HomeState {}

class HomeError extends HomeState {
  final String message;

  HomeError(this.message);
}

class HomeLoaded extends HomeState {
  final List<NotificationModel> notifications;

  HomeLoaded(this.notifications);
}
