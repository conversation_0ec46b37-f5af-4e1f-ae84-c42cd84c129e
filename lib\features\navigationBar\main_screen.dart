import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../core/services/navigation_service.dart';
import '../../injection_container.dart';
import 'navigation_bar.dart';

class MainScreen extends StatefulWidget {
  final StatefulNavigationShell navigationShell;

  const MainScreen({super.key, required this.navigationShell});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    // Xử lý pending navigation sau khi widget được build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _processPendingNavigation();
    });
  }

  Future<void> _processPendingNavigation() async {
    try {
      final navigationService = sl<NavigationService>();
      await navigationService.processPendingNavigation();
    } catch (e) {
      debugPrint('Error processing pending navigation: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.navigationShell,
      bottomNavigationBar: CustomNavigationBar(
        selectedIndex: widget.navigationShell.currentIndex,
        onTabChange: (index) {
          widget.navigationShell.goBranch(
            index,
            initialLocation: index == widget.navigationShell.currentIndex,
          );
        },
      ),
    );
  }
}
