import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';
import '../../domain/entities/attendance_status_entity.dart'; // QUAN TRỌNG

class FaceDetectionState extends Equatable {
  final File? image;
  final String status;
  final List<Face> faces;
  final bool isLoading;
  final Position? currentLocation;
  final String locationStatus;
  final AttendanceStatusEntity? attendanceStatus; // <-- SỬ DỤNG TRỰC TIẾP

  const FaceDetectionState({
    this.image,
    this.status = '', // Để trống hoặc một giá trị khởi tạo chung
    this.faces = const [],
    this.isLoading = false,
    this.currentLocation,
    this.locationStatus = '',
    this.attendanceStatus,
  });

  FaceDetectionState copyWith({
    File? image,
    String? status,
    List<Face>? faces,
    bool? isLoading,
    Position? currentLocation,
    String? locationStatus,
    AttendanceStatusEntity? attendanceStatus,
    bool? clearAttendanceStatus,
    bool? clearImageAndFaces,
  }) {
    bool shouldDeleteImage = image == null && this.image != null;

    return FaceDetectionState(
      image: clearImageAndFaces == true
          ? null
          : (shouldDeleteImage ? null : (image ?? this.image)),
      status: status ?? this.status,
      faces: clearImageAndFaces == true ? [] : (faces ?? this.faces),
      isLoading: isLoading ?? this.isLoading,
      currentLocation: currentLocation ?? this.currentLocation,
      locationStatus: locationStatus ?? this.locationStatus,
      attendanceStatus: clearAttendanceStatus == true
          ? null
          : (attendanceStatus ?? this.attendanceStatus),
    );
  }

  @override
  List<Object?> get props => [
    image,
    status,
    faces,
    isLoading,
    currentLocation,
    locationStatus,
    attendanceStatus,
  ];
}
