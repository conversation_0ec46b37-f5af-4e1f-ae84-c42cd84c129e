import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import '../../domain/usecases/get_manual_attendance_requests.dart';
import '../../domain/usecases/review_manual_attendance.dart';
import 'manual_attendance_state.dart';

class ManualAttendanceCubit extends Cubit<ManualAttendanceState> {
  final GetManualAttendanceRequestsUseCase _getRequestsUseCase;
  final ReviewManualAttendanceUseCase _reviewUseCase;

  ManualAttendanceCubit({
    required GetManualAttendanceRequestsUseCase getRequestsUseCase,
    required ReviewManualAttendanceUseCase reviewUseCase,
  })  : _getRequestsUseCase = getRequestsUseCase,
        _reviewUseCase = reviewUseCase,
        super(ManualAttendanceInitial());

  Future<void> getManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    emit(ManualAttendanceLoading());

    final result = await _getRequestsUseCase(GetManualAttendanceRequestsParams(
      page: page,
      limit: limit,
      status: status,
      userId: userId,
      startDate: startDate,
      endDate: endDate,
    ));

    result.fold(
      (failure) => emit(ManualAttendanceError(failure.message)),
      (requests) => emit(ManualAttendanceLoaded(requests: requests)),
    );
  }

  Future<void> reviewManualAttendance({
    required String id,
    required ManualAttendanceStatus status,
    String? adminNote,
  }) async {
    final currentState = state;
    if (currentState is! ManualAttendanceLoaded) return;

    emit(ManualAttendanceLoading());

    final result = await _reviewUseCase(ReviewManualAttendanceParams(
      id: id,
      status: status,
      adminNote: adminNote,
    ));

    result.fold(
      (failure) {
        emit(ManualAttendanceError(failure.message));
        // Restore previous state after showing error
        Future.delayed(const Duration(seconds: 2), () {
          if (state is ManualAttendanceError) {
            emit(currentState);
          }
        });
      },
      (updatedRequest) {
        // Update the request in the current list
        final updatedRequests = currentState.requests.map((request) {
          return request.id == updatedRequest.id ? updatedRequest : request;
        }).toList();

        emit(ManualAttendanceLoaded(
          requests: updatedRequests,
          pagination: currentState.pagination,
        ));

        // Emit success state briefly
        emit(ManualAttendanceReviewSuccess(updatedRequest));
        Future.delayed(const Duration(seconds: 1), () {
          if (state is ManualAttendanceReviewSuccess) {
            emit(ManualAttendanceLoaded(
              requests: updatedRequests,
              pagination: currentState.pagination,
            ));
          }
        });
      },
    );
  }

  void refresh() {
    if (state is ManualAttendanceLoaded) {
      getManualAttendanceRequests();
    }
  }
}
