import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:golderhr/core/network/dio_client.dart';
import 'package:golderhr/injection_container.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import 'navigation_service.dart';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  FirebaseMessaging? _messaging;
  FlutterLocalNotificationsPlugin? _localNotifications;
  String? _fcmToken;

  // Getters
  String? get fcmToken => _fcmToken;
  FirebaseMessaging? get messaging => _messaging;

  /// Khởi tạo Firebase và FCM
  Future<void> initialize() async {
    try {
      // Khởi tạo Firebase
      await Firebase.initializeApp();

      _messaging = FirebaseMessaging.instance;

      // Yêu cầu quyền notification
      await _requestPermission();

      // Khởi tạo local notifications
      await _initializeLocalNotifications();

      // Lấy FCM token
      await _getFCMToken();

      // Đăng ký FCM token với backend
      await _registerTokenWithBackend();

      // Setup message handlers
      _setupMessageHandlers();

      AppLogger.info('Firebase Service initialized successfully');
    } catch (e) {
      AppLogger.error('Error initializing Firebase Service', e);
    }
  }

  /// Khởi tạo Firebase Service mà không đăng ký token với backend
  /// (dùng khi app start, chưa có JWT token)
  Future<void> initializeWithoutBackendRegistration() async {
    try {
      // Khởi tạo Firebase
      await Firebase.initializeApp();

      _messaging = FirebaseMessaging.instance;

      // Yêu cầu quyền notification
      await _requestPermission();

      // Khởi tạo local notifications
      await _initializeLocalNotifications();

      // Lấy FCM token (nhưng không đăng ký với backend)
      await _getFCMToken();

      // Setup message handlers
      _setupMessageHandlers();

      AppLogger.info(
        'Firebase Service initialized successfully (without backend registration)',
      );
    } catch (e) {
      AppLogger.error('Error initializing Firebase Service', e);
    }
  }

  /// Yêu cầu quyền notification
  Future<void> _requestPermission() async {
    if (_messaging == null) return;

    NotificationSettings settings = await _messaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    AppLogger.info('User granted permission: ${settings.authorizationStatus}');
  }

  /// Khởi tạo local notifications
  Future<void> _initializeLocalNotifications() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _localNotifications!.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Tạo notification channel cho Android
    await _createNotificationChannel();
  }

  /// Tạo notification channel cho Android
  Future<void> _createNotificationChannel() async {
    if (_localNotifications == null) return;

    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'high_importance_channel', // id
      'High Importance Notifications', // title
      description: 'This channel is used for important notifications.',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
      showBadge: true,
    );

    await _localNotifications!
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(channel);

    AppLogger.info('Notification channel created: ${channel.id}');
  }

  /// Lấy FCM token
  Future<void> _getFCMToken() async {
    if (_messaging == null) return;

    try {
      _fcmToken = await _messaging!.getToken();
      AppLogger.info('FCM Token: $_fcmToken');

      // Lắng nghe token refresh
      _messaging!.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        AppLogger.info('FCM Token refreshed: $newToken');
        _registerTokenWithBackend();
      });
    } catch (e) {
      AppLogger.error('Error getting FCM token', e);
    }
  }

  /// Đăng ký FCM token với backend
  Future<void> _registerTokenWithBackend() async {
    if (_fcmToken == null) return;

    try {
      final deviceInfo = await _getDeviceInfo();
      final dioClient = sl<DioClient>();

      await dioClient.post(
        '/api/notifications/fcm-token',
        data: {
          'token': _fcmToken,
          'deviceType': Platform.isAndroid ? 'android' : 'ios',
          'deviceId': deviceInfo['deviceId'],
          'deviceInfo': deviceInfo,
        },
      );

      AppLogger.info('FCM token registered with backend successfully');
    } catch (e) {
      AppLogger.error('Error registering FCM token with backend', e);

      // Nếu lỗi 401 (token hết hạn), thử đăng ký lại sau khi user login
      if (e.toString().contains('401')) {
        AppLogger.info(
          'Token expired, will retry FCM registration after user login',
        );
        // Có thể lưu flag để retry sau khi user login thành công
      }
    }
  }

  /// Lấy thông tin device
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return {
        'deviceId': androidInfo.id,
        'model': androidInfo.model,
        'brand': androidInfo.brand,
        'osVersion': androidInfo.version.release,
        'appVersion': '1.0.0', // Có thể lấy từ package_info_plus
      };
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return {
        'deviceId': iosInfo.identifierForVendor,
        'model': iosInfo.model,
        'brand': 'Apple',
        'osVersion': iosInfo.systemVersion,
        'appVersion': '1.0.0',
      };
    }

    return {
      'deviceId': 'unknown',
      'model': 'unknown',
      'brand': 'unknown',
      'osVersion': 'unknown',
      'appVersion': '1.0.0',
    };
  }

  /// Setup message handlers
  void _setupMessageHandlers() {
    if (_messaging == null) return;

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      AppLogger.info('Got a message whilst in the foreground!');
      AppLogger.debug('Message data: ${message.data}');

      if (message.notification != null) {
        AppLogger.info(
          'Message also contained a notification: ${message.notification}',
        );
        _showLocalNotification(message);
      }
    });

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      AppLogger.info('A new onMessageOpenedApp event was published!');
      _handleNotificationTap(message);
    });

    // Handle notification tap when app is terminated
    _messaging!.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        AppLogger.info('App opened from terminated state via notification');
        _handleNotificationTap(message);
      }
    });
  }

  /// Hiển thị local notification khi app đang foreground
  Future<void> _showLocalNotification(RemoteMessage message) async {
    if (_localNotifications == null) return;

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription:
              'This channel is used for important notifications.',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications!.show(
      message.hashCode,
      message.notification?.title ?? 'Thông báo',
      message.notification?.body ?? '',
      platformChannelSpecifics,
      payload: jsonEncode(message.data),
    );
  }

  /// Xử lý khi user tap vào notification
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      _handleNotificationData(data);
    }
  }

  /// Hiển thị local notification cho testing
  Future<void> showLocalNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    if (_localNotifications == null) {
      AppLogger.error('Local notifications not initialized');
      return;
    }

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
          'high_importance_channel', // Sử dụng cùng channel
          'High Importance Notifications',
          channelDescription:
              'This channel is used for important notifications.',
          importance: Importance.high,
          priority: Priority.high,
          showWhen: true,
          enableVibration: true,
          playSound: true,
        );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
        );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    try {
      await _localNotifications!.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        platformChannelSpecifics,
        payload: data != null ? jsonEncode(data) : null,
      );
      AppLogger.info('Local notification shown: $title');
    } catch (e) {
      AppLogger.error('Error showing local notification', e);
    }
  }

  /// Xử lý khi tap notification từ FCM
  void _handleNotificationTap(RemoteMessage message) {
    _handleNotificationData(message.data);
  }

  /// Xử lý data của notification
  void _handleNotificationData(Map<String, dynamic> data) {
    AppLogger.info('Handling notification data: $data');

    // Sử dụng NavigationService để điều hướng
    try {
      final navigationService = sl<NavigationService>();
      navigationService.handleNotificationTap(data);
    } catch (e) {
      AppLogger.error('Error handling notification navigation', e);
      // Fallback: try to navigate to notifications page
      try {
        final navigationService = NavigationService.instance;
        navigationService.navigateToNotifications();
      } catch (fallbackError) {
        AppLogger.error('Fallback navigation also failed', fallbackError);
      }
    }
  }

  /// Xóa FCM token khỏi backend khi logout
  Future<void> removeTokenFromBackend() async {
    if (_fcmToken == null) return;

    try {
      final dioClient = sl<DioClient>();
      await dioClient.delete(
        '/api/notifications/fcm-token',
        data: {'token': _fcmToken},
      );

      AppLogger.info('FCM token removed from backend successfully');
    } catch (e) {
      AppLogger.error('Error removing FCM token from backend', e);
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.subscribeToTopic(topic);
      AppLogger.info('Subscribed to topic: $topic');
    } catch (e) {
      AppLogger.error('Error subscribing to topic', e);
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    if (_messaging == null) return;

    try {
      await _messaging!.unsubscribeFromTopic(topic);
      AppLogger.info('Unsubscribed from topic: $topic');
    } catch (e) {
      AppLogger.error('Error unsubscribing from topic', e);
    }
  }

  /// Retry FCM token registration (gọi sau khi user login thành công)
  Future<void> retryFCMRegistration() async {
    AppLogger.info('Retrying FCM token registration...');
    await _registerTokenWithBackend();
  }

  /// Lấy FCM token hiện tại
  Future<String?> getCurrentToken() async {
    if (_messaging == null) return null;

    try {
      if (_fcmToken == null) {
        _fcmToken = await _messaging!.getToken();
        AppLogger.info('FCM Token retrieved: $_fcmToken');
      }
      return _fcmToken;
    } catch (e) {
      AppLogger.error('Error getting current FCM token', e);
      return null;
    }
  }
}

/// Background message handler (phải là top-level function)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // Note: AppLogger không thể sử dụng trong background handler
  print('Handling a background message: ${message.messageId}');
}
