import 'package:equatable/equatable.dart';

class UserProfile extends Equatable {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String avatarUrl;
  final String department;
  final String position;

  const UserProfile({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.avatarUrl,
    required this.department,
    required this.position,
  });

  @override
  List<Object> get props => [
    id,
    name,
    email,
    phone,
    avatarUrl,
    department,
    position,
  ];
}
