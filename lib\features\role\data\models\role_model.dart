import '../../domain/entities/role_entity.dart';

class RoleModel extends RoleEntity {
  const RoleModel({
    required super.id,
    required super.name,
    super.createdAt,
    super.updatedAt,
  });

  factory RoleModel.fromJson(Map<String, dynamic> json) {
    return RoleModel(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  @override
  RoleModel copyWith({
    String? id,
    String? name,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RoleModel(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'RoleModel(id: $id, name: $name, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

// Create role parameters
class CreateRoleParams {
  final String name;

  const CreateRoleParams({required this.name});

  Map<String, dynamic> toJson() {
    return {'name': name};
  }
}

// Update role parameters
class UpdateRoleParams {
  final String roleId;
  final String? name;

  const UpdateRoleParams({required this.roleId, this.name});

  Map<String, dynamic> toJson() {
    return {if (name != null) 'name': name};
  }
}
