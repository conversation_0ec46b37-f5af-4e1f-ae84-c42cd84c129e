import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/manual_attendance_entity.dart';
import '../repositories/manual_attendance_repository.dart';

class GetManualAttendanceRequestsUseCase implements UseCase<List<ManualAttendanceEntity>, GetManualAttendanceRequestsParams> {
  final ManualAttendanceRepository repository;

  GetManualAttendanceRequestsUseCase(this.repository);

  @override
  Future<Either<Failure, List<ManualAttendanceEntity>>> call(GetManualAttendanceRequestsParams params) async {
    return await repository.getManualAttendanceRequests(
      page: params.page,
      limit: params.limit,
      status: params.status,
      userId: params.userId,
      startDate: params.startDate,
      endDate: params.endDate,
    );
  }
}

class GetManualAttendanceRequestsParams {
  final int page;
  final int limit;
  final ManualAttendanceStatus? status;
  final String? userId;
  final DateTime? startDate;
  final DateTime? endDate;

  GetManualAttendanceRequestsParams({
    this.page = 1,
    this.limit = 10,
    this.status,
    this.userId,
    this.startDate,
    this.endDate,
  });
}
