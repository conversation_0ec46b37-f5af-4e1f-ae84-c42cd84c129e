
import 'package:equatable/equatable.dart';

import '../../domain/entities/department_entity.dart';

class DepartmentState extends Equatable {
  final bool isLoading;
  final bool isProcessing;
  final List<DepartmentEntity> departments;
  final List<DepartmentEntity> dropdownDepartments;
  final List<DepartmentEntity> hierarchyDepartments;
  final DepartmentListResult? departmentListResult;
  final DepartmentEntity? selectedDepartment;
  final String? error;
  final String? successMessage;
  final int currentPage;
  final bool hasReachedMax;
  final bool includeDeleted;

  const DepartmentState({
    this.isLoading = false,
    this.isProcessing = false,
    this.departments = const [],
    this.dropdownDepartments = const [],
    this.hierarchyDepartments = const [],
    this.departmentListResult,
    this.selectedDepartment,
    this.error,
    this.successMessage,
    this.currentPage = 1,
    this.hasReachedMax = false,
    this.includeDeleted = false,
  });

  DepartmentState copyWith({
    bool? isLoading,
    bool? isProcessing,
    List<DepartmentEntity>? departments,
    List<DepartmentEntity>? dropdownDepartments,
    List<DepartmentEntity>? hierarchyDepartments,
    DepartmentListResult? departmentListResult,
    DepartmentEntity? selectedDepartment,
    String? error,
    String? successMessage,
    int? currentPage,
    bool? hasReachedMax,
    bool? includeDeleted,
  }) {
    return DepartmentState(
      isLoading: isLoading ?? this.isLoading,
      isProcessing: isProcessing ?? this.isProcessing,
      departments: departments ?? this.departments,
      dropdownDepartments: dropdownDepartments ?? this.dropdownDepartments,
      hierarchyDepartments: hierarchyDepartments ?? this.hierarchyDepartments,
      departmentListResult: departmentListResult ?? this.departmentListResult,
      selectedDepartment: selectedDepartment ?? this.selectedDepartment,
      error: error,
      successMessage: successMessage,
      currentPage: currentPage ?? this.currentPage,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      includeDeleted: includeDeleted ?? this.includeDeleted,
    );
  }

  bool get hasError => error != null;
  bool get hasSuccess => successMessage != null;

  @override
  List<Object?> get props => [
    isLoading,
    isProcessing,
    departments,
    dropdownDepartments,
    hierarchyDepartments,
    departmentListResult,
    selectedDepartment,
    error,
    successMessage,
    currentPage,
    hasReachedMax,
    includeDeleted,
  ];
}
