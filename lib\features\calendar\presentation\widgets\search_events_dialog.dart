import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import 'event_card_widget.dart';

/// Dialog để tìm kiếm events
class SearchEventsDialog extends StatefulWidget {
  const SearchEventsDialog({super.key});

  @override
  State<SearchEventsDialog> createState() => _SearchEventsDialogState();
}

class _SearchEventsDialogState extends State<SearchEventsDialog> {
  final _searchController = TextEditingController();
  final _focusNode = FocusNode();
  List<CalendarEventEntity> _searchResults = [];
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _focusNode.requestFocus();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 500,
        ),
        child: Column(
          children: [
            _buildHeader(context, l10n),
            _buildSearchField(context, l10n),
            Expanded(child: _buildSearchResults(context, l10n)),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, dynamic l10n) {
    return Container(
      padding: context.responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withOpacity(0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: AppColors.primaryBlue,
            size: context.rf(24),
          ),
          SizedBox(width: context.rw(12)),
          Expanded(
            child: Text(
              l10n.searchEvents,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchField(BuildContext context, dynamic l10n) {
    return Container(
      padding: context.responsive.padding(all: 20),
      child: TextField(
        controller: _searchController,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: 'Search events by title, description, or location...',
          prefixIcon: const Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: AppColors.textSecondary),
                  onPressed: _clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(
              color: AppColors.textSecondary.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(
              color: AppColors.primaryBlue,
              width: 2,
            ),
          ),
        ),
        onChanged: _onSearchChanged,
        textInputAction: TextInputAction.search,
        onSubmitted: _performSearch,
      ),
    );
  }

  Widget _buildSearchResults(BuildContext context, dynamic l10n) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        if (_isSearching) {
          return _buildLoadingState(context, l10n);
        }

        if (_searchController.text.isEmpty) {
          return _buildInitialState(context, l10n);
        }

        if (state is CalendarEventsSearched) {
          return _buildResultsList(context, state.searchResults, l10n);
        }

        if (_searchResults.isEmpty && _searchController.text.isNotEmpty) {
          return _buildNoResultsState(context, l10n);
        }

        return _buildResultsList(context, _searchResults, l10n);
      },
    );
  }

  Widget _buildInitialState(BuildContext context, dynamic l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: context.rf(64),
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: context.rh(16)),
          Text(
            'Start typing to search events',
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
          ),
          SizedBox(height: context.rh(8)),
          Text(
            'Search by title, description, or location',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context, dynamic l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(color: AppColors.primary),
          SizedBox(height: context.rh(16)),
          Text(
            'Searching events...',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState(BuildContext context, dynamic l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: context.rf(64),
            color: AppColors.textSecondary.withOpacity(0.5),
          ),
          SizedBox(height: context.rh(16)),
          Text(
            l10n.noEventsFound,
            style: Theme.of(
              context,
            ).textTheme.bodyLarge?.copyWith(color: AppColors.textSecondary),
          ),
          SizedBox(height: context.rh(8)),
          Text(
            'Try different keywords or check your spelling',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsList(
    BuildContext context,
    List<CalendarEventEntity> results,
    dynamic l10n,
  ) {
    if (results.isEmpty) {
      return _buildNoResultsState(context, l10n);
    }

    return Column(
      children: [
        Container(
          padding: context.responsive.padding(horizontal: 20, vertical: 12),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withOpacity(0.05),
            border: Border(
              bottom: BorderSide(
                color: AppColors.textSecondary.withOpacity(0.1),
              ),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.event_note,
                color: AppColors.primaryBlue,
                size: context.rf(18),
              ),
              SizedBox(width: context.rw(8)),
              Text(
                '${results.length} event${results.length == 1 ? '' : 's'} found',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.separated(
            padding: context.responsive.padding(all: 16),
            itemCount: results.length,
            separatorBuilder: (context, index) =>
                SizedBox(height: context.rh(8)),
            itemBuilder: (context, index) {
              final event = results[index];
              return EventCardWidget(
                event: event,
                showDate: true,
                onTap: () {
                  Navigator.of(context).pop();
                  // Navigate to event details or select the date
                  context.read<CalendarCubit>().selectDate(DateTime(
                    event.startTime.year,
                    event.startTime.month,
                    event.startTime.day,
                  ));
                },
              );
            },
          ),
        ),
      ],
    );
  }

  void _onSearchChanged(String query) {
    setState(() {
      // Update UI immediately for better UX
    });

    // Debounce search to avoid too many API calls
    Future.delayed(const Duration(milliseconds: 300), () {
      if (_searchController.text == query && query.isNotEmpty) {
        _performSearch(query);
      }
    });
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    context.read<CalendarCubit>().searchEvents(query.trim());

    // Simulate search delay for better UX
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults = [];
      _isSearching = false;
    });
    context.read<CalendarCubit>().searchEvents('');
    _focusNode.requestFocus();
  }
}
