// Ví dụ: features/profile/widgets/change_password_bottom_sheet.dart

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:golderhr/shared/widgets/text_field_custom.dart';
// Thay đổi các đường dẫn này cho khớp với dự án của bạn

class ChangePasswordBottomSheet extends StatefulWidget {
  const ChangePasswordBottomSheet({super.key});

  @override
  State<ChangePasswordBottomSheet> createState() =>
      _ChangePasswordBottomSheetState();
}

class _ChangePasswordBottomSheetState extends State<ChangePasswordBottomSheet> {
  // Quản lý state của các <PERSON>Field ngay trong BottomSheet
  late final TextEditingController _currentPasswordController;
  late final TextEditingController _newPasswordController;
  late final TextEditingController _confirmPasswordController;

  @override
  void initState() {
    super.initState();
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // void _onConfirmChange() {
  //   // 1. Lấy giá trị từ các controller
  //   final currentPassword = _currentPasswordController.text;
  //   final newPassword = _newPasswordController.text;
  //   final confirmPassword = _confirmPasswordController.text;

  //   // 2. Validate dữ liệu (ví dụ đơn giản)
  //   if (newPassword.isEmpty || newPassword != confirmPassword) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text("Mật khẩu không hợp lệ"),
  //       ), // Thêm chuỗi này vào AppStrings
  //     );
  //     return;
  //   }

  //   // 4. Đóng BottomSheet
  //   Navigator.of(context).pop();

  //   // 5. Hiển thị thông báo thành công (có thể làm việc này trong BlocListener)
  //   ScaffoldMessenger.of(
  //     context,
  //   ).showSnackBar(const SnackBar(content: Text("AppStrings.passwordChanged")));
  // }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Bọc trong Padding để nội dung không bị che bởi bàn phím
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 20,
        right: 20,
        top: 20,
      ),
      // Dùng Wrap để nội dung tự co dãn chiều cao
      child: Wrap(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min, // Quan trọng
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // (Tùy chọn) Thêm một "handle"
              Center(
                child: Container(
                  width: 40,
                  height: 5,
                  decoration: BoxDecoration(
                    color: Colors.grey[400],
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
              ResponsiveSpacer(
                mobileSize: 24,
                tabletSize: 26,
                mobileLandscapeSize: 24,
                tabletLandscapeSize: 26,
              ),

              Center(
                child: Text(
                  context.l10n.changePassword,
                  style: theme.textTheme.headlineSmall,
                ),
              ),
              ResponsiveSpacer(
                mobileSize: 24,
                tabletSize: 26,
                mobileLandscapeSize: 24,
                tabletLandscapeSize: 26,
              ),

              TextFieldCustom(
                controller: _currentPasswordController,
                obscureText: true,
                hintText: context.l10n.settingCurrentPassword,
              ),
              ResponsiveSpacer(
                mobileSize: 16,
                tabletSize: 18,
                mobileLandscapeSize: 16,
                tabletLandscapeSize: 18,
              ),
              TextFieldCustom(
                controller: _newPasswordController,
                obscureText: true,
                hintText: context.l10n.settingNewPassword,
              ),
              ResponsiveSpacer(
                mobileSize: 16,
                tabletSize: 18,
                mobileLandscapeSize: 16,
                tabletLandscapeSize: 18,
              ),
              TextFieldCustom(
                controller: _newPasswordController,
                obscureText: true,
                hintText: context.l10n.settingConfirmPassword,
              ),
              ResponsiveSpacer(
                mobileSize: 24,
                tabletSize: 26,
                mobileLandscapeSize: 24,
                tabletLandscapeSize: 26,
              ),

              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: ButtonCustom(
                      text: context.l10n.settingCancel,
                      backgroundColor: Colors.red,
                      onPressed: () {
                        context.pop();
                      },
                    ),
                  ),

                  const SizedBox(width: 8),
                  Flexible(
                    child: ButtonCustom(
                      text: context.l10n.save,

                      onPressed: () {},
                    ),
                  ),
                ],
              ),
              ResponsiveSpacer(
                mobileSize: 24,
                tabletSize: 26,
                mobileLandscapeSize: 24,
                tabletLandscapeSize: 26,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
