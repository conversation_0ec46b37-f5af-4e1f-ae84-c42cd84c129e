import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/role_cubit.dart';

class CreateRoleDialog extends StatefulWidget {
  const CreateRoleDialog({super.key});

  @override
  State<CreateRoleDialog> createState() => _CreateRoleDialogState();
}

class _CreateRoleDialogState extends State<CreateRoleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: responsive.widthPercentage(90),
          tablet: responsive.widthPercentage(70),
          mobileLandscape: responsive.widthPercentage(80),
          tabletLandscape: responsive.widthPercentage(60),
        ),
        constraints: BoxConstraints(
          maxWidth: responsive.adaptiveValue<double>(
            mobile: 400,
            tablet: 500,
            mobileLandscape: 450,
            tabletLandscape: 550,
          ),
          maxHeight: responsive.heightPercentage(70),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: responsive.adaptiveValue<double>(
                      mobile: 18,
                      tablet: 20,
                      mobileLandscape: 19,
                      tabletLandscape: 22,
                    ),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Icon(
                      Iconsax.security_user,
                      color: Colors.white,
                      size: responsive.adaptiveValue<double>(
                        mobile: 18,
                        tablet: 20,
                        mobileLandscape: 19,
                        tabletLandscape: 22,
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Create New Role',
                          style: AppTextStyle.bold(
                            context,
                            size: 18,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'Add a new role to the system',
                          style: AppTextStyle.regular(
                            context,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: BlocConsumer<RoleCubit, RoleState>(
                listener: (context, state) {
                  if (!state.isProcessing &&
                      !state.hasError &&
                      state.hasSuccess) {
                    Navigator.pop(context);
                  }
                },
                builder: (context, state) {
                  if (state.isProcessing) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  return SingleChildScrollView(
                    padding: responsive.responsivePadding,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Role Information Section
                          _buildSectionHeader(
                            'Role Information',
                            Iconsax.security_user,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _nameController,
                            label: 'Role Name *',
                            icon: Iconsax.security_user,
                            validator: (value) =>
                                _validateRoleName(value, context),
                            hint:
                                'Enter role name (e.g., Manager, HR, Developer)',
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          // Info card
                          Container(
                            padding: responsive.padding(all: 12),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.info_circle,
                                  color: Colors.blue.shade600,
                                  size: responsive.scaleRadius(16),
                                ),
                                SizedBox(width: responsive.scaleWidth(8)),
                                Expanded(
                                  child: Text(
                                    'Role names should be unique and descriptive. They will be used throughout the system for access control.',
                                    style: AppTextStyle.regular(
                                      context,
                                      size: 12,
                                      color: Colors.blue.shade700,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Action Buttons
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.cancel,
                        style: AppTextStyle.medium(
                          context,
                          size: responsive.adaptiveValue<double>(
                            mobile: 14,
                            tablet: 16,
                            mobileLandscape: 15,
                            tabletLandscape: 17,
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: BlocBuilder<RoleCubit, RoleState>(
                      builder: (context, state) {
                        return ElevatedButton(
                          onPressed: state.isProcessing ? null : _createRole,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.primaryColor,
                            foregroundColor: Colors.white,
                            padding: responsive.padding(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                            ),
                            elevation: 2,
                          ),
                          child: state.isProcessing
                              ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                              : Text(
                                  'Create Role',
                                  style: AppTextStyle.medium(
                                    context,
                                    size: responsive.adaptiveValue<double>(
                                      mobile: 14,
                                      tablet: 16,
                                      mobileLandscape: 15,
                                      tabletLandscape: 17,
                                    ),
                                    color: Colors.white,
                                  ),
                                ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final responsive = context.responsive;

    return Row(
      children: [
        Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 18,
            tablet: 20,
            mobileLandscape: 19,
            tabletLandscape: 22,
          ),
          color: Colors.grey[700],
        ),
        SizedBox(width: responsive.scaleWidth(8)),
        Text(
          title,
          style: AppTextStyle.bold(context, size: 16, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    String? hint,
  }) {
    final responsive = context.responsive;

    return TextFormField(
      controller: controller,
      validator: validator,
      style: AppTextStyle.regular(context, size: 14),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: AppTextStyle.regular(context, size: 12),
        hintStyle: AppTextStyle.regular(
          context,
          size: 12,
          color: Colors.grey[500],
        ),
        prefixIcon: Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 20,
            tablet: 22,
            mobileLandscape: 21,
            tabletLandscape: 24,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: responsive.padding(horizontal: 16, vertical: 12),
      ),
    );
  }

  String? _validateRoleName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Role name is required';
    }
    if (value.trim().length < 2) {
      return 'Role name must be at least 2 characters';
    }
    if (value.trim().length > 50) {
      return 'Role name must be less than 50 characters';
    }
    // Check for valid characters (letters, numbers, spaces, hyphens, underscores)
    if (!RegExp(r'^[a-zA-Z0-9\s\-_]+$').hasMatch(value.trim())) {
      return 'Role name contains invalid characters';
    }
    return null;
  }

  void _createRole() {
    if (_formKey.currentState!.validate()) {
      context.read<RoleCubit>().createNewRole(
        name: _nameController.text.trim(),
      );
    }
  }
}
