import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../theme/app_colors.dart';

class CustomTimePicker extends StatefulWidget {
  final TimeOfDay? initialTime;
  final String title;
  final Function(TimeOfDay) onTimeSelected;

  const CustomTimePicker({
    super.key,
    this.initialTime,
    required this.title,
    required this.onTimeSelected,
  });

  @override
  State<CustomTimePicker> createState() => _CustomTimePickerState();
}

class _CustomTimePickerState extends State<CustomTimePicker> {
  late int selectedHour;
  late int selectedMinute;
  late bool isAM;

  @override
  void initState() {
    super.initState();
    final time = widget.initialTime ?? TimeOfDay.now();
    selectedHour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    selectedMinute = time.minute;
    isAM = time.period == DayPeriod.am;
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: responsive.padding(all: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.access_time_rounded,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                SizedBox(width: responsive.widthPercentage(3)),
                Expanded(
                  child: Text(
                    widget.title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: responsive.heightPercentage(3)),

            // Time Display
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Hour
                  _buildTimeUnit(
                    value: selectedHour,
                    onIncrement: () => _incrementHour(),
                    onDecrement: () => _decrementHour(),
                    responsive: responsive,
                    theme: theme,
                  ),

                  // Separator
                  Padding(
                    padding: responsive.padding(horizontal: 16),
                    child: Text(
                      ':',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),

                  // Minute
                  _buildTimeUnit(
                    value: selectedMinute,
                    onIncrement: () => _incrementMinute(),
                    onDecrement: () => _decrementMinute(),
                    responsive: responsive,
                    theme: theme,
                    showLeadingZero: true,
                  ),

                  SizedBox(width: responsive.widthPercentage(4)),

                  // AM/PM Toggle
                  _buildAmPmToggle(responsive, theme),
                ],
              ),
            ),

            SizedBox(height: responsive.heightPercentage(3.0)),

            // Quick Time Buttons
            _buildQuickTimeButtons(responsive, theme),

            SizedBox(height: responsive.heightPercentage(3.0)),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: responsive.padding(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: AppColors.textSecondary.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                    child: Text(
                      'Cancel',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: responsive.widthPercentage(3)),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmTime,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      padding: responsive.padding(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      'Select',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeUnit({
    required int value,
    required VoidCallback onIncrement,
    required VoidCallback onDecrement,
    required dynamic responsive,
    required ThemeData theme,
    bool showLeadingZero = false,
  }) {
    return Column(
      children: [
        // Increment button
        GestureDetector(
          onTap: onIncrement,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.keyboard_arrow_up_rounded,
              color: AppColors.primaryBlue,
              size: 20,
            ),
          ),
        ),

        SizedBox(height: responsive.heightPercentage(1.0)),

        // Value display
        Container(
          width: 60,
          padding: responsive.padding(vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            showLeadingZero
                ? value.toString().padLeft(2, '0')
                : value.toString(),
            textAlign: TextAlign.center,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ),

        SizedBox(height: responsive.heightPercentage(1.0)),

        // Decrement button
        GestureDetector(
          onTap: onDecrement,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.keyboard_arrow_down_rounded,
              color: AppColors.primaryBlue,
              size: 20,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAmPmToggle(dynamic responsive, ThemeData theme) {
    return Column(
      children: [
        _buildAmPmButton(
          'AM',
          isAM,
          () => setState(() => isAM = true),
          responsive,
          theme,
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        _buildAmPmButton(
          'PM',
          !isAM,
          () => setState(() => isAM = false),
          responsive,
          theme,
        ),
      ],
    );
  }

  Widget _buildAmPmButton(
    String text,
    bool isSelected,
    VoidCallback onTap,
    dynamic responsive,
    ThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        padding: responsive.padding(vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: theme.textTheme.titleSmall?.copyWith(
            color: isSelected ? Colors.white : AppColors.primaryBlue,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickTimeButtons(dynamic responsive, ThemeData theme) {
    final quickTimes = [
      {'label': '9:00 AM', 'hour': 9, 'minute': 0},
      {'label': '12:00 PM', 'hour': 12, 'minute': 0},
      {'label': '1:00 PM', 'hour': 13, 'minute': 0},
      {'label': '5:00 PM', 'hour': 17, 'minute': 0},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Select',
          style: theme.textTheme.titleSmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        Wrap(
          spacing: 8,
          children: quickTimes.map((time) {
            return GestureDetector(
              onTap: () =>
                  _setQuickTime(time['hour'] as int, time['minute'] as int),
              child: Container(
                padding: responsive.padding(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.primaryBlue.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  time['label'] as String,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _incrementHour() {
    setState(() {
      selectedHour = selectedHour == 12 ? 1 : selectedHour + 1;
    });
  }

  void _decrementHour() {
    setState(() {
      selectedHour = selectedHour == 1 ? 12 : selectedHour - 1;
    });
  }

  void _incrementMinute() {
    setState(() {
      selectedMinute = (selectedMinute + 15) % 60;
    });
  }

  void _decrementMinute() {
    setState(() {
      selectedMinute = selectedMinute == 0 ? 45 : selectedMinute - 15;
    });
  }

  void _setQuickTime(int hour, int minute) {
    setState(() {
      selectedHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
      selectedMinute = minute;
      isAM = hour < 12;
    });
  }

  void _confirmTime() {
    int hour24 = selectedHour;
    if (isAM && selectedHour == 12) {
      hour24 = 0;
    } else if (!isAM && selectedHour != 12) {
      hour24 = selectedHour + 12;
    }

    final timeOfDay = TimeOfDay(hour: hour24, minute: selectedMinute);
    widget.onTimeSelected(timeOfDay);
    Navigator.of(context).pop();
  }
}
