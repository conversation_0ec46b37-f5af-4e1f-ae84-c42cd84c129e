import 'package:equatable/equatable.dart';
import '../../domain/entities/leave_balance.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/entities/leave_policy.dart';

abstract class LeaveState extends Equatable {
  const LeaveState();

  @override
  List<Object?> get props => [];
}

class LeaveInitial extends LeaveState {}

class LeaveLoading extends LeaveState {}
class LeaveLoaded extends LeaveState {
  final LeaveBalance balance;
  final List<LeaveRequest> recentRequests;
  final List<LeavePolicy> policies;
  final bool isSubmitting;
  final String? errorMessage; // Thêm field để lưu lỗi

  const LeaveLoaded({
    required this.balance,
    required this.recentRequests,
    required this.policies,
    this.isSubmitting = false,
    this.errorMessage, // Thêm parameter
  });

  // HÀM NÀY RẤT QUAN TRỌNG
  LeaveLoaded copyWith({
    LeaveBalance? balance,
    List<LeaveRequest>? recentRequests,
    List<LeavePolicy>? policies,
    bool? isSubmitting,
    String? errorMessage, // Thêm parameter
  }) {
    return LeaveLoaded(
      balance: balance ?? this.balance,
      recentRequests: recentRequests ?? this.recentRequests,
      policies: policies ?? this.policies,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      errorMessage: errorMessage, // Thêm field
    );
  }

  @override
  // Cập nhật props để Equatable hoạt động đúng
  List<Object?> get props => [balance, recentRequests, policies, isSubmitting, errorMessage];
}

class LeaveError extends LeaveState {
  final String message;

  const LeaveError(this.message);

  @override
  List<Object?> get props => [message];
}

class LeaveRequestSubmitting extends LeaveState {}

class LeaveRequestSubmitted extends LeaveState {
  final String message;

  const LeaveRequestSubmitted(this.message);

  @override
  List<Object?> get props => [message];
}

class LeaveRequestError extends LeaveState {
  final String message;

  const LeaveRequestError(this.message);

  @override
  List<Object?> get props => [message];
}
