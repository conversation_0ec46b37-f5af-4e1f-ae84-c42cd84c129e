import 'package:flutter/material.dart';
import '../../domain/entities/calendar_event.dart';

class EditEventDialog extends StatelessWidget {
  final CalendarEvent event;
  const EditEventDialog({super.key, required this.event});
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Edit Event (stub)'),
      content: Text('This is a stub for EditEventDialog: \\${event.title}'),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
} 