import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/leave_policy.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class GetLeavePolicies implements UseCase<List<LeavePolicy>, NoParams> {
  final LeaveRepository repository;

  GetLeavePolicies(this.repository);

  @override
  Future<Either<Failure, List<LeavePolicy>>> call(NoParams params) async {
    return await repository.getLeavePolicies();
  }
} 