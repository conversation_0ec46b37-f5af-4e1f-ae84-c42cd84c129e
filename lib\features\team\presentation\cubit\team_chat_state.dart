part of 'team_chat_cubit.dart';

abstract class TeamChatState extends Equatable {
  const TeamChatState();

  @override
  List<Object?> get props => [];
}

class TeamChatInitial extends TeamChatState {}

class TeamChatLoading extends TeamChatState {}

class TeamChatLoaded extends Team<PERSON>hatState {
  final List<TeamChatEntity> messages;
  final bool hasMore;
  final Map<String, bool> typingUsers;

  const TeamChatLoaded({
    required this.messages,
    required this.hasMore,
    required this.typingUsers,
  });

  @override
  List<Object> get props => [messages, hasMore, typingUsers];

  TeamChatLoaded copyWith({
    List<TeamChatEntity>? messages,
    bool? hasMore,
    Map<String, bool>? typingUsers,
  }) {
    return TeamChatLoaded(
      messages: messages ?? this.messages,
      hasMore: hasMore ?? this.hasMore,
      typingUsers: typingUsers ?? this.typingUsers,
    );
  }
}

class TeamChatError extends TeamChatState {
  final String message;

  const TeamChatError(this.message);

  @override
  List<Object> get props => [message];
}
