import 'dart:convert';

import 'package:golderhr/features/faceDetection/domain/entities/location_entity.dart';

class LocationModel extends LocationEntity {
  const LocationModel({required super.coordinates, required super.address});

  factory LocationModel.fromEntity(LocationEntity location) {
    return LocationModel(
      coordinates: location.coordinates,
      address: location.address,
    );
  }

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      address: json['address'],
      coordinates: List<double>.from(
        json['coordinates']['coordinates'].map((e) => e.toDouble()),
      ),
    );
  }

  String toJsonString() =>
      json.encode({'coordinates': coordinates, 'address': address});
}
