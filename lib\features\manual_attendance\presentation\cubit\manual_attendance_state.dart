import 'package:equatable/equatable.dart';
import '../../domain/entities/manual_attendance_entity.dart';

abstract class ManualAttendanceState extends Equatable {
  const ManualAttendanceState();

  @override
  List<Object?> get props => [];
}

class ManualAttendanceInitial extends ManualAttendanceState {}

class ManualAttendanceLoading extends ManualAttendanceState {}

class ManualAttendanceLoaded extends ManualAttendanceState {
  final List<ManualAttendanceEntity> requests;
  final ManualAttendancePagination? pagination;

  const ManualAttendanceLoaded({
    required this.requests,
    this.pagination,
  });

  @override
  List<Object?> get props => [requests, pagination];
}

class ManualAttendanceError extends ManualAttendanceState {
  final String message;

  const ManualAttendanceError(this.message);

  @override
  List<Object> get props => [message];
}

class ManualAttendanceReviewSuccess extends ManualAttendanceState {
  final ManualAttendanceEntity updatedRequest;

  const ManualAttendanceReviewSuccess(this.updatedRequest);

  @override
  List<Object> get props => [updatedRequest];
}
