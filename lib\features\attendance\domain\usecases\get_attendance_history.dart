// lib/features/attendance/domain/usecases/get_attendance_history.dart
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:golderhr/features/attendance/domain/entities/attendance_history.dart';
import 'package:golderhr/features/attendance/domain/repositories/attendance_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetAttendanceHistory
    implements UseCase<PaginatedAttendanceHistory, HistoryParams> {
  final AttendanceRepositoryV1 repository;

  GetAttendanceHistory(this.repository);

  @override
  Future<Either<Failure, PaginatedAttendanceHistory>> call(
    HistoryParams params,
  ) async {
    return await repository.getAttendanceHistory(
      page: params.page,
      limit: params.limit,
    );
  }
}

class HistoryParams extends Equatable {
  final int page;
  final int limit;

  const HistoryParams({required this.page, required this.limit});

  @override
  List<Object?> get props => [page, limit];
}
