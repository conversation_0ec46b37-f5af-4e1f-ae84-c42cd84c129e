import 'package:bloc/bloc.dart';

import '../../data/model/notification_model.dart';
import '../../domain/usecases/delete_notification.dart';
import '../../domain/usecases/get_notifications.dart';
import '../../domain/usecases/get_unread_count.dart';
import '../../domain/usecases/mark_all_as_read.dart';
import '../../domain/usecases/mark_notification_as_read.dart';
import 'notification_state.dart';

class NotificationCubit extends Cubit<NotificationState> {
  final GetNotificationsUseCase getNotificationsUseCase;
  final MarkNotificationAsReadUseCase markNotificationAsReadUseCase;
  final GetUnreadCountUseCase getUnreadCountUseCase;
  final MarkAllAsReadUseCase markAllAsReadUseCase;
  final DeleteNotificationUseCase deleteNotificationUseCase;

  NotificationCubit({
    required this.getNotificationsUseCase,
    required this.markNotificationAsReadUseCase,
    required this.getUnreadCountUseCase,
    required this.markAllAsReadUseCase,
    required this.deleteNotificationUseCase,
  }) : super(NotificationInitial());

  void _processAndEmitState(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      emit(NotificationEmpty());
      return;
    }


    final important = notifications.where((n) => n.isImportant).toList();
    final customer = notifications
        .where((n) => n.category == NotificationCategory.customer)
        .toList();
    final unread = notifications.where((n) => !n.isRead).toList();

    // Sắp xếp các danh sách theo thời gian gần nhất để hiển thị đúng thứ tự
    important.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    customer.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    unread.sort((a, b) => b.timestamp.compareTo(a.timestamp));

    // Nhóm cho tab 'Tất cả'
    final grouped = _groupNotifications(notifications);
    final allAsMap = {for (var e in notifications) e.id: e};

    // Phát ra trạng thái loaded với đầy đủ dữ liệu đã xử lý
    emit(
      NotificationLoaded(
        allNotifications: allAsMap,
        groupedNotifications: grouped,
        importantNotifications: important,
        customerNotifications: customer,
        unreadNotifications: unread,
      ),
    );
  }

  // Hàm nhóm thông báo theo ngày cho tab 'Tất cả'
  Map<String, List<NotificationModel>> _groupNotifications(
    List<NotificationModel> notifications,
  ) {
    final Map<String, List<NotificationModel>> grouped = {};
    final now = DateTime.now();

    for (var notif in notifications) {
      final difference = now.difference(notif.timestamp).inDays;
      String key;
      // Cải thiện logic để xử lý trường hợp nửa đêm
      if (difference == 0 && now.day == notif.timestamp.day) {
        key = 'Hôm nay';
      } else if (difference == 1 ||
          (difference == 0 && now.day != notif.timestamp.day)) {
        key = 'Hôm qua';
      } else if (difference < 7) {
        key = 'Tuần này';
      } else {
        key = 'Cũ hơn';
      }

      grouped.putIfAbsent(key, () => []).add(notif);
    }

    grouped.forEach((key, value) {
      value.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    });

    return grouped;
  }

  Future<void> fetchNotifications({
    int page = 1,
    int limit = 20,
    String? type,
    bool? isRead,
    String? priority,
  }) async {
    try {
      emit(NotificationLoading());
      final result = await getNotificationsUseCase(
        page: page,
        limit: limit,
        type: type,
        isRead: isRead,
        priority: priority,
      );

      result.fold(
        (failure) => emit(NotificationError(failure.message)),
        (notifications) =>
            _processAndEmitState(notifications.cast<NotificationModel>()),
      );
    } catch (e) {
      emit(NotificationError('Không thể tải thông báo. Vui lòng thử lại.'));
    }
  }

  // Hàm để UI gọi khi người dùng nhấn vào một thông báo
  Future<void> markAsRead(String notificationId) async {
    try {
      // Gọi API để mark as read
      final result = await markNotificationAsReadUseCase(notificationId);

      result.fold(
        (failure) {
          // Có thể hiển thị error message nếu cần
          print('Error marking notification as read: ${failure.message}');
        },
        (_) {
          // Cập nhật local state nếu thành công
          if (state is NotificationLoaded) {
            final currentState = state as NotificationLoaded;
            final updatedNotification = currentState
                .allNotifications[notificationId]
                ?.copyWith(isRead: true);

            if (updatedNotification != null) {
              final newAllNotifications = Map<String, NotificationModel>.from(
                currentState.allNotifications,
              );
              newAllNotifications[notificationId] = updatedNotification;
              _processAndEmitState(newAllNotifications.values.toList());
            }
          }
        },
      );
    } catch (e) {
      print('Error marking notification as read: $e');
    }
  }

  // Hàm để mark tất cả notifications là đã đọc
  Future<void> markAllAsRead() async {
    try {
      final result = await markAllAsReadUseCase();

      result.fold(
        (failure) {
          print('Error marking all notifications as read: ${failure.message}');
        },
        (_) {
          // Refresh notifications sau khi mark all as read
          fetchNotifications();
        },
      );
    } catch (e) {
      print('Error marking all notifications as read: $e');
    }
  }

  Future<void> deleteNotification(String notificationId) async {
    try {
      final result = await deleteNotificationUseCase(notificationId);
      result.fold(
        (failure) {
          print('Error deleting notification: ${failure.message}');
        },
        (_) {
          // Refresh notifications sau khi xóa
          fetchNotifications();
        },
      );
    } catch (e) {
      print('Error deleting notification: $e');
    }
  }
}
