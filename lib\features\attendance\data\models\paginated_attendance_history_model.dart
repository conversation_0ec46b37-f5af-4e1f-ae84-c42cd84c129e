// lib/features/attendance/data/models/paginated_attendance_history_model.dart

// Import entity từ Domain và model con từ Data
import '../../domain/entities/attendance_history.dart';

// --- Model con cho AttendanceHistoryItem ---
// (<PERSON><PERSON><PERSON> có thể đặt nó trong một file riêng hoặc cùng file này)
class AttendanceHistoryItemModel extends AttendanceHistoryItem {
  const AttendanceHistoryItemModel({
    required super.id,
    required super.date,
    required super.checkIn,
    required super.checkOut,
    required super.totalHours,
  });

  factory AttendanceHistoryItemModel.fromJson(Map<String, dynamic> json) {
    return AttendanceHistoryItemModel(
      id: json['id'] as String,
      date: json['date'] as String,
      checkIn: json['checkIn'] as String,
      checkOut: json['checkOut'] as String,
      totalHours: json['totalHours'] as String,
    );
  }
}

// --- Model chính cho dữ liệu phân trang ---
// QUAN TRỌNG: extends PaginatedAttendanceHistory từ tầng Domain
class PaginatedAttendanceHistoryModel extends PaginatedAttendanceHistory {
  const PaginatedAttendanceHistoryModel({
    required super.history,
    required super.currentPage,
    required super.totalPages,
  });

  factory PaginatedAttendanceHistoryModel.fromJson(Map<String, dynamic> json) {
    // Parse danh sách các item con
    var historyList = (json['history'] as List)
        .map((item) => AttendanceHistoryItemModel.fromJson(item))
        .toList();

    return PaginatedAttendanceHistoryModel(
      history: historyList,
      currentPage: json['currentPage'] as int,
      totalPages: json['totalPages'] as int,
    );
  }
}
