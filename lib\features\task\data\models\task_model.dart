import '../../domain/entities/task_entity.dart';

class TaskModel extends TaskEntity {
  const TaskModel({
    required super.id,
    required super.title,
    super.description,
    required super.assignedTo,
    required super.createdBy,
    super.teamId,
    required super.status,
    required super.priority,
    super.dueDate,
    super.attachments,
    super.comments,
    super.tags,
    super.estimatedHours,
    super.actualHours,
    super.completedAt,
    super.createdAt,
    super.updatedAt,
  });

  factory TaskModel.fromJson(Map<String, dynamic> json) {
    return TaskModel(
      id: json['_id'] ?? json['id'],
      title: json['title'],
      description: json['description'],
      assignedTo: json['assignedTo'] is Map 
          ? UserModel.fromJson(json['assignedTo'])
          : json['assignedTo'],
      createdBy: json['createdBy'] is Map
          ? UserModel.fromJson(json['createdBy'])
          : json['createdBy'],
      teamId: json['teamId'] is Map
          ? json['teamId']['_id'] ?? json['teamId']['id']
          : json['teamId'],
      status: TaskStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TaskStatus.todo,
      ),
      priority: TaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => TaskPriority.medium,
      ),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      attachments: (json['attachments'] as List?)
          ?.map((e) => TaskAttachmentModel.fromJson(e))
          .toList() ?? [],
      comments: (json['comments'] as List?)
          ?.map((e) => TaskCommentModel.fromJson(e))
          .toList() ?? [],
      tags: List<String>.from(json['tags'] ?? []),
      estimatedHours: json['estimatedHours']?.toDouble(),
      actualHours: json['actualHours']?.toDouble(),
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'assignedTo': assignedTo is UserEntity 
          ? (assignedTo as UserEntity).id
          : assignedTo,
      'createdBy': createdBy is UserEntity
          ? (createdBy as UserEntity).id
          : createdBy,
      'teamId': teamId,
      'status': status.name,
      'priority': priority.name,
      'dueDate': dueDate?.toIso8601String(),
      'attachments': attachments?.map((e) => (e as TaskAttachmentModel).toJson()).toList(),
      'comments': comments?.map((e) => (e as TaskCommentModel).toJson()).toList(),
      'tags': tags,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'completedAt': completedAt?.toIso8601String(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  TaskModel copyWith({
    String? id,
    String? title,
    String? description,
    dynamic assignedTo,
    dynamic createdBy,
    String? teamId,
    TaskStatus? status,
    TaskPriority? priority,
    DateTime? dueDate,
    List<TaskAttachmentEntity>? attachments,
    List<TaskCommentEntity>? comments,
    List<String>? tags,
    double? estimatedHours,
    double? actualHours,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TaskModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      assignedTo: assignedTo ?? this.assignedTo,
      createdBy: createdBy ?? this.createdBy,
      teamId: teamId ?? this.teamId,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      dueDate: dueDate ?? this.dueDate,
      attachments: attachments ?? this.attachments,
      comments: comments ?? this.comments,
      tags: tags ?? this.tags,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class TaskAttachmentModel extends TaskAttachmentEntity {
  const TaskAttachmentModel({
    required super.fileName,
    required super.fileUrl,
    required super.fileType,
    required super.uploadedAt,
  });

  factory TaskAttachmentModel.fromJson(Map<String, dynamic> json) {
    return TaskAttachmentModel(
      fileName: json['fileName'],
      fileUrl: json['fileUrl'],
      fileType: json['fileType'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }
}

class TaskCommentModel extends TaskCommentEntity {
  const TaskCommentModel({
    required super.userId,
    required super.message,
    required super.createdAt,
  });

  factory TaskCommentModel.fromJson(Map<String, dynamic> json) {
    return TaskCommentModel(
      userId: json['userId'],
      message: json['message'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'message': message,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class UserModel extends UserEntity {
  const UserModel({
    required super.id,
    required super.fullname,
    super.avatar,
    super.email,
    super.department,
    super.position,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['_id'] ?? json['id'],
      fullname: json['fullname'],
      avatar: json['avatar'],
      email: json['email'],
      department: json['department'],
      position: json['position'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'avatar': avatar,
      'email': email,
      'department': department,
      'position': position,
    };
  }
}
