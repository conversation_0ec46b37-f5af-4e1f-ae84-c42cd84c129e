import 'package:dio/dio.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import '../models/manual_attendance_model.dart';

abstract class ManualAttendanceRemoteDataSource {
  Future<List<ManualAttendanceModel>> getManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  Future<ManualAttendanceModel> reviewManualAttendance({
    required String id,
    required ManualAttendanceStatus status,
    String? adminNote,
  });

  Future<List<ManualAttendanceModel>> getMyManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
  });
}

class ManualAttendanceRemoteDataSourceImpl implements ManualAttendanceRemoteDataSource {
  final DioClient dioClient;

  ManualAttendanceRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<ManualAttendanceModel>> getManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) {
        queryParams['status'] = status.value;
      }
      if (userId != null) {
        queryParams['userId'] = userId;
      }
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
      }

      final response = await dioClient.get(
        '/api/attendance/manual-attendance/admin/requests',
        queryParameters: queryParams,
      );

      final List<dynamic> requestsData = response.data['data']['requests'];
      return requestsData.map((json) => ManualAttendanceModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'Failed to fetch manual attendance requests',
      );
    } catch (e) {
      throw ServerException('An unexpected error occurred: $e');
    }
  }

  @override
  Future<ManualAttendanceModel> reviewManualAttendance({
    required String id,
    required ManualAttendanceStatus status,
    String? adminNote,
  }) async {
    try {
      final response = await dioClient.put(
        '/api/attendance/manual-attendance/admin/review/$id',
        data: {
          'status': status.value,
          if (adminNote != null) 'adminNote': adminNote,
        },
      );

      return ManualAttendanceModel.fromJson(response.data['data']['manualAttendance']);
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'Failed to review manual attendance',
      );
    } catch (e) {
      throw ServerException('An unexpected error occurred: $e');
    }
  }

  @override
  Future<List<ManualAttendanceModel>> getMyManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (status != null) {
        queryParams['status'] = status.value;
      }

      final response = await dioClient.get(
        '/api/attendance/manual-attendance/my-requests',
        queryParameters: queryParams,
      );

      final List<dynamic> requestsData = response.data['data']['requests'];
      return requestsData.map((json) => ManualAttendanceModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'Failed to fetch my manual attendance requests',
      );
    } catch (e) {
      throw ServerException('An unexpected error occurred: $e');
    }
  }
}
