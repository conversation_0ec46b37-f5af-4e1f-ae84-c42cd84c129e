import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/calendar_repository.dart';

// Dùng String làm Params
class DeleteCalendarEvent implements UseCase<void, String> {
  final CalendarRepository repository;

  DeleteCalendarEvent(this.repository);

  @override
  Future<Either<Failure, void>> call(String eventId) async {
    return await repository.deleteCalendarEvent(eventId);
  }
}