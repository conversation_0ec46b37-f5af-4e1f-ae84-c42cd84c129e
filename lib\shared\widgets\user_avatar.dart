import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/features/cubit/user_cubit.dart';
import 'package:golderhr/shared/widgets/cached_avatar.dart';

/// Widget để hiển thị avatar của user hiện tại với cache
/// Tự động lấy thông tin user từ UserCubit
class UserAvatar extends StatelessWidget {
  final double size;
  final double borderWidth;
  final Color borderColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final Widget? editIcon;
  final bool showOnlineIndicator;

  const UserAvatar({
    super.key,
    this.size = 40,
    this.borderWidth = 0,
    this.borderColor = Colors.white,
    this.backgroundColor,
    this.onTap,
    this.editIcon,
    this.showOnlineIndicator = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserCubit, UserEntity?>(
      builder: (context, user) {
        Widget avatar = CachedAvatar(
          imageUrl: user?.avatar,
          size: size,
          borderWidth: borderWidth,
          borderColor: borderColor,
          backgroundColor: backgroundColor,
          onTap: onTap,
          editIcon: editIcon,
        );

        // Thêm online indicator nếu cần
        if (showOnlineIndicator) {
          avatar = Stack(
            children: [
              avatar,
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  width: size * 0.25,
                  height: size * 0.25,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                ),
              ),
            ],
          );
        }

        return avatar;
      },
    );
  }
}

/// Predefined UserAvatar sizes for consistency
class UserAvatarSize {
  static const double small = 32;
  static const double medium = 40;
  static const double large = 56;
  static const double extraLarge = 80;
  static const double profile = 120;
}

/// UserAvatar variants for common use cases
class UserAvatarVariants {
  /// Small avatar for lists and compact spaces
  static Widget small({VoidCallback? onTap}) =>
      UserAvatar(size: UserAvatarSize.small, onTap: onTap);

  /// Medium avatar for app bars and navigation
  static Widget medium({VoidCallback? onTap}) =>
      UserAvatar(size: UserAvatarSize.medium, onTap: onTap);

  /// Large avatar for profile headers
  static Widget large({
    VoidCallback? onTap,
    Widget? editIcon,
    bool showOnlineIndicator = false,
  }) => UserAvatar(
    size: UserAvatarSize.large,
    borderWidth: 2,
    onTap: onTap,
    editIcon: editIcon,
    showOnlineIndicator: showOnlineIndicator,
  );

  /// Extra large avatar for profile pages
  static Widget extraLarge({VoidCallback? onTap, Widget? editIcon}) =>
      UserAvatar(
        size: UserAvatarSize.extraLarge,
        borderWidth: 3,
        onTap: onTap,
        editIcon: editIcon,
      );

  /// Profile avatar for profile pages
  static Widget profile({VoidCallback? onTap, Widget? editIcon}) => UserAvatar(
    size: UserAvatarSize.profile,
    borderWidth: 4,
    onTap: onTap,
    editIcon: editIcon,
  );
}
