import 'package:flutter/cupertino.dart';

class CreateDialogValidator{
  static String? validateDepartmentName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Department name is required';
    }
    if (value.trim().length < 2) {
      return 'Department name must be at least 2 characters';
    }
    if (value.trim().length > 100) {
      return 'Department name must be less than 100 characters';
    }
    return null;
  }

  static String? validateDepartmentCode(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length < 2) {
        return 'Department code must be at least 2 characters';
      }
      if (value.trim().length > 10) {
        return 'Department code must be less than 10 characters';
      }
      // Check for valid characters (letters, numbers, hyphens, underscores)
      if (!RegExp(r'^[a-zA-Z0-9\-_]+$').hasMatch(value.trim())) {
        return 'Department code contains invalid characters';
      }
    }
    return null;
  }
}