// new_password_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/gradient_background.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';

import '../../../../core/routes/app_routes.dart';
import '../cubit/auth_cubit.dart';
import '../cubit/auth_state.dart';
import '../widgets/arrow_appbar.dart';
import '../widgets/auth_card.dart';
import '../widgets/auth_welcome_section.dart';

class NewPasswordPage extends StatefulWidget {
  const NewPasswordPage({super.key});

  @override
  State<NewPasswordPage> createState() => _NewPasswordPageState();
}

class _NewPasswordPageState extends State<NewPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      context.read<AuthCubit>().resetPassword(_passwordController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is PasswordResetSuccess) {
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: context.l10n.passwordResetSuccessfully,
          );
          context.push(AppRoutes.login);
        } else if (state is AuthError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.message,
            isError: true,
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        extendBodyBehindAppBar: true,
        body: GradientBackground(
          child: SafeArea(
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              physics: const ClampingScrollPhysics(),
              child: SizedBox(
                height:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top,
                child: Padding(
                  padding: responsive.padding(horizontal: 24, vertical: 16),
                  child: Column(
                    children: [
                      const ArrowAppBar(),
                      SizedBox(height: responsive.heightPercentage(8)),
                      AuthWelcomeSection(
                        title: context.l10n.authTitleResetPassword,
                        subtitle: context.l10n.authSubTitleResetPassword,
                        showLogo: false,
                      ),
                      SizedBox(height: responsive.heightPercentage(4)),
                      AuthCard(
                        child: Form(
                          key: _formKey,
                          child: Column(
                            children: [
                              TextFormField(
                                controller: _passwordController,
                                decoration: InputDecoration(
                                  labelText: context.l10n.settingNewPassword,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                obscureText: true,
                                keyboardType: TextInputType.visiblePassword,
                                autovalidateMode:
                                    AutovalidateMode.onUserInteraction,

                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return context
                                        .l10n
                                        .loginPleaseEnterPassword;
                                  }
                                  if (value.length < 8) {
                                    return context
                                        .l10n
                                        .registerPasswordsDoNotMatch;
                                  }
                                  return null;
                                },
                              ),
                              SizedBox(height: responsive.heightPercentage(2)),
                              TextFormField(
                                controller: _confirmPasswordController,
                                decoration: InputDecoration(
                                  labelText:
                                      context.l10n.settingConfirmPassword,
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                obscureText: true,
                                keyboardType: TextInputType.visiblePassword,
                                autovalidateMode:
                                    AutovalidateMode.onUserInteraction,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return context
                                        .l10n
                                        .pleaseEnterConfirmPassword;
                                  }
                                  if (value != _passwordController.text) {
                                    return context
                                        .l10n
                                        .registerPasswordsDoNotMatch;
                                  }
                                  return null;
                                },
                              ),
                              SizedBox(height: responsive.heightPercentage(3)),
                              BlocBuilder<AuthCubit, AuthState>(
                                builder: (context, state) {
                                  final isLoading = state is AuthLoading;
                                  return isLoading
                                      ? const CircularProgressIndicator()
                                      : ButtonCustom(
                                          text: context.l10n.authResetPassword,
                                          onPressed: _handleSubmit,
                                        );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
