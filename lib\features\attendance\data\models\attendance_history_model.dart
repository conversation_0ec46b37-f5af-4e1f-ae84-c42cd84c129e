import '../../domain/entities/attendance_history.dart';

class AttendanceHistoryModel extends AttendanceHistoryItem {
  const AttendanceHistoryModel({
    required super.id,
    required super.checkIn,
    required super.checkOut,
    required super.totalHours,
    required super.date,
  });
  factory AttendanceHistoryModel.fromJson(Map<String, dynamic> json) {
    return AttendanceHistoryModel(
      id: json['id'] ?? '',
      checkIn: json['checkIn'] ?? '--:--',
      checkOut: json['checkOut'] ?? '--:--',
      totalHours: json['totalHours'] ?? '--',
      date: json['date'] ?? '',
    );
  }
}
