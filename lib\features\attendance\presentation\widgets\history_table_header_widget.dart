import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';

class HistoryTableHeaderWidget extends StatelessWidget {
  const HistoryTableHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;
    final style = TextStyle(
      color: Colors.grey[600],
      fontWeight: FontWeight.bold,
      fontSize: 14,
    );

    return Padding(
      padding: EdgeInsets.only(
        left: responsive.widthPercentage(1),
        right: responsive.widthPercentage(1),
        top: 8.0,
        bottom: 2.0,
      ),
      child: Row(
        children: [
          Expanded(flex: 3, child: Text(l10n.date.toUpperCase(), style: style)),
          Expanded(
            flex: 2,
            child: Text(
              l10n.inShort.toUpperCase(),
              style: style,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              l10n.outShort.toUpperCase(),
              style: style,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              l10n.hoursShort.toUpperCase(),
              style: style,
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }
}
