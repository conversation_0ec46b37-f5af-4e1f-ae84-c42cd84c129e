import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/task_entity.dart';

abstract class TaskRepository {
  Future<Either<Failure, List<TaskEntity>>> getUserTasks({
    String? status,
    String? priority,
    String? teamId,
    String? dueDate,
  });
  
  Future<Either<Failure, List<TaskEntity>>> getTeamTasks(String teamId, {String? status});
  
  Future<Either<Failure, TaskEntity>> createTask(Map<String, dynamic> taskData);
  
  Future<Either<Failure, TaskEntity>> updateTaskStatus(String taskId, String status);
  
  Future<Either<Failure, TaskEntity>> addTaskComment(String taskId, String message);
  
  Future<Either<Failure, Map<String, dynamic>>> getTaskStats({String? teamId});
}
