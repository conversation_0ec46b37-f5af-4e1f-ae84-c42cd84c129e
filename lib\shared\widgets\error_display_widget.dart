import 'package:flutter/material.dart';
import '../../core/extensions/l10n_extension.dart';

class ErrorDisplayWidget extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;
  final String? retryButtonText;
  final IconData? errorIcon;
  final Color? errorColor;

  const ErrorDisplayWidget({
    super.key,
    this.errorMessage,
    this.onRetry,
    this.retryButtonText,
    this.errorIcon,
    this.errorColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = context.l10n;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error Icon
            Icon(
              errorIcon ?? Icons.error_outline,
              size: 64,
              color: errorColor ?? Colors.red.shade400,
            ),
            
            const SizedBox(height: 16),
            
            // Error Title
            Text(
              l10n.error,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: errorColor ?? Colors.red.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            // Error Message
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                errorMessage ?? l10n.settingError,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              
              // Retry Button
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: Text(retryButtonText ?? l10n.refresh),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// A specialized error widget for network/API errors
class NetworkErrorWidget extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;

  const NetworkErrorWidget({
    super.key,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      errorMessage: errorMessage,
      onRetry: onRetry,
      errorIcon: Icons.wifi_off,
      errorColor: Colors.orange.shade600,
    );
  }
}

/// A specialized error widget for server errors
class ServerErrorWidget extends StatelessWidget {
  final String? errorMessage;
  final VoidCallback? onRetry;

  const ServerErrorWidget({
    super.key,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      errorMessage: errorMessage,
      onRetry: onRetry,
      errorIcon: Icons.cloud_off,
      errorColor: Colors.red.shade600,
    );
  }
}

/// A specialized error widget for validation errors
class ValidationErrorWidget extends StatelessWidget {
  final String errorMessage;
  final VoidCallback? onRetry;

  const ValidationErrorWidget({
    super.key,
    required this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return ErrorDisplayWidget(
      errorMessage: errorMessage,
      onRetry: onRetry,
      errorIcon: Icons.warning_amber,
      errorColor: Colors.amber.shade700,
    );
  }
}
