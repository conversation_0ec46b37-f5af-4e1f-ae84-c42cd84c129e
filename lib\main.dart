import 'package:device_preview/device_preview.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get_storage/get_storage.dart';
import 'package:golderhr/firebase_options.dart';

import 'app.dart';
import 'core/services/firebase_service.dart';
import 'injection_container.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  await dotenv.load(fileName: ".env");
  await GetStorage.init();
  await setupLocator();
  // Khởi tạo Firebase Service để nhận notification (không đăng ký token với backend)
  await FirebaseService().initializeWithoutBackendRegistration();
  runApp(DevicePreview(enabled: !kReleaseMode, builder: (context) => MyApp()));
}
