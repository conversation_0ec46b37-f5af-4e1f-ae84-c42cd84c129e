import '../../../../core/network/dio_client.dart';
import '../../../../core/error/exceptions.dart';
import '../models/admin_user_model.dart';

abstract class AdminUserRemoteDataSource {
  Future<AdminUserListResponse> getAllUsers({
    int page = 1,
    int limit = 10,
    String? search,
    String? role,
    String? department,
    bool includeDeleted = false,
    String sortBy = 'createdAt',
    String sortOrder = 'desc',
  });

  Future<AdminUserModel> getUserById(String userId);

  Future<AdminUserModel> createUser({
    required String fullname,
    required String email,
    required String password,
    String? phone,
    String? department,
    String? position,
    required String role,
    String? organization,
  });

  Future<AdminUserModel> updateUser(
    String userId, {
    String? fullname,
    String? email,
    String? phone,
    String? department,
    String? position,
    String? role,
    String? organization,
    bool? isdisable,
  });

  Future<AdminUserModel> softDeleteUser(String userId);
  Future<AdminUserModel> restoreUser(String userId);
  Future<AdminUserModel> toggleUserStatus(String userId);
  Future<void> resetUserPassword(String userId, String newPassword);
  Future<AdminUserStatistics> getUserStatistics();

  Future<Map<String, dynamic>> bulkDeleteUsers(List<String> userIds);
  Future<Map<String, dynamic>> bulkRestoreUsers(List<String> userIds);
}

class AdminUserRemoteDataSourceImpl implements AdminUserRemoteDataSource {
  final DioClient dioClient;

  AdminUserRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<AdminUserListResponse> getAllUsers({
    int page = 1,
    int limit = 10,
    String? search,
    String? role,
    String? department,
    bool includeDeleted = false,
    String sortBy = 'createdAt',
    String sortOrder = 'desc',
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        'sortBy': sortBy,
        'sortOrder': sortOrder,
        'includeDeleted': includeDeleted,
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }
      if (role != null && role.isNotEmpty) {
        queryParams['role'] = role;
      }
      if (department != null && department.isNotEmpty) {
        queryParams['department'] = department;
      }

      print(
        '🔍 [DEBUG] AdminUserRemoteDataSource: API call with params: $queryParams',
      );

      final response = await dioClient.get(
        '/api/admin/users',
        queryParameters: queryParams,
      );

      print(
        '🔍 [DEBUG] AdminUserRemoteDataSource: API response status: ${response.statusCode}',
      );

      if (response.statusCode == 200) {
        return AdminUserListResponse.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to fetch users',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> getUserById(String userId) async {
    try {
      final response = await dioClient.get('/api/admin/users/$userId');

      if (response.statusCode == 200) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to fetch user',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> createUser({
    required String fullname,
    required String email,
    required String password,
    String? phone,
    String? department,
    String? position,
    required String role,
    String? organization,
  }) async {
    try {
      final data = <String, dynamic>{
        'fullname': fullname,
        'email': email,
        'password': password,
        'role': role,
      };

      if (phone != null) data['phone'] = phone;
      if (department != null) data['department'] = department;
      if (position != null) data['position'] = position;
      if (organization != null) data['organization'] = organization;

      final response = await dioClient.post('/api/admin/users', data: data);

      if (response.statusCode == 201) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to create user',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> updateUser(
    String userId, {
    String? fullname,
    String? email,
    String? phone,
    String? department,
    String? position,
    String? role,
    String? organization,
    bool? isdisable,
  }) async {
    try {
      final data = <String, dynamic>{};

      if (fullname != null) data['fullname'] = fullname;
      if (email != null) data['email'] = email;
      if (phone != null) data['phone'] = phone;
      if (department != null) data['department'] = department;
      if (position != null) data['position'] = position;
      if (role != null) data['role'] = role;
      if (organization != null) data['organization'] = organization;
      if (isdisable != null) data['isdisable'] = isdisable;

      final response = await dioClient.put(
        '/api/admin/users/$userId',
        data: data,
      );

      if (response.statusCode == 200) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to update user',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> softDeleteUser(String userId) async {
    try {
      final response = await dioClient.delete('/api/admin/users/$userId');

      if (response.statusCode == 200) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to delete user',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> restoreUser(String userId) async {
    try {
      final response = await dioClient.patch(
        '/api/admin/users/$userId/restore',
      );

      if (response.statusCode == 200) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to restore user',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserModel> toggleUserStatus(String userId) async {
    try {
      final response = await dioClient.patch(
        '/api/admin/users/$userId/toggle-status',
      );

      if (response.statusCode == 200) {
        return AdminUserModel.fromJson(response.data['data']['user']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to toggle user status',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<void> resetUserPassword(String userId, String newPassword) async {
    try {
      final response = await dioClient.patch(
        '/api/admin/users/$userId/reset-password',
        data: {'newPassword': newPassword},
      );

      if (response.statusCode != 200) {
        throw ServerException(
          response.data['message'] ?? 'Failed to reset password',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<AdminUserStatistics> getUserStatistics() async {
    try {
      final response = await dioClient.get('/api/admin/users/statistics');

      if (response.statusCode == 200) {
        return AdminUserStatistics.fromJson(
          response.data['data']['statistics'],
        );
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to fetch statistics',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> bulkDeleteUsers(List<String> userIds) async {
    try {
      final response = await dioClient.post(
        '/api/admin/users/bulk-delete',
        data: {'userIds': userIds},
      );

      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to bulk delete users',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> bulkRestoreUsers(List<String> userIds) async {
    try {
      final response = await dioClient.post(
        '/api/admin/users/bulk-restore',
        data: {'userIds': userIds},
      );

      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to bulk restore users',
        );
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException('Network error: ${e.toString()}');
    }
  }
}
