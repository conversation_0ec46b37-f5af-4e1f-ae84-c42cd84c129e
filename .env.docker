# ===========================================
# GOLDER HR BACKEND - DOCKER CONFIGURATION
# ===========================================

# Server Configuration
NODE_ENV=production
PORT=3000
APP_PORT=3000

# Database Configuration (MongoDB Cloud)
MONGO_URL=mongodb+srv://golden-hr:<EMAIL>/hrm_crm?retryWrites=true&w=majority&appName=golden-hr
DB_NAME=hrm_crm

# JWT Configuration
JWT_SECRET=day_la_mot_chuoi_bi_mat_rat_dai_va_kho_doan
COOKIE_SECRET=mot_chuoi_bi_mat_khac_cho_cookie

# Email Configuration (để trống, cấu hình sau)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=
EMAIL_PASS=

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=dqkdh8qoe
CLOUDINARY_API_KEY=269286784462321
CLOUDINARY_API_SECRET=dhk-XDlLIq-QX5Bf-5dX8tjKCIs

# Firebase Configuration
FIREBASE_PROJECT_ID=goldenhr-54cda
FIREBASE_PRIVATE_KEY_ID=3684726cfb27ac2605e4f11a3b2dcae8f9c4bdda
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=108134222190750511607
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=redis123
REDIS_PORT=6379

# Face Verification Service
FACE_VERIFICATION_URL=http://localhost:5000
PYTHON_API_URL=http://localhost:5000/verify-face

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=*

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads
