import 'package:flutter/material.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';

class DateRangePicker extends StatelessWidget {
  final DateTime? startDate;
  final DateTime? endDate;
  final Function(DateTime) onStartDateChanged;
  final Function(DateTime) onEndDateChanged;

  const DateRangePicker({
    super.key,
    required this.startDate,
    required this.endDate,
    required this.onStartDateChanged,
    required this.onEndDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.textSecondary.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.date_range_outlined,
                  color: AppColors.primaryGreen,
                  size: 20,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Text(
                context.l10n.dateRange,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontSize: responsive.fontSize(16),
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: responsive.heightPercentage(2)),

          Row(
            children: [
              Expanded(
                child: _buildDateField(
                  context: context,
                  label: context.l10n.startDate,
                  date: startDate,
                  onTap: () => _selectStartDate(context),
                  responsive: responsive,
                  theme: theme,
                ),
              ),
              SizedBox(width: responsive.widthPercentage(3)),
              Expanded(
                child: _buildDateField(
                  context: context,
                  label: context.l10n.endDate,
                  date: endDate,
                  onTap: () => _selectEndDate(context),
                  responsive: responsive,
                  theme: theme,
                ),
              ),
            ],
          ),

          if (startDate != null && endDate != null) ...[
            SizedBox(height: responsive.heightPercentage(2)),
            Container(
              padding: responsive.padding(all: 12),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.primaryBlue,
                    size: 16,
                  ),
                  SizedBox(width: responsive.widthPercentage(2)),
                  Text(
                    context.l10n.durationInfo(
                      _calculateDuration(),
                      _calculateDuration() == 1
                          ? context.l10n.day
                          : context.l10n.days,
                    ),
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: responsive.fontSize(12),
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDateField({
    required BuildContext context,
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required Responsive responsive,
    required ThemeData theme,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontSize: responsive.fontSize(14),
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: responsive.padding(all: 16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: date != null
                    ? AppColors.primaryBlue.withValues(alpha: 0.3)
                    : AppColors.textSecondary.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today_outlined,
                  color: date != null
                      ? AppColors.primaryBlue
                      : AppColors.textSecondary,
                  size: 16,
                ),
                SizedBox(width: responsive.widthPercentage(2)),
                Expanded(
                  child: Text(
                    date != null
                        ? '${date.day}/${date.month}/${date.year}'
                        : context.l10n.selectDate,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontSize: responsive.fontSize(14),
                      color: date != null
                          ? AppColors.textPrimary
                          : AppColors.textSecondary,
                      fontWeight: date != null
                          ? FontWeight.w500
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primaryBlue),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      onStartDateChanged(date);
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: endDate ?? startDate ?? DateTime.now(),
      firstDate: startDate ?? DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: AppColors.primaryBlue),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      onEndDateChanged(date);
    }
  }

  int _calculateDuration() {
    if (startDate == null || endDate == null) return 0;
    return endDate!.difference(startDate!).inDays + 1;
  }
}
