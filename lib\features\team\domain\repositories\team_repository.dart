import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/team_entity.dart';

abstract class TeamRepository {
  // Team Management
  Future<Either<Failure, List<TeamEntity>>> getUserTeams();
  Future<Either<Failure, TeamEntity>> getTeamDetails(String teamId);
  Future<Either<Failure, TeamEntity>> createTeam(Map<String, dynamic> teamData);
  Future<Either<Failure, void>> addTeamMember(String teamId, String userId, String role);
  Future<Either<Failure, Map<String, dynamic>>> getTeamStats(String teamId);

  // Chat Management
  Future<Either<Failure, Map<String, dynamic>>> getTeamChatHistory(String teamId, {int page = 1, int limit = 50});
  Future<Either<Failure, void>> pinMessage(String teamId, String messageId);

  // Meeting Management
  Future<Either<Failure, void>> createMeeting(String teamId, Map<String, dynamic> meetingData);
}
