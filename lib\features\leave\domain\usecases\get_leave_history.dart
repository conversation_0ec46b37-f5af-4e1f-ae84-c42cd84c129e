import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../entities/leave_request.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class GetLeaveHistory implements UseCase<List<LeaveRequest>, GetLeaveHistoryParams> {
  final LeaveRepository repository;

  GetLeaveHistory(this.repository);

  @override
  Future<Either<Failure, List<LeaveRequest>>> call(GetLeaveHistoryParams params) async {
    return await repository.getLeaveHistory(
      page: params.page,
      limit: params.limit,
      status: params.status,
    );
  }
}

class GetLeaveHistoryParams extends Equatable {
  final int page;
  final int limit;
  final String? status;

  const GetLeaveHistoryParams({
    this.page = 1,
    this.limit = 10,
    this.status,
  });

  @override
  List<Object?> get props => [page, limit, status];
}
