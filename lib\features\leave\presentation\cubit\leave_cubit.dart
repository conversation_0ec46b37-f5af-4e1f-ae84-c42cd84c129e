import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../l10n/app_localizations.dart';

import '../../domain/entities/leave_request.dart';

import '../../domain/usecases/get_leave_summary.dart';
import '../../domain/usecases/get_leave_history.dart';
import '../../domain/usecases/get_leave_policies.dart';
import '../../domain/usecases/submit_leave_request.dart';
import '../../domain/usecases/get_approvers.dart';
import '../../domain/usecases/usecase.dart';
import '../../validators/leave_validator.dart';
import 'leave_state.dart';

class LeaveCubit extends Cubit<LeaveState> {
  final GetLeaveSummary getLeaveSummary;
  final GetLeaveHistory getLeaveHistory;
  final GetLeavePolicies getLeavePolicies;
  final SubmitLeaveRequest submitLeaveRequestUseCase;
  final GetApprovers getApprovers;

  LeaveCubit({
    required this.getLeaveSummary,
    required this.getLeaveHistory,
    required this.getLeavePolicies,
    required this.submitLeaveRequestUseCase,
    required this.getApprovers,
  }) : super(LeaveInitial());

  Future<void> loadLeaveData({AppLocalizations? l10n}) async {
    emit(LeaveLoading());

    try {
      // Get leave summary/balance
      final balanceResult = await getLeaveSummary(NoParams());

      // Get recent leave requests (first 5)
      final historyResult = await getLeaveHistory(
        const GetLeaveHistoryParams(page: 1, limit: 5),
      );

      // Get leave policies from backend
      final policiesResult = await getLeavePolicies(NoParams());

      balanceResult.fold((failure) => emit(LeaveError(failure.message)), (
        balance,
      ) {
        historyResult.fold((failure) => emit(LeaveError(failure.message)), (
          recentRequests,
        ) {
          policiesResult.fold((failure) => emit(LeaveError(failure.message)), (
            policies,
          ) {
            emit(
              LeaveLoaded(
                balance: balance,
                recentRequests: recentRequests,
                policies: policies,
              ),
            );
          });
        });
      });
    } catch (e) {
      emit(
        LeaveError(
          l10n?.failedToLoadLeaveData(e.toString()) ??
              'Failed to load leave data: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> submitLeaveRequest({
    required LeaveType type,
    required DateTime startDate,
    required DateTime endDate,
    required String reason,
    String? approverId,
    AppLocalizations? l10n,
  }) async {
    // Validate before submitting
    final validationResult = LeaveValidator.validateLeaveRequest(
      type: type,
      startDate: startDate,
      endDate: endDate,
      reason: reason,
      l10n: l10n,
    );

    if (!validationResult.isValid) {
      // Lấy state hiện tại để giữ lại data
      final currentState = state;
      if (currentState is LeaveLoaded) {
        emit(currentState.copyWith(errorMessage: validationResult.firstError));
      } else {
        emit(LeaveRequestError(validationResult.firstError));
      }
      return;
    }

    // Lấy state hiện tại để giữ lại data
    final currentState = state;
    if (currentState is LeaveLoaded) {
      // Cập nhật state với isSubmitting = true
      emit(currentState.copyWith(isSubmitting: true));
    } else {
      emit(LeaveRequestSubmitting());
    }

    try {
      final result = await submitLeaveRequestUseCase(
        SubmitLeaveRequestParams(
          type: type,
          startDate: startDate,
          endDate: endDate,
          reason: reason,
          approverId: approverId,
        ),
      );

      result.fold(
        (failure) {
          // Nếu có lỗi, trở về state LeaveLoaded với isSubmitting = false và lưu lỗi
          if (currentState is LeaveLoaded) {
            emit(
              currentState.copyWith(
                isSubmitting: false,
                errorMessage: failure.message,
              ),
            );
          } else {
            emit(LeaveRequestError(failure.message));
          }
        },
        (leaveRequest) {
          // Nếu thành công, hiển thị message và reload data
          emit(
            LeaveRequestSubmitted(
              l10n?.leaveRequestSubmittedSuccess ??
                  'Leave request submitted successfully!',
            ),
          );
          // Reload data after successful submission
          loadLeaveData(l10n: l10n);
        },
      );
    } catch (e) {
      // Nếu có exception, trở về state LeaveLoaded với isSubmitting = false và lưu lỗi
      if (currentState is LeaveLoaded) {
        emit(
          currentState.copyWith(
            isSubmitting: false,
            errorMessage:
                l10n?.failedToSubmitLeaveRequest(e.toString()) ??
                'Failed to submit leave request: ${e.toString()}',
          ),
        );
      } else {
        emit(
          LeaveRequestError(
            l10n?.failedToSubmitLeaveRequest(e.toString()) ??
                'Failed to submit leave request: ${e.toString()}',
          ),
        );
      }
    }
  }

  Future<void> refreshData() async {
    await loadLeaveData();
  }

  Future<List<Map<String, dynamic>>> getApproversList() async {
    final result = await getApprovers(NoParams());
    return result.fold((failure) => [], (approvers) => approvers);
  }

  void clearErrorMessage() {
    final currentState = state;
    if (currentState is LeaveLoaded) {
      emit(currentState.copyWith(errorMessage: null));
    }
  }
}
