import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/calendar_l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import '../../domain/entities/calendar_event.dart';

class CalendarHeaderWidget extends StatelessWidget {
  const CalendarHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        final cubit = context.read<CalendarCubit>();
        final currentMonth = cubit.currentMonth;

        return Container(
          margin: context.responsive.padding(horizontal: 16),
          padding: context.responsive.padding(all: 15),
          decoration: BoxDecoration(
            color: AppColors.cardBackground,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryBlue.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMonthNavigation(context, cubit, currentMonth),
              SizedBox(height: context.rh(12)),
              _buildStatCards(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildMonthNavigation(
    BuildContext context,
    CalendarCubit cubit,
    DateTime currentMonth,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildNavigationButton(
          context,
          icon: Icons.chevron_left,
          onPressed: () => cubit.previousMonth(),
        ),
        Text(
          _getMonthYearString(context, currentMonth),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        _buildNavigationButton(
          context,
          icon: Icons.chevron_right,
          onPressed: () => cubit.nextMonth(),
        ),
      ],
    );
  }

  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.black, size: 28),
        padding: EdgeInsets.all(context.rw(8)),
      ),
    );
  }

  Widget _buildStatCards(BuildContext context, CalendarState state) {
    // Tính toán thống kê thực tế từ events
    final events = state.events;
    final currentMonth = context.read<CalendarCubit>().currentMonth;

    // Lọc events trong tháng hiện tại
    final currentMonthEvents = events.where((event) {
      return event.startTime.year == currentMonth.year &&
             event.startTime.month == currentMonth.month &&
             !event.isDeleted;
    }).toList();

    // Tính toán số lượng theo loại
    final meetingsCount = currentMonthEvents
        .where((event) => event.type == CalendarEventType.meeting)
        .length;

    final leavesCount = currentMonthEvents
        .where((event) => event.type == CalendarEventType.leave)
        .length;

    final totalEvents = currentMonthEvents.length;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            title: "Events",
            count: totalEvents.toString(),
            icon: Icons.event_outlined,
          ),
        ),
        SizedBox(width: context.responsive.scaleWidth(10),),
        Expanded(
          child: _buildStatCard(
            context,
            title: "Meetings",
            count: meetingsCount.toString(),
            icon: Icons.groups_outlined,
          ),
        ),
        SizedBox(width: context.responsive.scaleWidth(10),),
        Expanded(
          child: _buildStatCard(
            context,
            title: "Leaves",
            count: leavesCount.toString(),
            icon: Icons.beach_access_outlined,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String count,
    required IconData icon,
  }) {
    return Container(
      width: context.rw(90),
      padding: EdgeInsets.symmetric(
        horizontal: context.rw(6),
        vertical: context.rh(6),
      ),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.black.withValues(alpha: 0.15), width: 0.5),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.black, size: context.rf(16)),
          SizedBox(height: context.rh(2)),
          Text(
            count,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.w600,
              fontSize: context.rf(13),
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Colors.black.withValues(alpha: 0.85),
              fontSize: context.rf(10),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  String _getMonthYearString(BuildContext context, DateTime date) {
    final l10n = context.l10n;
    final monthNames = l10n.calendarFullMonthNames;
    return '${monthNames[date.month - 1]} ${date.year}';
  }
}
