part of 'admin_user_cubit.dart';

class AdminUserState extends Equatable {
  final bool isLoading;
  final bool isLoadingMore;
  final bool isProcessing;
  final bool isLoadingStats;
  final List<AdminUserEntity> users;
  final UserPagination? pagination;
  final UserStatistics? statistics;
  final UserFilterOptions filterOptions;
  final List<String> selectedUserIds;
  final String? error;
  final String? successMessage;

  const AdminUserState({
    this.isLoading = false,
    this.isLoadingMore = false,
    this.isProcessing = false,
    this.isLoadingStats = false,
    this.users = const [],
    this.pagination,
    this.statistics,
    this.filterOptions = const UserFilterOptions(),
    this.selectedUserIds = const [],
    this.error,
    this.successMessage,
  });

  @override
  List<Object?> get props => [
    isLoading,
    isLoadingMore,
    isProcessing,
    isLoadingStats,
    users,
    pagination,
    statistics,
    filterOptions,
    selectedUserIds,
    error,
    successMessage,
  ];

  AdminUserState copyWith({
    bool? isLoading,
    bool? isLoadingMore,
    bool? isProcessing,
    bool? isLoadingStats,
    List<AdminUserEntity>? users,
    UserPagination? pagination,
    UserStatistics? statistics,
    UserFilterOptions? filterOptions,
    List<String>? selectedUserIds,
    String? error,
    String? successMessage,
  }) {
    return AdminUserState(
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      isProcessing: isProcessing ?? this.isProcessing,
      isLoadingStats: isLoadingStats ?? this.isLoadingStats,
      users: users ?? this.users,
      pagination: pagination ?? this.pagination,
      statistics: statistics ?? this.statistics,
      filterOptions: filterOptions ?? this.filterOptions,
      selectedUserIds: selectedUserIds ?? this.selectedUserIds,
      error: error,
      successMessage: successMessage,
    );
  }

  // Helper getters
  bool get hasUsers => users.isNotEmpty;
  bool get hasError => error != null;
  bool get hasSuccess => successMessage != null;
  bool get hasSelection => selectedUserIds.isNotEmpty;
  bool get isAllSelected =>
      selectedUserIds.length == users.length && users.isNotEmpty;
  bool get canLoadMore => pagination?.hasNext ?? false;

  int get totalUsers => pagination?.totalUsers ?? 0;
  int get currentPage => pagination?.currentPage ?? 1;
  int get totalPages => pagination?.totalPages ?? 1;

  // Filtered users based on current filter options
  List<AdminUserEntity> get filteredUsers {
    var filtered = users;

    // Apply status filter if not showing all
    if (filterOptions.status != UserStatus.all) {
      switch (filterOptions.status) {
        case UserStatus.active:
          filtered = filtered.where((user) => user.isActive).toList();
          break;
        case UserStatus.disabled:
          filtered = filtered
              .where((user) => user.isdisable && !user.isdeleted)
              .toList();
          break;
        case UserStatus.deleted:
          filtered = filtered.where((user) => user.isdeleted).toList();
          break;
        case UserStatus.all:
          break;
      }
    }

    return filtered;
  }

  // Statistics helpers
  int get activeUsersCount => statistics?.overview.activeUsers ?? 0;
  int get disabledUsersCount => statistics?.overview.disabledUsers ?? 0;
  int get deletedUsersCount => statistics?.overview.deletedUsers ?? 0;
  int get totalUsersCount => statistics?.overview.totalUsers ?? 0;

  double get activePercentage => statistics?.overview.activePercentage ?? 0;
  double get disabledPercentage => statistics?.overview.disabledPercentage ?? 0;
  double get deletedPercentage => statistics?.overview.deletedPercentage ?? 0;

  List<RoleDistributionEntity> get roleDistribution =>
      statistics?.roleDistribution ?? [];

  // Selection helpers
  bool isUserSelected(String userId) => selectedUserIds.contains(userId);

  List<AdminUserEntity> get selectedUsers {
    return users.where((user) => selectedUserIds.contains(user.id)).toList();
  }

  // Filter helpers
  bool get hasActiveFilters {
    return filterOptions.search?.isNotEmpty == true ||
        filterOptions.role?.isNotEmpty == true ||
        filterOptions.department?.isNotEmpty == true ||
        filterOptions.status != UserStatus.active ||
        filterOptions.includeDeleted;
  }

  String get filterSummary {
    final filters = <String>[];

    if (filterOptions.search?.isNotEmpty == true) {
      filters.add('Search: "${filterOptions.search}"');
    }

    if (filterOptions.role?.isNotEmpty == true) {
      filters.add('Role: ${filterOptions.role}');
    }

    if (filterOptions.department?.isNotEmpty == true) {
      filters.add('Department: ${filterOptions.department}');
    }

    if (filterOptions.status != UserStatus.active) {
      filters.add('Status: ${filterOptions.status.displayName}');
    }

    return filters.isEmpty ? 'No filters applied' : filters.join(', ');
  }
}
