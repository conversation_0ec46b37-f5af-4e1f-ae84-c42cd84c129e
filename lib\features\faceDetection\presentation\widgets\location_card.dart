import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../shared/widgets/location_widget.dart';
import '../cubit/face_checkin_cubit.dart';
import '../cubit/face_checkin_state.dart';

class LocationCard extends StatelessWidget {
  const LocationCard({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      builder: (context, state) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(responsive.fontSize(16)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(12),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: LocationWidget(
            initialLocation: state.currentLocation,
            onLocationChanged: (position) {
              if (position != null) {
                context.read<FaceDetectionCubit>().updateLocation(position);
              }
            },
            showAccuracy: true,
            autoGetLocation: state.currentLocation == null,
            iconColor: Colors.green.shade600,
            addressStyle: TextStyle(
              fontSize: responsive.fontSize(13),
              fontWeight: FontWeight.w600,
              color: Colors.blue.shade700,
            ),
            accuracyStyle: TextStyle(
              fontSize: responsive.fontSize(12),
              color: Colors.green.shade600,
            ),
          ),
        );
      },
    );
  }
}
