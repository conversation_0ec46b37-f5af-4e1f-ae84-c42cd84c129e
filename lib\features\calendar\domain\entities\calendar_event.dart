import 'package:equatable/equatable.dart';

import '../../../auth/domain/entities/user_entity.dart';

enum CalendarEventType {
  meeting,
  leave,
  holiday,
  training,
  event,
  other,
  unknown
}
class CalendarEventEntity extends Equatable {
  final String id;
  final String title;
  final String? description; // <PERSON><PERSON> thể null
  final DateTime startTime;
  final DateTime endTime;
  final bool isAllDay;
  final CalendarEventType type;
  final String color;
  final String? location; // Có thể null
  final List<UserEntity> attendees;
  final UserEntity createdBy;
  final bool isRecurring;
  final String? recurrenceRule; // Có thể null
  final bool isDeleted;
  final DateTime createdAt;
  final DateTime updatedAt;

  const CalendarEventEntity({
    required this.id,
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    required this.isAllDay,
    required this.type,
    required this.color,
    this.location,
    required this.attendees,
    required this.createdBy,
    required this.isRecurring,
    this.recurrenceRule,
    required this.isDeleted,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    description,
    startTime,
    endTime,
    isAllDay,
    type,
    color,
    location,
    attendees,
    createdBy,
    isRecurring,
    recurrenceRule,
    isDeleted,
    createdAt,
    updatedAt
  ];
}

// Type alias for backward compatibility
typedef CalendarEvent = CalendarEventEntity;