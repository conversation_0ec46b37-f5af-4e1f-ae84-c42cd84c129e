import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:golderhr/features/faceDetection/domain/entities/attendance_record_entity.dart';
import 'package:golderhr/features/faceDetection/domain/entities/location_entity.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/attendance_status_entity.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import '../../domain/repositories/attendance_repository.dart';
import '../datasources/attendance_remote_data_source.dart';
import '../models/location_model.dart';

class AttendanceRepositoryImpl implements AttendanceRepository {
  final AttendanceRemoteDataSource remoteDataSource;

  AttendanceRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, AttendanceRecordEntity>> checkIn({
    // Nhận Entity
    required File image,
    required LocationEntity location,
  }) async {
    try {
      final locationModel = LocationModel(
        coordinates: location.coordinates,
        address: location.address,
      );
      final response = await remoteDataSource.checkIn(
        image: image,
        location: locationModel,
      );

      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure("An unexpected error occurred: ${e.toString()}"),
      );
    }
  }

  @override
  Future<Either<Failure, AttendanceRecordEntity>> checkOut({
    // Nhận Entity
    required File image,
    required LocationEntity location,
  }) async {
    try {
      final locationModel = LocationModel(
        coordinates: location.coordinates,
        address: location.address,
      );
      final response = await remoteDataSource.checkOut(
        image: image,
        location: locationModel,
      );

      return Right(response);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      // Bắt các lỗi không lường trước khác
      return Left(
        ServerFailure("An unexpected error occurred: ${e.toString()}"),
      );
    }
  }

  @override
  Future<Either<Failure, AttendanceStatusEntity?>> getTodayAttendance() async {
    try {
      // remoteDataSource giờ trả về AttendanceStatusModel?
      final result = await remoteDataSource.getTodayAttendance();
      return Right(result); // result cũng là một AttendanceStatusEntity?
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure(
          "An unexpected error occurred while fetching today's attendance.",
        ),
      );
    }
  }

  @override
  Future<Either<Failure, void>> submitManualAttendance(
    ManualAttendanceEntity manualAttendance,
  ) async {
    try {
      await remoteDataSource.submitManualAttendance(manualAttendance);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure("An unexpected error occurred: ${e.toString()}"),
      );
    }
  }
}
