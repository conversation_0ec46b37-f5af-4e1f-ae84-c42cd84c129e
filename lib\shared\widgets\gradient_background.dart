import 'package:flutter/material.dart';

class GradientBackground extends StatelessWidget {
  final Widget child;

  const GradientBackground({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF0491FB), // Vibrant blue
            Color.fromRGBO(
              4,
              145,
              251,
              0.8,
            ), // Slightly lighter blue for smooth transition
            Color.fromRGBO(
              4,
              183,
              4,
              0.8,
            ), // Slightly lighter green for smooth transition
            Color(0xFF04B704), // Vibrant green
          ],
          stops: [0.0, 0.4, 0.6, 1.0],
        ),
      ),
      child: child,
    );
  }
}
