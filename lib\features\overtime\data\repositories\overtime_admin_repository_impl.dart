import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../../domain/repositories/overtime_admin_repository.dart';
import '../datasources/overtime_admin_remote_data_source.dart';

class OvertimeAdminRepositoryImpl implements OvertimeAdminRepository {
  final OvertimeAdminRemoteDataSource remoteDataSource;

  OvertimeAdminRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<OvertimeRequestEntity>>> getAllOvertimeRequests({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      final requests = await remoteDataSource.getAllOvertimeRequests(
        page: page,
        limit: limit,
        status: status,
      );
      return Right(requests);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, OvertimeRequestEntity>> approveOvertimeRequest(
    String requestId,
  ) async {
    try {
      final request = await remoteDataSource.approveOvertimeRequest(requestId);
      return Right(request);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, OvertimeRequestEntity>> rejectOvertimeRequest(
    String requestId,
    String rejectionReason,
  ) async {
    try {
      final request = await remoteDataSource.rejectOvertimeRequest(
        requestId,
        rejectionReason,
      );
      return Right(request);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }
}
