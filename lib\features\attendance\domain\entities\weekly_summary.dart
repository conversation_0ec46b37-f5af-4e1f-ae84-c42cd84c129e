// lib/features/attendance/domain/entities/weekly_summary.dart
import 'package:equatable/equatable.dart';

class WeeklySummary extends Equatable {
  final String workDays; // vd: "4 / 5"
  final String totalHours; // vd: "32h 15m"
  final String overtime; // vd: "2h 30m"
  final int lateArrivals; // vd: 1
  final double performance; // vd: 0.8 (cho vòng tròn tiến độ)

  const WeeklySummary({
    required this.workDays,
    required this.totalHours,
    required this.overtime,
    required this.lateArrivals,
    required this.performance,
  });

  @override
  List<Object?> get props => [
    workDays,
    totalHours,
    overtime,
    lateArrivals,
    performance,
  ];
}
