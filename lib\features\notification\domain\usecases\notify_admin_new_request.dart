import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/notification_repository.dart';

class NotifyAdminNewRequest
    implements UseCase<void, NotifyAdminNewRequestParams> {
  final NotificationRepository repository;

  NotifyAdminNewRequest(this.repository);

  @override
  Future<Either<Failure, void>> call(NotifyAdminNewRequestParams params) async {
    return await repository.notifyAdminNewRequest(
      requestType: params.requestType,
      requestId: params.requestId,
      employeeName: params.employeeName,
      requestDetails: params.requestDetails,
    );
  }
}

class NotifyAdminNewRequestParams extends Equatable {
  final String requestType;
  final String requestId;
  final String employeeName;
  final String requestDetails;

  const NotifyAdminNewRequestParams({
    required this.requestType,
    required this.requestId,
    required this.employeeName,
    required this.requestDetails,
  });

  @override
  List<Object> get props => [
    requestType,
    requestId,
    employeeName,
    requestDetails,
  ];

  @override
  String toString() {
    return 'NotifyAdminNewRequestParams(requestType: $requestType, requestId: $requestId, employeeName: $employeeName, requestDetails: $requestDetails)';
  }
}
