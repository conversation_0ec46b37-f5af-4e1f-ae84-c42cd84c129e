// lib/utils/overtime_validators.dart

import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/features/overtime/domain/entities/overtime_request_entity.dart'; // Import chính xác OvertimeRequestEntity của bạn

class OvertimeValidators {
  OvertimeValidators._();

  static String? validateReason(BuildContext context, String? value) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.reasonIsRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.reasonTooShort;
    }
    if (value.trim().length > 500) {
      return context.l10n.reasonTooLong;
    }
    return null;
  }

  static String? validateDate(BuildContext context, DateTime? date) {
    if (date == null) {
      return context.l10n.pleaseSelectADate;
    }
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    if (selectedDateOnly.isBefore(todayOnly)) {
      return context.l10n.cannotSelectPastDates;
    }
    return null;
  }

  static String? validateTimeRange(
    BuildContext context,
    DateTime? selectedDate,
    TimeOfDay? startTime,
    TimeOfDay? endTime,
  ) {
    if (selectedDate == null || startTime == null || endTime == null) {
      return context.l10n.pleaseSelectAllTimeFields;
    }

    final startDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      startTime.hour,
      startTime.minute,
    );

    final endDateTime = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
      endTime.hour,
      endTime.minute,
    );

    if (endDateTime.isBefore(startDateTime) ||
        endDateTime.isAtSameMomentAs(startDateTime)) {
      return context.l10n.endTimeMustBeAfterStartTime;
    }

    final duration = endDateTime.difference(startDateTime);
    if (duration.inMinutes < 30) {
      return context.l10n.minimumOvertimeDuration;
    }

    if (duration.inHours > 12) {
      return context.l10n.maximumOvertimeDuration;
    }

    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);
    final selectedDateOnly = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
    );

    if (selectedDateOnly.isAtSameMomentAs(todayOnly)) {
      final now = DateTime.now();
      if (startDateTime.isBefore(now)) {
        return context.l10n.cannotSelectPastTime;
      }
    }

    if (startTime.hour < 6 || endTime.hour > 23) {
      return context.l10n.overtimeHoursRange;
    }

    return null;
  }

  static String? validateApprover(BuildContext context, dynamic approver) {
    if (approver == null) {
      return context.l10n.pleaseSelectApprover;
    }
    return null;
  }

  // Phương thức validator cho kiểm tra trùng lặp, sử dụng List<OvertimeRequestEntity>
  static String? validateDuplicateRequest(
    BuildContext context,
    DateTime selectedDate,
    List<OvertimeRequestEntity> existingRequests,
    // Dùng OvertimeRequestEntity
  ) {
    final selectedDateOnly = DateTime(
      selectedDate.year,
      selectedDate.month,
      selectedDate.day,
    );

    final hasDuplicate = existingRequests.any((req) {
      final reqDateOnly = DateTime(req.date.year, req.date.month, req.date.day);

      // Kiểm tra trạng thái. Giả sử OvertimeStatus.pending và OvertimeStatus.approved là các giá trị hợp lệ.
      return reqDateOnly.isAtSameMomentAs(selectedDateOnly) &&
          (req.status == OvertimeStatus.pending ||
              req.status == OvertimeStatus.approved);
    });

    if (hasDuplicate) {
      return context.l10n.alreadyHaveRequestForThisDate;
    }
    return null;
  }
}
