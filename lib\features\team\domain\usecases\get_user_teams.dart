import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/team_entity.dart';
import '../repositories/team_repository.dart';

class GetUserTeams implements UseCase<List<TeamEntity>, NoParams> {
  final TeamRepository repository;

  GetUserTeams(this.repository);

  @override
  Future<Either<Failure, List<TeamEntity>>> call(NoParams params) async {
    return await repository.getUserTeams();
  }
}
