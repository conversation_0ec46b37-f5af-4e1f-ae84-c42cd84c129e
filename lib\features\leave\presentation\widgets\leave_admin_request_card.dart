import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:intl/intl.dart';

// Import các widget chung mới
import 'request_card_info_item.dart';
import 'request_status_chip.dart';

import '../../domain/entities/leave_request.dart';
import '../cubit/leave_admin_cubit.dart';
import 'leave_rejection_dialog.dart';

class LeaveAdminRequestCard extends StatelessWidget {
  final LeaveRequest request;
  final bool isProcessing;

  const LeaveAdminRequestCard({
    super.key,
    required this.request,
    required this.isProcessing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0,
      shadowColor: Colors.grey.withValues(alpha: 0.1),
      color: AppColors.cardBackground,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16),
      side: BorderSide(
        color: AppColors.border
      )
      ),
      child: Padding(
        padding: context.responsive.padding(all:16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: Text(
                request.employeeName,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Padding(
                padding: const EdgeInsets.only(top: 4.0),
                child: Text(
                  _getLeaveTypeLabel(request.type),
                  style: theme.textTheme.bodySmall
                      ?.copyWith(color: theme.colorScheme.primary),
                ),
              ),
              trailing: RequestStatusChip(status: _covertStatus(request.status)),
            ),

            const Divider(height: 24),

            // Phần thông tin
            RequestCardInfoItem(
              icon: Icons.calendar_today_rounded,
              label: context.l10n.dateRange,
              value: request.dateRange,
            ),
            const SizedBox(height: 16),
            RequestCardInfoItem(
              icon: Icons.schedule_rounded,
              label: context.l10n.duration,
              value: '${request.duration} day${request.duration > 1 ? 's' : ''}',
            ),
            const SizedBox(height: 16),
            RequestCardInfoItem(
              icon: Icons.description_outlined,
              label: context.l10n.reason,
              value: request.reason,
              useColumnLayout: true,
            ),

            // Lý do từ chối (nếu có)
            if (request.rejectionReason != null) ...[
              const SizedBox(height: 16),
              RequestCardInfoItem(
                icon: Icons.gpp_bad_outlined,
                label: context.l10n.rejectionReason,
                value: request.rejectionReason!,
                valueColor: Colors.red[700],
              ),
            ],

            // Các nút hành động
            if (request.status == LeaveStatus.pending) ...[
              const SizedBox(height: 24),
              _ActionButtons(
                isProcessing: isProcessing,
                onApprove: () => _approveRequest(context),
                onReject: () => _showRejectDialog(context),
              ),
            ],

            // Thông tin người duyệt
            if (request.status != LeaveStatus.pending && request.approvedAt != null) ...[
              const Divider(height: 32),
              _ApprovalInfo(
                status: request.status,
                approverName: _getApproverDisplayName(),
                approvedAt: request.approvedAt!,
              )
            ]
          ],
        ),
      ),
    );
  }

  RequestStatus _covertStatus(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.pending: return RequestStatus.pending;
      case LeaveStatus.approved: return RequestStatus.approved;
      case LeaveStatus.rejected: return RequestStatus.rejected;
      case LeaveStatus.cancelled: return RequestStatus.cancelled;
    }
  }

  void _approveRequest(BuildContext context) {
    context.read<LeaveAdminCubit>().approveRequest(request.id);
  }

  void _showRejectDialog(BuildContext context) {
    final cubit = context.read<LeaveAdminCubit>();
    showDialog(
      context: context,
      builder: (dialogContext) => LeaveRejectionDialog(
        onReject: (reason) => cubit.rejectRequest(request.id, reason),
      ),
    );
  }

  String _getApproverDisplayName() {
    if (request.approverName != null && request.approverName!.isNotEmpty) {
      return request.approverName!;
    }
    return 'Admin';
  }

  String _getLeaveTypeLabel(LeaveType type) {
    switch (type) {
      case LeaveType.annual: return 'Annual Leave';
      case LeaveType.sick: return 'Sick Leave';
      case LeaveType.personal: return 'Personal Leave';
      case LeaveType.maternity: return 'Maternity Leave';
      case LeaveType.unpaid: return 'Unpaid Leave';
    }
  }
}

// Widget nội bộ cho các nút hành động để giữ code gọn gàng
class _ActionButtons extends StatelessWidget {
  final bool isProcessing;
  final VoidCallback onApprove;
  final VoidCallback onReject;

  const _ActionButtons({
    required this.isProcessing,
    required this.onApprove,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: isProcessing ? null : onReject,
            icon: const Icon(Icons.close),
            label: const Text('Reject'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: BorderSide(color: Colors.red.withValues(alpha: 0.5)),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isProcessing ? null : onApprove,
            icon: const Icon(Icons.check),
            label: const Text('Approve'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              padding: const EdgeInsets.symmetric(vertical: 12),
              elevation: 1,
            ),
          ),
        ),
      ],
    );
  }
}

// Widget nội bộ cho thông tin duyệt
class _ApprovalInfo extends StatelessWidget {
  final LeaveStatus status;
  final String approverName;
  final DateTime approvedAt;

  const _ApprovalInfo({required this.status, required this.approverName, required this.approvedAt});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isApproved = status == LeaveStatus.approved;
    final color = isApproved ? Colors.green : Colors.red;
    final text = isApproved ? 'Approved by' : 'Rejected by';

    return Text.rich(
      TextSpan(
        style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        children: [
          TextSpan(text: '$text '),
          TextSpan(
            text: approverName,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
          TextSpan(text: ' at ${DateFormat('dd/MM/yyyy HH:mm').format(approvedAt)}'),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }
}