import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/task_entity.dart';
import '../repositories/task_repository.dart';

class GetUserTasks implements UseCase<List<TaskEntity>, GetUserTasksParams> {
  final TaskRepository repository;

  GetUserTasks(this.repository);

  @override
  Future<Either<Failure, List<TaskEntity>>> call(GetUserTasksParams params) async {
    return await repository.getUserTasks(
      status: params.status,
      priority: params.priority,
      teamId: params.teamId,
      dueDate: params.dueDate,
    );
  }
}

class GetUserTasksParams extends Equatable {
  final String? status;
  final String? priority;
  final String? teamId;
  final String? dueDate;

  const GetUserTasksParams({
    this.status,
    this.priority,
    this.teamId,
    this.dueDate,
  });

  @override
  List<Object?> get props => [status, priority, teamId, dueDate];
}
