import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
class RequestCardInfoItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final Color? valueColor;
  final bool useColumnLayout; 

  const RequestCardInfoItem({
    super.key,
    required this.icon,
    required this.label,
    required this.value,
    this.valueColor,
    this.useColumnLayout = false, 
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: context.responsive.fontSize(18),
          color: theme.colorScheme.primary,
        ),
        SizedBox(width: context.responsive.widthPercentage(2)),
        Expanded(
          child: useColumnLayout
              ? Column( 
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
               SizedBox(height: context.responsive.heightPercentage(1)),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: valueColor ?? Colors.black87,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
            ],
          )
              : Row( 
            children: [
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              Text(
                value,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: valueColor ?? Colors.black87,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
