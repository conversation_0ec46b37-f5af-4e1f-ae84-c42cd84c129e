import '../../domain/entities/team_entity.dart';

class TeamModel extends TeamEntity {
  const TeamModel({
    required super.id,
    required super.name,
    super.description,
    super.avatar,
    required super.leaderId,
    super.departmentId,
    super.isActive = true,
    super.userRole,
    super.memberCount,
    super.stats,
    super.members,
    super.createdAt,
    super.updatedAt,
  });

  factory TeamModel.fromJson(Map<String, dynamic> json) {
    return TeamModel(
      id: json['_id'] ?? json['id'],
      name: json['name'],
      description: json['description'],
      avatar: json['avatar'],
      leaderId: json['leaderId'] is Map
          ? json['leaderId']['_id'] ?? json['leaderId']['id']
          : json['leaderId'],
      departmentId: json['departmentId'] is Map
          ? json['departmentId']['_id'] ?? json['departmentId']['id']
          : json['departmentId'],
      isActive: json['isActive'] ?? true,
      userRole: json['userRole'],
      memberCount: json['memberCount'] ?? 0,
      stats: json['stats'] != null ? TeamStatsModel.fromJson(json['stats']) : null,
      members: json['members'] != null
          ? (json['members'] as List).map((m) => TeamMemberModel.fromJson(m)).toList()
          : null,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'leaderId': leaderId,
      'departmentId': departmentId,
      'isActive': isActive,
      'userRole': userRole,
      'memberCount': memberCount,
      'stats': stats?.toJson(),
      'members': members?.map((m) => (m as TeamMemberModel).toJson()).toList(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  TeamModel copyWith({
    String? id,
    String? name,
    String? description,
    String? avatar,
    String? leaderId,
    String? departmentId,
    bool? isActive,
    String? userRole,
    int? memberCount,
    TeamStatsEntity? stats,
    List<TeamMemberEntity>? members,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TeamModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      avatar: avatar ?? this.avatar,
      leaderId: leaderId ?? this.leaderId,
      departmentId: departmentId ?? this.departmentId,
      isActive: isActive ?? this.isActive,
      userRole: userRole ?? this.userRole,
      memberCount: memberCount ?? this.memberCount,
      stats: stats ?? this.stats,
      members: members ?? this.members,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class TeamStatsModel extends TeamStatsEntity {
  const TeamStatsModel({
    required super.totalTasks,
    required super.completedTasks,
    required super.pendingTasks,
    required super.totalMembers,
    required super.totalMeetings,
    required super.totalDocuments,
    required super.totalMessages,
    required super.completionRate,
  });

  factory TeamStatsModel.fromJson(Map<String, dynamic> json) {
    return TeamStatsModel(
      totalTasks: json['totalTasks'] ?? 0,
      completedTasks: json['completedTasks'] ?? 0,
      pendingTasks: json['pendingTasks'] ?? 0,
      totalMembers: json['totalMembers'] ?? 0,
      totalMeetings: json['totalMeetings'] ?? 0,
      totalDocuments: json['totalDocuments'] ?? 0,
      totalMessages: json['totalMessages'] ?? 0,
      completionRate: json['completionRate'] ?? 0,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'pendingTasks': pendingTasks,
      'totalMembers': totalMembers,
      'totalMeetings': totalMeetings,
      'totalDocuments': totalDocuments,
      'totalMessages': totalMessages,
      'completionRate': completionRate,
    };
  }
}

class TeamMemberModel extends TeamMemberEntity {
  const TeamMemberModel({
    required super.id,
    required super.fullname,
    super.avatar,
    super.email,
    super.department,
    super.position,
    super.role,
    super.joinedAt,
    super.isActive = true,
  });

  factory TeamMemberModel.fromJson(Map<String, dynamic> json) {
    // Handle nested user data
    final userData = json['userId'] ?? json;

    return TeamMemberModel(
      id: userData['_id'] ?? userData['id'],
      fullname: userData['fullname'],
      avatar: userData['avatar'],
      email: userData['email'],
      department: userData['department'],
      position: userData['position'],
      role: json['role'], // Team role, not user role
      joinedAt: json['joinedAt'] != null ? DateTime.parse(json['joinedAt']) : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'avatar': avatar,
      'email': email,
      'department': department,
      'position': position,
      'role': role,
      'joinedAt': joinedAt?.toIso8601String(),
      'isActive': isActive,
    };
  }
}
