import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';

class SectionTitle extends StatelessWidget {
  final String title;

  const SectionTitle({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Text(
      title,
      style: context.lightTheme.textTheme.titleMedium!.copyWith(
        fontWeight: FontWeight.bold,
        color: context.lightTheme.colorScheme.secondary,
      ),
    );
  }
}
