import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/team_entity.dart';
import '../repositories/team_repository.dart';

class GetTeamDetails implements UseCase<TeamEntity, GetTeamDetailsParams> {
  final TeamRepository repository;

  GetTeamDetails(this.repository);

  @override
  Future<Either<Failure, TeamEntity>> call(GetTeamDetailsParams params) async {
    return await repository.getTeamDetails(params.teamId);
  }
}

class GetTeamDetailsParams extends Equatable {
  final String teamId;

  const GetTeamDetailsParams({required this.teamId});

  @override
  List<Object> get props => [teamId];
}
