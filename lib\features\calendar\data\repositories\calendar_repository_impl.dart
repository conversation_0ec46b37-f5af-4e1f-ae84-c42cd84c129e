import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/calendar_event.dart';

import '../../domain/params/create_event_params.dart';
import '../../domain/params/update_event_params.dart';
import '../../domain/repositories/calendar_repository.dart';
import '../datasources/calendar_remote_data_source.dart'; // Sẽ tạo file này

class CalendarRepositoryImpl implements CalendarRepository {
  final CalendarRemoteDataSource remoteDataSource;


  CalendarRepositoryImpl({
    required this.remoteDataSource,

  });

  @override
  Future<Either<Failure, CalendarEvent>> createCalendarEvent(
      CreateEventParams params) async {

    try {

      final remoteEvent = await remoteDataSource.createCalendarEvent(params);

      return Right(remoteEvent);
    } on ServerException catch (e) {
      // 3. Bắt lỗi từ server và biến nó thành ServerFailure
      return Left(ServerFailure( e.message));
    }
  }

  // Triển khai các hàm khác với logic tương tự
  @override
  Future<Either<Failure, void>> deleteCalendarEvent(String eventId) async {
    try {
      await remoteDataSource.deleteCalendarEvent(eventId);
      return const Right(null); // Thành công không trả về gì
    } on ServerException catch (e) {
      return Left(ServerFailure( e.message));
    }
  }

  @override
  Future<Either<Failure, CalendarEvent>> getCalendarEventById(String eventId) async {
    try {
      final remoteEvent = await remoteDataSource.getCalendarEventById(eventId);
      return Right(remoteEvent);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    CalendarEventType? type,
    int? page,
    int? limit
  }) async {
    try {
      final remoteEvents = await remoteDataSource.getCalendarEvents(
          startDate: startDate,
          endDate: endDate,
          type: type?.toString().split('.').last,
          page: page,
          limit: limit
      );
      return Right(remoteEvents);
    } on ServerException catch (e) {
      return Left(ServerFailure( e.message));
    }
  }

  @override
  Future<Either<Failure, CalendarEvent>> updateCalendarEvent(
      String eventId, UpdateEventParams params) async {
    try {
      final remoteEvent = await remoteDataSource.updateCalendarEvent(eventId, params);
      return Right(remoteEvent);
    } on ServerException catch (e) {
      return Left(ServerFailure( e.message));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getCalendarSummary() async {
    try {
      final summary = await remoteDataSource.getCalendarSummary();
      return Right(summary);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getTodayEvents() async {
    try {
      final events = await remoteDataSource.getTodayEvents();
      return Right(events);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getUpcomingEvents({int? limit}) async {
    try {
      final events = await remoteDataSource.getUpcomingEvents(limit: limit);
      return Right(events);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> getWeeklyEvents({required DateTime weekStart}) async {
    try {
      final events = await remoteDataSource.getWeeklyEvents(weekStart: weekStart);
      return Right(events);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }

  @override
  Future<Either<Failure, List<CalendarEvent>>> checkEventConflicts({
    required DateTime startTime,
    required DateTime endTime,
    String? excludeEventId,
  }) async {
    try {
      final conflicts = await remoteDataSource.checkEventConflicts(
        startTime: startTime,
        endTime: endTime,
        excludeEventId: excludeEventId,
      );
      return Right(conflicts);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    }
  }
}