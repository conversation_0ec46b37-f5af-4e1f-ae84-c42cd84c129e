name: gold<PERSON><PERSON>
description: "A new Flutter project."

publish_to: "none"
version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.1
  get_it: ^8.0.3
  dio: ^5.8.0+1
  go_router: ^15.1.3
  freezed_annotation: ^2.4.1
  device_preview: ^1.2.0
  intl: ^0.20.2
  equatable: ^2.0.7
  shared_preferences: ^2.5.3
  logger: ^2.5.0
  flutter_native_splash: ^2.4.6
  flutter_launcher_icons: ^0.14.3
  get_storage: ^2.1.1
  google_nav_bar: ^5.0.7
  iconsax: ^0.0.8
  flutter_svg: ^2.2.0
  timeago: ^3.7.1
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  dartz: ^0.10.1
  url_launcher: ^6.3.1
  permission_handler: ^12.0.0+1
  local_auth: ^2.3.0
  flutter_secure_storage: ^9.2.4
  flutter_animate: ^4.5.2
  google_mlkit_face_detection: ^0.13.1
  geolocator: ^14.0.1
  geocoding: ^3.0.0
  device_info_plus: ^11.5.0
  package_info_plus: ^8.0.2
  flutter_dotenv: ^5.2.1
  dotted_border: ^3.0.1
  table_calendar: ^3.2.0
  flutter_cache_manager: ^3.4.1
  firebase_messaging: ^15.2.7
  firebase_core: ^3.14.0
  socket_io_client: ^2.0.3+1
  syncfusion_flutter_datagrid: ^28.1.33
  flutter_local_notifications: ^19.3.0
  connectivity_plus: ^6.1.4


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - .env
    - assets/images/
    - assets/icons/
    - assets/fonts/

  fonts:
    - family: Roboto
      fonts:
        - asset: assets/fonts/Roboto-Light.ttf
          weight: 300
        - asset: assets/fonts/Roboto-Regular.ttf
          weight: 400
        - asset: assets/fonts/Roboto-Medium.ttf
          weight: 500
        - asset: assets/fonts/Roboto-Bold.ttf
          weight: 700
        - asset: assets/fonts/Roboto-Black.ttf
          weight: 900
        - asset: assets/fonts/Roboto-Italic.ttf
          style: italic

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/logo.jpg"
