import 'package:equatable/equatable.dart';

import '../../domain/entities/calendar_event.dart';


class CalendarState extends Equatable {
  final bool isLoading;
  final bool isProcessing;
  final List<CalendarEvent> events;
  final List<CalendarEvent> searchResults;
  final CalendarEvent? selectedEvent;
  final DateTime? selectedDate;
  final String? error;
  final String? successMessage;

  const CalendarState({
    this.isLoading = false,
    this.isProcessing = false,
    this.events = const [],
    this.searchResults = const [],
    this.selectedEvent,
    this.selectedDate,
    this.error,
    this.successMessage,
});

  CalendarState copyWith({
    bool? isLoading,
    bool? isProcessing,
    List<CalendarEvent>? events,
    List<CalendarEvent>? searchResults,
    CalendarEvent? selectedEvent,
    DateTime? selectedDate,
    String? error,
    String? successMessage,
  }){
    return CalendarState(
      isLoading: isLoading ?? this.isLoading,
      isProcessing: isProcessing ?? this.isProcessing,
      events: events ?? this.events,
      searchResults: searchResults ?? this.searchResults,
      selectedEvent: selectedEvent ?? this.selectedEvent,
      selectedDate: selectedDate ?? this.selectedDate,
      error: error,
      successMessage: successMessage,
    );
  }
  bool get hasError => error != null;
  bool get hasSuccess => successMessage != null;

  @override
  List<Object?> get props => [
    isLoading,
    isProcessing,
    events,
    searchResults,
    selectedEvent,
    selectedDate,
    error,
    successMessage,
  ];
}

// Specific state classes for different scenarios
class CalendarEventsLoaded extends CalendarState {
  final Map<String, dynamic>? summary;

  const CalendarEventsLoaded({
    required super.events,
    this.summary,
    super.isLoading,
    super.isProcessing,
    super.searchResults,
    super.selectedEvent,
    super.selectedDate,
    super.error,
    super.successMessage,
  });

  @override
  List<Object?> get props => [...super.props, summary];
}

class CalendarEventsSearched extends CalendarState {
  const CalendarEventsSearched({
    required super.searchResults,
    super.isLoading,
    super.isProcessing,
    super.events,
    super.selectedEvent,
    super.selectedDate,
    super.error,
    super.successMessage,
  });
}