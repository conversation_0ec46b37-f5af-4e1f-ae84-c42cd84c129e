import 'package:flutter/material.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/manual_attendance_entity.dart';

class ManualAttendanceFilter extends StatelessWidget {
  final ManualAttendanceStatus? selectedStatus;
  final DateTime? startDate;
  final DateTime? endDate;
  final Function({
    ManualAttendanceStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) onFilterChanged;

  const ManualAttendanceFilter({
    super.key,
    this.selectedStatus,
    this.startDate,
    this.endDate,
    required this.onFilterChanged,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bộ lọc',
            style: TextStyle(
              fontSize: responsive.fontSize(16),
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 12),
          
          // Status filter
          Row(
            children: [
              Icon(
                Iconsax.status,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                'Trạng thái:',
                style: TextStyle(
                  fontSize: responsive.fontSize(14),
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Wrap(
                  spacing: 8,
                  children: [
                    _buildStatusChip(
                      context,
                      'Tất cả',
                      null,
                      selectedStatus == null,
                      Colors.grey,
                    ),
                    _buildStatusChip(
                      context,
                      'Chờ duyệt',
                      ManualAttendanceStatus.pending,
                      selectedStatus == ManualAttendanceStatus.pending,
                      Colors.orange,
                    ),
                    _buildStatusChip(
                      context,
                      'Đã duyệt',
                      ManualAttendanceStatus.approved,
                      selectedStatus == ManualAttendanceStatus.approved,
                      Colors.green,
                    ),
                    _buildStatusChip(
                      context,
                      'Từ chối',
                      ManualAttendanceStatus.rejected,
                      selectedStatus == ManualAttendanceStatus.rejected,
                      Colors.red,
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Date range filter
          Row(
            children: [
              Icon(
                Iconsax.calendar,
                size: 16,
                color: Colors.grey.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                'Thời gian:',
                style: TextStyle(
                  fontSize: responsive.fontSize(14),
                  fontWeight: FontWeight.w500,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: _buildDateButton(
                        context,
                        startDate != null 
                            ? '${startDate!.day}/${startDate!.month}/${startDate!.year}'
                            : 'Từ ngày',
                        () => _selectStartDate(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '-',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildDateButton(
                        context,
                        endDate != null 
                            ? '${endDate!.day}/${endDate!.month}/${endDate!.year}'
                            : 'Đến ngày',
                        () => _selectEndDate(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () {
                        onFilterChanged(
                          status: selectedStatus,
                          startDate: null,
                          endDate: null,
                        );
                      },
                      icon: Icon(
                        Icons.clear,
                        size: 20,
                        color: Colors.grey.shade600,
                      ),
                      tooltip: 'Xóa bộ lọc ngày',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(
    BuildContext context,
    String label,
    ManualAttendanceStatus? status,
    bool isSelected,
    Color color,
  ) {
    final responsive = Responsive.of(context);
    
    return FilterChip(
      label: Text(
        label,
        style: TextStyle(
          fontSize: responsive.fontSize(12),
          color: isSelected ? Colors.white : color,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onSelected: (_) {
        onFilterChanged(
          status: status,
          startDate: startDate,
          endDate: endDate,
        );
      },
      backgroundColor: color.withOpacity(0.1),
      selectedColor: color,
      checkmarkColor: Colors.white,
      side: BorderSide(color: color.withOpacity(0.3)),
    );
  }

  Widget _buildDateButton(BuildContext context, String text, VoidCallback onTap) {
    final responsive = Responsive.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: responsive.fontSize(12),
            color: Colors.grey.shade700,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Future<void> _selectStartDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: startDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      onFilterChanged(
        status: selectedStatus,
        startDate: date,
        endDate: endDate,
      );
    }
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: endDate ?? DateTime.now(),
      firstDate: startDate ?? DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
    );

    if (date != null) {
      onFilterChanged(
        status: selectedStatus,
        startDate: startDate,
        endDate: date,
      );
    }
  }
}
