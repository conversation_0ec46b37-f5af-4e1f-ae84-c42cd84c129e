import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/user_profile.dart';
import '../repositories/profile_repository.dart';

class UpdateUserProfile {
  final ProfileRepository repository;

  UpdateUserProfile(this.repository);

  Future<Either<Failure, UserProfile>> call({
    required String name,
    required String email,
    required String phone,
    String? avatarUrl,
  }) async {
    return await repository.updateUserProfile(
      name: name,
      email: email,
      phone: phone,
      avatarUrl: avatarUrl,
    );
  }
}
