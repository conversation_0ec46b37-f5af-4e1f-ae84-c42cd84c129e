import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/calendar_repository.dart';

class GetCalendarSummary implements UseCase<Map<String, dynamic>, NoParams> {
  final CalendarRepository repository;

  GetCalendarSummary(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(NoParams params) async {
    return await repository.getCalendarSummary();
  }
}
