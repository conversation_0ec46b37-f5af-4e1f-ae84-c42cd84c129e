import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/admin_user_entity.dart';

class AdminUserDataGrid extends StatefulWidget {
  final List<AdminUserEntity> users;
  final bool isLoading;
  final List<String> selectedUserIds;
  final Function(AdminUserEntity) onUserTap;
  final Function(String) onUserSelect;
  final Function(String action, AdminUserEntity user) onUserAction;

  const AdminUserDataGrid({
    super.key,
    required this.users,
    required this.isLoading,
    required this.selectedUserIds,
    required this.onUserTap,
    required this.onUserSelect,
    required this.onUserAction,
  });

  @override
  State<AdminUserDataGrid> createState() => _AdminUserDataGridState();
}

class _AdminUserDataGridState extends State<AdminUserDataGrid> {
  late AdminUserDataSource _dataSource;
  final DataGridController _dataGridController = DataGridController();

  @override
  void initState() {
    super.initState();
    _dataSource = AdminUserDataSource(
      users: widget.users,
      selectedUserIds: widget.selectedUserIds,
      onUserSelect: widget.onUserSelect,
      onUserAction: widget.onUserAction,
    );
  }

  @override
  void didUpdateWidget(AdminUserDataGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.users != widget.users ||
        oldWidget.selectedUserIds != widget.selectedUserIds) {
      _dataSource.updateData(
        users: widget.users,
        selectedUserIds: widget.selectedUserIds,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white, // Set white background
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        child: SfDataGrid(
          source: _dataSource,
          controller: _dataGridController,
          allowSorting: true,
          allowMultiColumnSorting: true,
          selectionMode: SelectionMode.multiple,
          checkboxColumnSettings: DataGridCheckboxColumnSettings(
            showCheckboxOnHeader: true,
            width: responsive.adaptiveValue<double>(
              mobile: 50,
              tablet: 60,
              mobileLandscape: 55,
              tabletLandscape: 65,
            ),
          ),
          gridLinesVisibility: GridLinesVisibility.both,
          headerGridLinesVisibility: GridLinesVisibility.both,
          columnWidthMode: ColumnWidthMode.auto,
          headerRowHeight: responsive.adaptiveValue<double>(
            mobile: 50,
            tablet: 60,
            mobileLandscape: 55,
            tabletLandscape: 65,
          ),
          rowHeight: responsive.adaptiveValue<double>(
            mobile: 60,
            tablet: 70,
            mobileLandscape: 65,
            tabletLandscape: 75,
          ),
          columns: _buildColumns(context),
          onCellTap: (details) {
            if (details.rowColumnIndex.rowIndex > 0) {
              final user = widget.users[details.rowColumnIndex.rowIndex - 1];
              widget.onUserTap(user);
            }
          },
        ),
      ),
    );
  }

  List<GridColumn> _buildColumns(BuildContext context) {
    final responsive = context.responsive;

    return [
      // Index/Number Column
      GridColumn(
        columnName: 'index',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            '#',
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 50,
          tablet: 60,
          mobileLandscape: 55,
          tabletLandscape: 65,
        ),
        allowSorting: false,
      ),

      // Avatar & Name Column
      GridColumn(
        columnName: 'user',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'User', // l10n.user,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 180,
          tablet: 250,
          mobileLandscape: 220,
          tabletLandscape: 280,
        ),
      ),

      // Email Column
      GridColumn(
        columnName: 'email',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Email', // l10n.email,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 150,
          tablet: 200,
          mobileLandscape: 180,
          tabletLandscape: 220,
        ),
      ),

      // Role Column
      GridColumn(
        columnName: 'role',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Role', // l10n.role,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 100,
          tablet: 120,
          mobileLandscape: 110,
          tabletLandscape: 130,
        ),
      ),

      // Department Column
      GridColumn(
        columnName: 'department',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Department', // l10n.department,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 120,
          tablet: 150,
          mobileLandscape: 135,
          tabletLandscape: 165,
        ),
      ),

      // Position Column
      GridColumn(
        columnName: 'position',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Position', // l10n.position,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 120,
          tablet: 150,
          mobileLandscape: 135,
          tabletLandscape: 165,
        ),
      ),

      // Status Column
      GridColumn(
        columnName: 'status',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Status', // l10n.status,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 100,
          tablet: 120,
          mobileLandscape: 110,
          tabletLandscape: 130,
        ),
      ),

      // Created Date Column
      GridColumn(
        columnName: 'createdAt',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            border: Border(
              right: BorderSide(color: Colors.grey.shade300, width: 1),
            ),
          ),
          child: Text(
            'Created', // l10n.created,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 80,
          tablet: 100,
          mobileLandscape: 90,
          tabletLandscape: 110,
        ),
      ),

      // Actions Column
      GridColumn(
        columnName: 'actions',
        label: Container(
          padding: responsive.padding(all: 8),
          alignment: Alignment.center,
          decoration: BoxDecoration(color: Colors.grey.shade50),
          child: Text(
            'Actions', // l10n.actions,
            style: AppTextStyle.bold(
              context,
              size: 14,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        width: responsive.adaptiveValue<double>(
          mobile: 160,
          tablet: 170,
          mobileLandscape: 140,
          tabletLandscape: 180,
        ),
        allowSorting: false,
      ),
    ];
  }

  @override
  void dispose() {
    _dataGridController.dispose();
    super.dispose();
  }
}

class AdminUserDataSource extends DataGridSource {
  List<AdminUserEntity> _users = [];
  List<String> _selectedUserIds = [];
  final Function(String) onUserSelect;
  final Function(String action, AdminUserEntity user) onUserAction;

  List<DataGridRow> _dataGridRows = [];

  @override
  List<DataGridRow> get rows => _dataGridRows;

  AdminUserDataSource({
    required List<AdminUserEntity> users,
    required List<String> selectedUserIds,
    required this.onUserSelect,
    required this.onUserAction,
  }) {
    updateData(users: users, selectedUserIds: selectedUserIds);
  }

  void updateData({
    required List<AdminUserEntity> users,
    required List<String> selectedUserIds,
  }) {
    _users = users;
    _selectedUserIds = selectedUserIds;
    buildDataGridRows();
    notifyListeners();
  }

  void buildDataGridRows() {
    _dataGridRows = _users.asMap().entries.map<DataGridRow>((entry) {
      final index = entry.key + 1; // Start from 1
      final user = entry.value;

      return DataGridRow(
        cells: [
          DataGridCell<int>(columnName: 'index', value: index),
          DataGridCell<AdminUserEntity>(columnName: 'user', value: user),
          DataGridCell<String>(columnName: 'email', value: user.email),
          DataGridCell<String>(columnName: 'role', value: user.role.name),
          DataGridCell<String>(
            columnName: 'department',
            value: user.department ?? '',
          ),
          DataGridCell<String>(
            columnName: 'position',
            value: user.position ?? '',
          ),
          DataGridCell<String>(columnName: 'status', value: user.statusText),
          DataGridCell<DateTime>(
            columnName: 'createdAt',
            value: user.createdAt,
          ),
          DataGridCell<AdminUserEntity>(columnName: 'actions', value: user),
        ],
      );
    }).toList();
  }

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    final user =
        row.getCells().where((cell) => cell.columnName == 'user').first.value
            as AdminUserEntity;
    final isSelected = _selectedUserIds.contains(user.id);

    return DataGridRowAdapter(
      color: isSelected ? Colors.blue.withValues(alpha: 0.1) : null,
      cells: row.getCells().map<Widget>((cell) {
        switch (cell.columnName) {
          case 'index':
            return _buildIndexCell(cell.value as int);
          case 'user':
            return _buildUserCell(user);
          case 'email':
            return _buildTextCell(user.email);
          case 'role':
            return _buildRoleCell(user);
          case 'department':
            return _buildTextCell(user.department ?? '-');
          case 'position':
            return _buildTextCell(user.position ?? '-');
          case 'status':
            return _buildStatusCell(user);
          case 'createdAt':
            return _buildDateCell(user.createdAt);
          case 'actions':
            return _buildActionsCell(user);
          default:
            return Container();
        }
      }).toList(),
    );
  }

  Widget _buildIndexCell(int index) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Text(
        index.toString(),
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildUserCell(AdminUserEntity user) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Selection Checkbox
          GestureDetector(
            onTap: () => onUserSelect(user.id),
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: _selectedUserIds.contains(user.id)
                      ? Colors.blue
                      : Colors.grey,
                  width: 2,
                ),
                color: _selectedUserIds.contains(user.id)
                    ? Colors.blue
                    : Colors.transparent,
              ),
              child: _selectedUserIds.contains(user.id)
                  ? const Icon(Icons.check, size: 14, color: Colors.white)
                  : null,
            ),
          ),

          const SizedBox(width: 12),

          // Avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: _getRoleColor(user.role.name),
            backgroundImage: user.avatar != null
                ? NetworkImage(user.avatar!)
                : null,
            child: user.avatar == null
                ? Text(
                    user.initials,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  )
                : null,
          ),

          const SizedBox(width: 12),

          // Name
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  user.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (user.phone != null) ...[
                  const SizedBox(height: 1),
                  Text(
                    user.phone!,
                    style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextCell(String text) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 14, color: Colors.black87),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildRoleCell(AdminUserEntity user) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: _getRoleColor(user.role.name).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _getRoleColor(user.role.name).withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          user.role.name.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: _getRoleColor(user.role.name),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusCell(AdminUserEntity user) {
    Color color;
    IconData icon;
    String text;

    if (user.isdeleted) {
      color = Colors.red;
      icon = Icons.delete_outline;
      text = 'Deleted';
    } else if (user.isdisable) {
      color = Colors.orange;
      icon = Icons.block;
      text = 'Disabled';
    } else {
      color = Colors.green;
      icon = Icons.check_circle_outline;
      text = 'Active';
    }

    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 12, color: color),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateCell(DateTime date) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Text(
        '${date.day}/${date.month}/${date.year}',
        style: const TextStyle(fontSize: 12, color: Colors.black87),
      ),
    );
  }

  Widget _buildActionsCell(AdminUserEntity user) {
    return Container(
      padding: const EdgeInsets.all(4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Edit Button
          IconButton(
            onPressed: () => onUserAction('edit', user),
            icon: const Icon(Iconsax.edit, size: 14),
            tooltip: 'Edit User',
            constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
            padding: EdgeInsets.zero,
          ),

          // Toggle Status Button
          IconButton(
            onPressed: user.isdeleted
                ? null
                : () => onUserAction('toggle_status', user),
            icon: Icon(
              user.isdisable ? Iconsax.play : Iconsax.pause,
              size: 14,
              color: user.isdeleted
                  ? Colors.grey
                  : (user.isdisable ? Colors.green : Colors.orange),
            ),
            tooltip: user.isdisable ? 'Enable' : 'Disable',
            constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
            padding: EdgeInsets.zero,
          ),

          // Delete/Restore Button
          IconButton(
            onPressed: () =>
                onUserAction(user.isdeleted ? 'restore' : 'delete', user),
            icon: Icon(
              user.isdeleted ? Iconsax.refresh : Iconsax.trash,
              size: 14,
              color: user.isdeleted ? Colors.green : Colors.red,
            ),
            tooltip: user.isdeleted ? 'Restore' : 'Delete',
            constraints: const BoxConstraints(minWidth: 28, minHeight: 28),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'hr':
        return Colors.purple;
      case 'manager':
        return Colors.blue;
      case 'user':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  int compare(DataGridRow? a, DataGridRow? b, SortColumnDetails sortColumn) {
    if (a == null || b == null) return 0;

    final aValue = a
        .getCells()
        .firstWhere((cell) => cell.columnName == sortColumn.name)
        .value;
    final bValue = b
        .getCells()
        .firstWhere((cell) => cell.columnName == sortColumn.name)
        .value;

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) {
      return sortColumn.sortDirection == DataGridSortDirection.ascending
          ? -1
          : 1;
    }
    if (bValue == null) {
      return sortColumn.sortDirection == DataGridSortDirection.ascending
          ? 1
          : -1;
    }

    int result = 0;
    switch (sortColumn.name) {
      case 'user':
        final userA = aValue as AdminUserEntity;
        final userB = bValue as AdminUserEntity;
        result = userA.fullname.toLowerCase().compareTo(
          userB.fullname.toLowerCase(),
        );
        break;
      case 'email':
        result = (aValue as String).toLowerCase().compareTo(
          (bValue as String).toLowerCase(),
        );
        break;
      case 'role':
        result = (aValue as String).toLowerCase().compareTo(
          (bValue as String).toLowerCase(),
        );
        break;
      case 'department':
        final deptA = (aValue as String).isEmpty ? 'zzz' : aValue.toLowerCase();
        final deptB = (bValue as String).isEmpty ? 'zzz' : bValue.toLowerCase();
        result = deptA.compareTo(deptB);
        break;
      case 'position':
        final posA = (aValue as String).isEmpty ? 'zzz' : aValue.toLowerCase();
        final posB = (bValue as String).isEmpty ? 'zzz' : bValue.toLowerCase();
        result = posA.compareTo(posB);
        break;
      case 'status':
        result = (aValue as String).toLowerCase().compareTo(
          (bValue as String).toLowerCase(),
        );
        break;
      case 'createdAt':
        result = (aValue as DateTime).compareTo(bValue as DateTime);
        break;
      default:
        result = aValue.toString().toLowerCase().compareTo(
          bValue.toString().toLowerCase(),
        );
    }

    return sortColumn.sortDirection == DataGridSortDirection.ascending
        ? result
        : -result;
  }
}
