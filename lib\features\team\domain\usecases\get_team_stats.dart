import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/team_repository.dart';

class GetTeamStats implements UseCase<Map<String, dynamic>, GetTeamStatsParams> {
  final TeamRepository repository;

  GetTeamStats(this.repository);

  @override
  Future<Either<Failure, Map<String, dynamic>>> call(GetTeamStatsParams params) async {
    return await repository.getTeamStats(params.teamId);
  }
}

class GetTeamStatsParams extends Equatable {
  final String teamId;

  const GetTeamStatsParams({required this.teamId});

  @override
  List<Object> get props => [teamId];
}
