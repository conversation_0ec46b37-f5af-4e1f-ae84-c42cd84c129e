import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/leave_request.dart';
import '../../domain/repositories/leave_admin_repository.dart';
import '../datasources/leave_admin_remote_data_source.dart';

class LeaveAdminRepositoryImpl implements LeaveAdminRepository {
  final LeaveAdminRemoteDataSource remoteDataSource;

  LeaveAdminRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<LeaveRequest>>> getAllLeaveRequests({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      print(
        '📦 [LEAVE_ADMIN_REPO] getAllLeaveRequests - page: $page, limit: $limit, status: $status',
      );

      final requests = await remoteDataSource.getAllLeaveRequests(
        page: page,
        limit: limit,
        status: status,
      );

      print('📦 [LEAVE_ADMIN_REPO] Successfully fetched ${requests.length} requests');
      return Right(requests);
    } on ServerException catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] ServerException: ${e.message}');
      return Left(ServerFailure(e.message));
    } catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] Unexpected error: $e');
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, LeaveRequest>> approveLeaveRequest(
    String requestId,
  ) async {
    try {
      print('📦 [LEAVE_ADMIN_REPO] approveLeaveRequest - requestId: $requestId');

      final request = await remoteDataSource.approveLeaveRequest(requestId);

      print('📦 [LEAVE_ADMIN_REPO] Successfully approved request: $requestId');
      return Right(request);
    } on ServerException catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] Approve ServerException: ${e.message}');
      return Left(ServerFailure(e.message));
    } catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] Approve unexpected error: $e');
      return Left(ServerFailure('Failed to approve leave request'));
    }
  }

  @override
  Future<Either<Failure, LeaveRequest>> rejectLeaveRequest(
    String requestId,
    String rejectionReason,
  ) async {
    try {
      print(
        '📦 [LEAVE_ADMIN_REPO] rejectLeaveRequest - requestId: $requestId, reason: $rejectionReason',
      );

      final request = await remoteDataSource.rejectLeaveRequest(
        requestId,
        rejectionReason,
      );

      print('📦 [LEAVE_ADMIN_REPO] Successfully rejected request: $requestId');
      return Right(request);
    } on ServerException catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] Reject ServerException: ${e.message}');
      return Left(ServerFailure(e.message));
    } catch (e) {
      print('❌ [LEAVE_ADMIN_REPO] Reject unexpected error: $e');
      return Left(ServerFailure('Failed to reject leave request'));
    }
  }
}
