import 'package:flutter/material.dart';


/// Calendar-specific validators
class CalendarValidators {
  CalendarValidators._();

  /// Validates event title
  static String? validateEventTitle(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Event title is required';
    }
    if (value.trim().length < 3) {
      return 'Event title must be at least 3 characters';
    }
    if (value.trim().length > 100) {
      return 'Event title must not exceed 100 characters';
    }
    return null;
  }

  /// Validates event description
  static String? validateEventDescription(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 500) {
        return 'Event description must not exceed 500 characters';
      }
    }
    return null;
  }

  /// Validates event location
  static String? validateEventLocation(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 200) {
        return 'Event location must not exceed 200 characters';
      }
    }
    return null;
  }

  /// Validates event date range
  static String? validateEventDateRange(DateTime startDate, DateTime endDate, BuildContext context) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);
    
    // Check if start date is not in the past
    if (startDateOnly.isBefore(today)) {
      return 'Event start date cannot be in the past';
    }
    
    // Check if end date is after start date
    if (endDate.isBefore(startDate)) {
      return 'Event end date must be after start date';
    }
    
    return null;
  }

  /// Validates event time range
  static String? validateEventTimeRange(DateTime startTime, DateTime endTime, BuildContext context, {bool isAllDay = false}) {
    if (isAllDay) {
      return null; // No time validation needed for all-day events
    }
    
    if (endTime.isBefore(startTime)) {
      return 'End time must be after start time';
    }
    
    final duration = endTime.difference(startTime);
    if (duration.inMinutes < 15) {
      return 'Event duration must be at least 15 minutes';
    }
    
    if (duration.inDays > 7) {
      return 'Event duration cannot exceed 7 days';
    }
    
    return null;
  }

  /// Validates event attendees
  static String? validateEventAttendees(List<String> attendees, BuildContext context) {
    if (attendees.length > 50) {
      return 'Cannot add more than 50 attendees to an event';
    }
    return null;
  }

  /// Validates recurring event settings
  static String? validateRecurringEvent(bool isRecurring, String? recurrenceRule, BuildContext context) {
    if (isRecurring && (recurrenceRule == null || recurrenceRule.trim().isEmpty)) {
      return 'Recurrence rule is required for recurring events';
    }
    return null;
  }

  /// Comprehensive event validation
  static List<String> validateCompleteEvent({
    required String? title,
    required String? description,
    required String? location,
    required DateTime startTime,
    required DateTime endTime,
    required List<String> attendees,
    required bool isAllDay,
    required bool isRecurring,
    String? recurrenceRule,
    required BuildContext context,
  }) {
    final errors = <String>[];

    // Validate title
    final titleError = validateEventTitle(title, context);
    if (titleError != null) errors.add(titleError);

    // Validate description
    final descriptionError = validateEventDescription(description, context);
    if (descriptionError != null) errors.add(descriptionError);

    // Validate location
    final locationError = validateEventLocation(location, context);
    if (locationError != null) errors.add(locationError);

    // Validate date range
    final dateRangeError = validateEventDateRange(startTime, endTime, context);
    if (dateRangeError != null) errors.add(dateRangeError);

    // Validate time range
    final timeRangeError = validateEventTimeRange(startTime, endTime, context, isAllDay: isAllDay);
    if (timeRangeError != null) errors.add(timeRangeError);

    // Validate attendees
    final attendeesError = validateEventAttendees(attendees, context);
    if (attendeesError != null) errors.add(attendeesError);

    // Validate recurring settings
    final recurringError = validateRecurringEvent(isRecurring, recurrenceRule, context);
    if (recurringError != null) errors.add(recurringError);

    return errors;
  }
}
