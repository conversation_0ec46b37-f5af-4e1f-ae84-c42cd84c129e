import 'package:equatable/equatable.dart';


import '../entities/calendar_event.dart'; // Để dùng enum

class CreateEventParams extends Equatable {
  final String title;
  final String? description;
  final DateTime startTime;
  final DateTime endTime;
  final bool isAllDay;
  final CalendarEventType type; // Dùng enum để an toàn kiểu
  final String? color;
  final String? location;
  final List<String>? attendees; // Mảng các ID của user
  final bool? isRecurring;
  final String? recurrenceRule;

  const CreateEventParams({
    required this.title,
    this.description,
    required this.startTime,
    required this.endTime,
    required this.isAllDay,
    required this.type,
    this.color,
    this.location,
    this.attendees,
    this.isRecurring,
    this.recurrenceRule,
  });

  /// Chuyển đổi object này thành một Map để gửi đi dưới dạng JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      if (description != null) 'description': description,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'isAllDay': isAllDay,
      'type': type.name, // Chuyển enum thành chuỗi (vd: CalendarEventType.meeting -> "meeting")
      if (color != null) 'color': color,
      if (location != null) 'location': location,
      if (attendees != null) 'attendees': attendees,
      if (isRecurring != null) 'isRecurring': isRecurring,
      if (recurrenceRule != null) 'recurrenceRule': recurrenceRule,
    };
  }

  @override
  List<Object?> get props => [
    title,
    description,
    startTime,
    endTime,
    isAllDay,
    type,
    color,
    location,
    attendees,
    isRecurring,
    recurrenceRule,
  ];
}