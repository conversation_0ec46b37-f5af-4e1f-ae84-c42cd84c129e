import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../shared/widgets/action_button.dart';
import '../../../../shared/widgets/action_item_model.dart';

class HomeTopActions extends StatelessWidget {
  final List<ActionItem> actions;

  const HomeTopActions({super.key, required this.actions});

  @override
  Widget build(BuildContext context) {
    if (actions.length < 4) {
      return Center(child: Text(context.l10n.homeErrorFunction));
    }

    // GridView chỉ có 4 mục
    return Container(
      padding: context.responsive.padding(all: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300, width: 1.5),
      ),
      child: GridView.count(
        crossAxisCount: 4,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,

        children: actions
            .take(4)
            .map((item) => ActionButton(item: item))
            .toList(),
      ),
    );
  }
}
