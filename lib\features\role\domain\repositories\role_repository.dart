import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/role_entity.dart';

abstract class RoleRepository {
  /// Get all roles with pagination and filtering
  Future<Either<Failure, RoleListResult>> getAllRoles({
    int page = 1,
    int limit = 10,
    String? search,
  });

  /// Get role by ID
  Future<Either<Failure, RoleEntity>> getRoleById(String roleId);

  /// Create a new role
  Future<Either<Failure, RoleEntity>> createRole({required String name});

  /// Update role information
  Future<Either<Failure, RoleEntity>> updateRole(String roleId, {String? name});

  /// Delete role (soft delete)
  Future<Either<Failure, void>> deleteRole(String roleId);

  /// Get roles for dropdown (active roles only)
  Future<Either<Failure, List<RoleEntity>>> getRolesForDropdown();

  /// Check if role name exists
  Future<Either<Failure, bool>> checkRoleNameExists(
    String name, {
    String? excludeId,
  });
}
