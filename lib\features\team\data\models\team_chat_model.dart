import '../../domain/entities/team_chat_entity.dart';
import '../../domain/entities/team_entity.dart';

class TeamChatModel extends TeamChatEntity {
  const TeamChatModel({
    required super.id,
    required super.teamId,
    required super.senderId,
    required super.message,
    required super.type,
    super.attachments,
    super.mentions,
    super.replyTo,
    super.isPinned = false,
    super.isEdited = false,
    super.editedAt,
    super.reactions,
    super.createdAt,
    super.updatedAt,
  });

  factory TeamChatModel.fromJson(Map<String, dynamic> json) {
    return TeamChatModel(
      id: json['_id'] ?? json['id'],
      teamId: json['teamId'],
      senderId: json['senderId'] is Map
          ? TeamMemberModel.fromJson(json['senderId'])
          : json['senderId'],
      message: json['message'],
      type: ChatMessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ChatMessageType.text,
      ),
      attachments: (json['attachments'] as List?)
          ?.map((e) => ChatAttachmentModel.fromJson(e))
          .toList() ?? [],
      mentions: List<String>.from(json['mentions'] ?? []),
      replyTo: json['replyTo'],
      isPinned: json['isPinned'] ?? false,
      isEdited: json['isEdited'] ?? false,
      editedAt: json['editedAt'] != null ? DateTime.parse(json['editedAt']) : null,
      reactions: (json['reactions'] as List?)
          ?.map((e) => ChatReactionModel.fromJson(e))
          .toList() ?? [],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teamId': teamId,
      'senderId': senderId is TeamMemberEntity
          ? (senderId as TeamMemberEntity).id
          : senderId,
      'message': message,
      'type': type.name,
      'attachments': attachments?.map((e) => (e as ChatAttachmentModel).toJson()).toList(),
      'mentions': mentions,
      'replyTo': replyTo,
      'isPinned': isPinned,
      'isEdited': isEdited,
      'editedAt': editedAt?.toIso8601String(),
      'reactions': reactions?.map((e) => (e as ChatReactionModel).toJson()).toList(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  TeamChatModel copyWith({
    String? id,
    String? teamId,
    dynamic senderId,
    String? message,
    ChatMessageType? type,
    List<ChatAttachmentEntity>? attachments,
    List<String>? mentions,
    String? replyTo,
    bool? isPinned,
    bool? isEdited,
    DateTime? editedAt,
    List<ChatReactionEntity>? reactions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TeamChatModel(
      id: id ?? this.id,
      teamId: teamId ?? this.teamId,
      senderId: senderId ?? this.senderId,
      message: message ?? this.message,
      type: type ?? this.type,
      attachments: attachments ?? this.attachments,
      mentions: mentions ?? this.mentions,
      replyTo: replyTo ?? this.replyTo,
      isPinned: isPinned ?? this.isPinned,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      reactions: reactions ?? this.reactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class ChatAttachmentModel extends ChatAttachmentEntity {
  const ChatAttachmentModel({
    required super.fileName,
    required super.fileUrl,
    required super.fileType,
    required super.fileSize,
  });

  factory ChatAttachmentModel.fromJson(Map<String, dynamic> json) {
    return ChatAttachmentModel(
      fileName: json['fileName'],
      fileUrl: json['fileUrl'],
      fileType: json['fileType'],
      fileSize: json['fileSize'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'fileSize': fileSize,
    };
  }
}

class ChatReactionModel extends ChatReactionEntity {
  const ChatReactionModel({
    required super.userId,
    required super.emoji,
    required super.createdAt,
  });

  factory ChatReactionModel.fromJson(Map<String, dynamic> json) {
    return ChatReactionModel(
      userId: json['userId'],
      emoji: json['emoji'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'emoji': emoji,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class TeamMemberModel extends TeamMemberEntity {
  const TeamMemberModel({
    required super.id,
    required super.fullname,
    super.avatar,
    super.email,
    super.department,
    super.position,
    super.role,
    super.joinedAt,
    super.isActive = true,
  });

  factory TeamMemberModel.fromJson(Map<String, dynamic> json) {
    return TeamMemberModel(
      id: json['_id'] ?? json['id'],
      fullname: json['fullname'],
      avatar: json['avatar'],
      email: json['email'],
      department: json['department'],
      position: json['position'],
      role: json['role'],
      joinedAt: json['joinedAt'] != null ? DateTime.parse(json['joinedAt']) : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'avatar': avatar,
      'email': email,
      'department': department,
      'position': position,
      'role': role,
      'joinedAt': joinedAt?.toIso8601String(),
      'isActive': isActive,
    };
  }
}
