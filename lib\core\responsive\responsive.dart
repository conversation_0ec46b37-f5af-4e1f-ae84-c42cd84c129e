import 'package:flutter/widgets.dart';

class Responsive {
  final BuildContext context;

  Responsive(this.context);

  // --- CÁC HẰNG SỐ CHO LANDSCAPE SCALE ---
  static const double mobileLandscapeScaleFactor =
      0.7; // Giảm 0.7 lần cho mobile landscape
  static const double tabletLandscapeScaleFactor =
      1.5; // Tăng 1.3 lần cho tablet landscape

  // --- CÁC GETTER CƠ BẢN ---
  Size get screenSize => MediaQuery.of(context).size;

  double get width => screenSize.width;

  double get height => screenSize.height;

  Orientation get orientation => MediaQuery.of(context).orientation;

  bool get isLandscape => orientation == Orientation.landscape;

  // --- BREAKPOINTS ---
  static const double mobileBreakpointPortrait = 600;
  static const double tabletBreakpointPortrait = 900;
  static const double mobileBreakpointLandscape = 950;
  static const double tabletBreakpointLandscape = 1300;

  bool get isMobile => isLandscape
      ? width < mobileBreakpointLandscape
      : width < mobileBreakpointPortrait;

  bool get isTablet => isLandscape
      ? width >= mobileBreakpointLandscape && width < tabletBreakpointLandscape
      : width >= mobileBreakpointPortrait && width < tabletBreakpointPortrait;

  // --- KÍCH THƯỚC GỐC ---
  static const double baseWidthPortrait = 375;
  static const double baseHeightPortrait = 812;

  /// Scale kích thước theo chiều rộng, điều chỉnh theo landscape.
  double scaleWidth(double size, {double? landscapeScaleFactor}) {
    final baseScale = (size / baseWidthPortrait) * width;
    if (!isLandscape) return baseScale;

    // Sử dụng adaptiveValue để chọn hệ số
    final scaleFactor = adaptiveValue<double>(
      mobile: 1.0,
      tablet: 1.0,
      mobileLandscape: landscapeScaleFactor ?? mobileLandscapeScaleFactor,
      tabletLandscape: landscapeScaleFactor ?? tabletLandscapeScaleFactor,
    );
    return baseScale * scaleFactor;
  }

  /// Scale kích thước theo chiều cao, điều chỉnh theo landscape.
  double scaleHeight(double size, {double? landscapeScaleFactor}) {
    final baseScale = (size / baseHeightPortrait) * height;
    if (!isLandscape) return baseScale;

    // Sử dụng adaptiveValue để chọn hệ số
    final scaleFactor = adaptiveValue<double>(
      mobile: 1.0,
      tablet: 1.0,
      mobileLandscape: landscapeScaleFactor ?? mobileLandscapeScaleFactor,
      tabletLandscape: landscapeScaleFactor ?? tabletLandscapeScaleFactor,
    );
    return baseScale * scaleFactor;
  }

  /// Scale font chữ, điều chỉnh theo landscape.
  double fontSize(double size, {double? landscapeScaleFactor}) {
    final textScaler = MediaQuery.of(context).textScaler;
    final scaledSize = scaleWidth(
      size,
      landscapeScaleFactor: landscapeScaleFactor,
    );
    return textScaler.scale(scaledSize).clamp(size * 0.8, size * 2.0);
  }

  /// Scale radius, điều chỉnh theo landscape.
  double scaleRadius(double size, {double? landscapeScaleFactor}) {
    return scaleWidth(size, landscapeScaleFactor: landscapeScaleFactor);
  }

  double widthPercentage(double percent) => width * percent / 100;

  double heightPercentage(double percent) => height * percent / 100;

  EdgeInsets get safeAreaPadding => MediaQuery.of(context).padding;

  /// Responsive padding cho các màn hình khác nhau
  EdgeInsets get responsivePadding => EdgeInsets.symmetric(
    horizontal: adaptiveValue<double>(
      mobile: widthPercentage(4),
      tablet: widthPercentage(6),
      mobileLandscape: widthPercentage(8),
      tabletLandscape: widthPercentage(10),
    ),
    vertical: adaptiveValue<double>(
      mobile: heightPercentage(2),
      tablet: heightPercentage(3),
      mobileLandscape: heightPercentage(1.5),
      tabletLandscape: heightPercentage(2),
    ),
  );

  /// Responsive card padding
  EdgeInsets get cardPadding => EdgeInsets.all(
    adaptiveValue<double>(
      mobile: 16,
      tablet: 20,
      mobileLandscape: 18,
      tabletLandscape: 24,
    ),
  );

  /// Responsive spacing giữa các elements
  double get defaultSpacing => adaptiveValue<double>(
    mobile: 16,
    tablet: 20,
    mobileLandscape: 18,
    tabletLandscape: 24,
  );

  /// Responsive border radius
  double get defaultRadius => adaptiveValue<double>(
    mobile: 12,
    tablet: 16,
    mobileLandscape: 14,
    tabletLandscape: 18,
  );

  /// Grid columns cho responsive layout
  int get gridColumns => adaptiveValue<int>(
    mobile: 1,
    tablet: 2,
    mobileLandscape: 2,
    tabletLandscape: 3,
  );

  /// Responsive font sizes
  double get titleFontSize => adaptiveValue<double>(
    mobile: 24,
    tablet: 28,
    mobileLandscape: 26,
    tabletLandscape: 32,
  );

  double get subtitleFontSize => adaptiveValue<double>(
    mobile: 18,
    tablet: 20,
    mobileLandscape: 19,
    tabletLandscape: 22,
  );

  double get bodyFontSize => adaptiveValue<double>(
    mobile: 14,
    tablet: 16,
    mobileLandscape: 15,
    tabletLandscape: 18,
  );

  double get captionFontSize => adaptiveValue<double>(
    mobile: 12,
    tablet: 14,
    mobileLandscape: 13,
    tabletLandscape: 15,
  );

  EdgeInsets padding({
    double all = 0,
    double horizontal = 0,
    double vertical = 0,
    double left = 0,
    double right = 0,
    double top = 0,
    double bottom = 0,
    double? landscapeScaleFactor,
  }) {
    return EdgeInsets.only(
      left: scaleWidth(
        left == 0 ? (all == 0 ? horizontal : all) : left,
        landscapeScaleFactor: landscapeScaleFactor,
      ),
      right: scaleWidth(
        right == 0 ? (all == 0 ? horizontal : all) : right,
        landscapeScaleFactor: landscapeScaleFactor,
      ),
      top: scaleHeight(
        top == 0 ? (all == 0 ? vertical : all) : top,
        landscapeScaleFactor: landscapeScaleFactor,
      ),
      bottom: scaleHeight(
        bottom == 0 ? (all == 0 ? vertical : all) : bottom,
        landscapeScaleFactor: landscapeScaleFactor,
      ),
    );
  }

  // Hàm điều khiển landscape, yêu cầu các tham số bắt buộc
  T adaptiveValue<T>({
    required T mobile,
    required T tablet,
    required T? mobileLandscape,
    required T? tabletLandscape,
  }) {
    if (isLandscape) {
      if (isTablet && tabletLandscape != null) return tabletLandscape;
      if (isMobile) return mobileLandscape ?? mobile;
    }
    if (isTablet) return tablet;
    return mobile;
  }

  static Responsive of(BuildContext context) => Responsive(context);
}
