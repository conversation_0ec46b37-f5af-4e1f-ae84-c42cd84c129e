import 'package:dio/dio.dart';


import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../../../calendar/data/models/calendar_event_model.dart';
import '../../../calendar/domain/params/create_event_params.dart';
import '../../../calendar/domain/params/update_event_params.dart'; // Giả sử bạn có DioClient


// --- Interface ---
abstract class CalendarRemoteDataSource {
  Future<List<CalendarEventModel>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    String? type,
    int? page,
    int? limit,
  });

  Future<CalendarEventModel> getCalendarEventById(String eventId);

  Future<CalendarEventModel> createCalendarEvent(CreateEventParams params);

  Future<CalendarEventModel> updateCalendarEvent(String eventId, UpdateEventParams params);

  Future<void> deleteCalendarEvent(String eventId);

  // Additional endpoints to match backend
  Future<Map<String, dynamic>> getCalendarSummary();

  Future<List<CalendarEventModel>> getTodayEvents();

  Future<List<CalendarEventModel>> getUpcomingEvents({int? limit});

  Future<List<CalendarEventModel>> getWeeklyEvents({required DateTime weekStart});

  Future<List<CalendarEventModel>> checkEventConflicts({
    required DateTime startTime,
    required DateTime endTime,
    String? excludeEventId,
  });
}


// --- Implementation ---
class CalendarRemoteDataSourceImpl implements CalendarRemoteDataSource {
  final DioClient dioClient;

  CalendarRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<CalendarEventModel> createCalendarEvent(CreateEventParams params) async {
    try {
      final response = await dioClient.post(
        '/api/calendar',
        data: params.toJson(),
      );

      // Backend trả về data.event, không phải data trực tiếp
      return CalendarEventModel.fromJson(response.data['data']['event']);
    } on DioException catch (e) {

      final errorMessage = e.response?.data?['message'] ?? 'Failed to create event';
      throw ServerException( errorMessage);
    }
  }

  @override
  Future<List<CalendarEventModel>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    String? type,
    int? page,
    int? limit,
  }) async {
    final queryParams = <String, dynamic>{
      if (startDate != null) 'startDate': startDate.toIso8601String(),
      if (endDate != null) 'endDate': endDate.toIso8601String(),
      if (type != null) 'type': type,
      if (page != null) 'page': page,
      if (limit != null) 'limit': limit,
    };

    try {
      final response = await dioClient.get(
        '/api/calendar',
        queryParameters: queryParams,
      );
   
      final List<dynamic> eventsJson = response.data['data']['events'];
      return eventsJson.map((json) => CalendarEventModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch events';
      throw ServerException( errorMessage);
    }
  }

  @override
  Future<CalendarEventModel> getCalendarEventById(String eventId) async {
    try {
      final response = await dioClient.get('/api/calendar/$eventId');
      // Có thể backend trả về data.event cho single event
      return CalendarEventModel.fromJson(response.data['data']['event'] ?? response.data['data']);
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch event';
      throw ServerException( errorMessage);
    }
  }

  @override
  Future<CalendarEventModel> updateCalendarEvent(String eventId, UpdateEventParams params) async {
    try {
      final response = await dioClient.put( // Dùng PUT để match với backend
        '/api/calendar/$eventId',
        data: params.toJson(),
      );
      // Backend có thể trả về data.event cho update
      return CalendarEventModel.fromJson(response.data['data']['event'] ?? response.data['data']);
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to update event';
      throw ServerException(errorMessage);
    }
  }

  @override
  Future<void> deleteCalendarEvent(String eventId) async {
    try {
      await dioClient.delete('/api/calendar/$eventId');
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to delete event';
      throw ServerException( errorMessage);
    }
  }

  @override
  Future<Map<String, dynamic>> getCalendarSummary() async {
    try {
      final response = await dioClient.get('/api/calendar/summary');
      return response.data['data'] as Map<String, dynamic>;
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch calendar summary';
      throw ServerException(errorMessage);
    }
  }

  @override
  Future<List<CalendarEventModel>> getTodayEvents() async {
    try {
      final response = await dioClient.get('/api/calendar/today');
      final List<dynamic> eventsJson = response.data['data']['events'];
      return eventsJson.map((json) => CalendarEventModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch today events';
      throw ServerException(errorMessage);
    }
  }

  @override
  Future<List<CalendarEventModel>> getUpcomingEvents({int? limit}) async {
    try {
      final queryParams = <String, dynamic>{};
      if (limit != null) queryParams['limit'] = limit;

      final response = await dioClient.get(
        '/api/calendar/upcoming',
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );
      final List<dynamic> eventsJson = response.data['data']['events'];
      return eventsJson.map((json) => CalendarEventModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch upcoming events';
      throw ServerException(errorMessage);
    }
  }

  @override
  Future<List<CalendarEventModel>> getWeeklyEvents({required DateTime weekStart}) async {
    try {
      final response = await dioClient.get(
        '/api/calendar/weekly',
        queryParameters: {'weekStart': weekStart.toIso8601String()},
      );
      final List<dynamic> eventsJson = response.data['data']['events'];
      return eventsJson.map((json) => CalendarEventModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to fetch weekly events';
      throw ServerException(errorMessage);
    }
  }

  @override
  Future<List<CalendarEventModel>> checkEventConflicts({
    required DateTime startTime,
    required DateTime endTime,
    String? excludeEventId,
  }) async {
    try {
      final data = {
        'startTime': startTime.toIso8601String(),
        'endTime': endTime.toIso8601String(),
        if (excludeEventId != null) 'excludeEventId': excludeEventId,
      };

      final response = await dioClient.post('/api/calendar/conflicts', data: data);
      final List<dynamic> conflictsJson = response.data['data']['conflicts'];
      return conflictsJson.map((json) => CalendarEventModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage = e.response?.data?['message'] ?? 'Failed to check event conflicts';
      throw ServerException(errorMessage);
    }
  }
}