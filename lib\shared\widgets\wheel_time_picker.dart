import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';

class WheelTimePicker extends StatefulWidget {
  final TimeOfDay? initialTime;
  final String title;
  final Function(TimeOfDay) onTimeSelected;

  const WheelTimePicker({
    super.key,
    this.initialTime,
    required this.title,
    required this.onTimeSelected,
  });

  @override
  State<WheelTimePicker> createState() => _WheelTimePickerState();
}

class _WheelTimePickerState extends State<WheelTimePicker> {
  late FixedExtentScrollController hourController;
  late FixedExtentScrollController minuteController;
  late FixedExtentScrollController periodController;

  late int selectedHour;
  late int selectedMinute;
  late int selectedPeriod; // 0 for AM, 1 for PM

  @override
  void initState() {
    super.initState();
    final time = widget.initialTime ?? TimeOfDay.now();

    selectedHour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    selectedMinute = time.minute;
    selectedPeriod = time.period == DayPeriod.am ? 0 : 1;

    hourController = FixedExtentScrollController(initialItem: selectedHour - 1);
    minuteController = FixedExtentScrollController(initialItem: selectedMinute);
    periodController = FixedExtentScrollController(initialItem: selectedPeriod);
  }

  @override
  void dispose() {
    hourController.dispose();
    minuteController.dispose();
    periodController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: responsive.padding(all: 24.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryBlue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.access_time_rounded,
                    color: AppColors.primaryBlue,
                    size: 24,
                  ),
                ),
                SizedBox(width: responsive.widthPercentage(3.0)),
                Expanded(
                  child: Text(
                    widget.title,
                    style: AppTextStyle.bold(context, size: 18, color: AppColors.textPrimary),
                  ),
                ),
              ],
            ),
            SizedBox(height: responsive.heightPercentage(3.0)),

            // Time Picker Wheels
            Container(
              height: 200,
              padding: responsive.padding(all: 16.0),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  // Hour Wheel
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        Text(
                          context.l10n.hour,
                          style: AppTextStyle.medium(context, size: 12, color: AppColors.textSecondary),
                        ),
                        SizedBox(height: responsive.heightPercentage(1.0)),
                        Expanded(
                          child: CupertinoPicker(
                            scrollController: hourController,
                            itemExtent: 40,
                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedHour = index + 1;
                              });
                            },
                            selectionOverlay: Container(
                              decoration: BoxDecoration(
                                border: Border.symmetric(
                                  horizontal: BorderSide(
                                    color: AppColors.primaryBlue.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                            children: List.generate(12, (index) {
                              final hour = index + 1;
                              return Center(
                                child: Text(
                                  hour.toString(),
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: AppColors.textPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Separator
                  Padding(
                    padding: responsive.padding(horizontal: 8.0),
                    child: Text(
                      ':',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                  ),

                  // Minute Wheel
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        Text(
                          context.l10n.minute,
                          style: AppTextStyle.medium(context, size: 12, color: AppColors.textSecondary),
                        ),
                        SizedBox(height: responsive.heightPercentage(1.0)),
                        Expanded(
                          child: CupertinoPicker(
                            scrollController: minuteController,
                            itemExtent: 40,
                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedMinute = index;
                              });
                            },
                            selectionOverlay: Container(
                              decoration: BoxDecoration(
                                border: Border.symmetric(
                                  horizontal: BorderSide(
                                    color: AppColors.primaryBlue.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                            children: List.generate(60, (index) {
                              return Center(
                                child: Text(
                                  index.toString().padLeft(2, '0'),
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: AppColors.textPrimary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(width: responsive.widthPercentage(2.0)),

                  // AM/PM Wheel
                  Expanded(
                    flex: 1,
                    child: Column(
                      children: [
                        Text(
                          context.l10n.period,
                          style: AppTextStyle.medium(context, size: 12, color: AppColors.textSecondary),
                        ),
                        SizedBox(height: responsive.heightPercentage(1.0)),
                        Expanded(
                          child: CupertinoPicker(
                            scrollController: periodController,
                            itemExtent: 40,
                            onSelectedItemChanged: (index) {
                              setState(() {
                                selectedPeriod = index;
                              });
                            },
                            selectionOverlay: Container(
                              decoration: BoxDecoration(
                                border: Border.symmetric(
                                  horizontal: BorderSide(
                                    color: AppColors.primaryBlue.withValues(
                                      alpha: 0.3,
                                    ),
                                    width: 2,
                                  ),
                                ),
                              ),
                            ),
                            children: ['AM', 'PM'].map((period) {
                              return Center(
                                child: Text(
                                  period,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: AppColors.textPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: responsive.heightPercentage(3.0)),

            // Current Time Display
            Container(
              padding: responsive.padding(all: 16.0),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryBlue.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.schedule_rounded,
                    color: AppColors.primaryBlue,
                    size: 20,
                  ),
                  SizedBox(width: responsive.widthPercentage(2.0)),
                  Text(
                    'Selected: ${selectedHour.toString().padLeft(2, '0')}:${selectedMinute.toString().padLeft(2, '0')} ${selectedPeriod == 0 ? 'AM' : 'PM'}',
                    style: AppTextStyle.bold(context, size: 16, color: AppColors.primaryBlue),
                  ),
                ],
              ),
            ),

            SizedBox(height: responsive.heightPercentage(3.0)),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: TextButton.styleFrom(
                      padding: responsive.padding(vertical: 16.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: AppColors.textSecondary.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                    child: Text(
                      context.l10n.cancel,
                      style: AppTextStyle.medium(context, size: 16, color: AppColors.textSecondary),
                    ),
                  ),
                ),
                SizedBox(width: responsive.widthPercentage(3.0)),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _confirmTime,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      padding: responsive.padding(vertical: 16.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      context.l10n.select,
                      style: AppTextStyle.bold(context, size: 16, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _confirmTime() {
    int hour24 = selectedHour;
    if (selectedPeriod == 0 && selectedHour == 12) {
      // 12 AM = 0 hour
      hour24 = 0;
    } else if (selectedPeriod == 1 && selectedHour != 12) {
      // PM hours except 12 PM
      hour24 = selectedHour + 12;
    }

    final timeOfDay = TimeOfDay(hour: hour24, minute: selectedMinute);
    widget.onTimeSelected(timeOfDay);
    Navigator.of(context).pop();
  }
}
