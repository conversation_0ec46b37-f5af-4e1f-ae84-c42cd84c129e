import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../injection_container.dart';
import '../../domain/entities/user_for_dropdown_entity.dart';
import '../../domain/reponsitories/upload_face_repository.dart';
import '../cubit/upload_face_cubit.dart';
import '../cubit/upload_face_state.dart';

class UploadEmployeeFacePage extends StatelessWidget {
  const UploadEmployeeFacePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          UploadFaceCubit(repository: sl<UploadFaceRepository>())..fetchUsers(),
      child: const UploadEmployeeFaceView(),
    );
  }
}

class UploadEmployeeFaceView extends StatelessWidget {
  const UploadEmployeeFaceView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocListener<UploadFaceCubit, UploadFaceState>(
      // Listen buildWhen để tối ưu, chỉ rebuild khi cần
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status == UploadStatus.uploadSuccess) {
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: context.l10n.settingUploadImageSuccess,
          );
        } else if (state.status == UploadStatus.failure) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: "${state.errorMessage}",
            isError: true,
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey.shade50,
        appBar: _buildAppBar(context),
        body: BlocBuilder<UploadFaceCubit, UploadFaceState>(
          // buildWhen để tối ưu, không rebuild widget không cần thiết
          buildWhen: (previous, current) =>
              previous.imageFile != current.imageFile ||
              previous.selectedUser != current.selectedUser ||
              previous.status != current.status,
          builder: (context, state) {
            if (state.status == UploadStatus.loadingUsers) {
              return const Center(child: CircularProgressIndicator());
            }

            final isUploading = state.status == UploadStatus.uploading;
            final isButtonEnabled =
                state.selectedUser != null &&
                state.imageFile != null &&
                !isUploading;

            return SingleChildScrollView(
              padding: context.responsive.padding(all: 24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSectionTitle(
                    "1: ${context.l10n.settingChooseEmployee}",
                    // Sửa nhỏ: thêm space
                    context,
                  ),
                  const ResponsiveSpacer(mobileSize: 16, tabletSize: 18),
                  _buildUserDropdown(context, state.users, state.selectedUser),
                  const ResponsiveSpacer(mobileSize: 40, tabletSize: 42),
                  _buildSectionTitle(
                    "2: ${context.l10n.settingChooseAnEmployeeFromList}",
                    // Sửa nhỏ: thêm space
                    context,
                  ),
                  const ResponsiveSpacer(mobileSize: 16, tabletSize: 18),
                  _buildImagePicker(context, state.imageFile),
                  const ResponsiveSpacer(mobileSize: 60, tabletSize: 62),
                  _buildUploadButton(
                    context,
                    isEnabled: isButtonEnabled,
                    isUploading: isUploading,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Các hàm build khác không thay đổi...
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 1,
      backgroundColor: Colors.white,
      foregroundColor: Colors.blue.shade800,
      leading: IconButton(
        icon: const Icon(Iconsax.arrow_left_2),
        onPressed: () => Navigator.pop(context),
      ),
      title: Text(
        context.l10n.settingAddImage,
        style: context.lightTheme.textTheme.titleLarge!.copyWith(
          color: Colors.black87,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, BuildContext context) {
    return Text(
      title,
      style: context.lightTheme.textTheme.titleLarge!.copyWith(
        color: Colors.black87,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildUserDropdown(
    BuildContext context,
    List<UserForDropdownEntity> users,
    UserForDropdownEntity? selectedUser,
  ) {
    // Thêm key để Dropdown rebuild đúng khi selectedUser bị reset về null
    return DropdownButtonFormField<UserForDropdownEntity>(
      key: ValueKey(selectedUser?.id),
      value: selectedUser,
      hint: Text(context.l10n.settingChooseAnEmployeeFromList),
      isExpanded: true,
      isDense: false,
      decoration: InputDecoration(
        prefixIcon: Icon(Iconsax.user_octagon, color: Colors.grey.shade600),
        contentPadding: context.responsive.padding(vertical: 6, horizontal: 16),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: users.map((user) {
        return DropdownMenuItem<UserForDropdownEntity>(
          value: user,
          child: Padding(
            padding: context.responsive.padding(vertical: 1),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  user.fullname,
                  style: context.lightTheme.textTheme.titleMedium,
                ),
              ],
            ),
          ),
        );
      }).toList(),
      onChanged: (newValue) =>
          context.read<UploadFaceCubit>().userSelected(newValue),
    );
  }

  Widget _buildImagePicker(BuildContext context, File? imageFile) {
    return GestureDetector(
      onTap: () => context.read<UploadFaceCubit>().pickImage(),
      child: imageFile == null
          ? DottedBorder(
              options: RectDottedBorderOptions(
                dashPattern: const [8, 4],
                strokeWidth: 1.5,
                color: Colors.grey.shade400,
              ),
              child: Container(
                height: 200,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Iconsax.gallery_add,
                      size: context.responsive.fontSize(40),
                      color: Colors.grey.shade600,
                    ),
                    const ResponsiveSpacer(mobileSize: 8, tabletSize: 10),
                    Text(
                      context.l10n.settingChooseImageALibrary,
                      style: TextStyle(color: Colors.grey.shade600),
                    ),
                  ],
                ),
              ),
            )
          : ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.file(
                    imageFile,
                    height: 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: IconButton(
                      onPressed: () =>
                          context.read<UploadFaceCubit>().clearImage(),
                      icon: const Icon(
                        Iconsax.trash,
                        color: Colors.white,
                        size: 20,
                      ),
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.black54,
                        padding: const EdgeInsets.all(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildUploadButton(
    BuildContext context, {
    required bool isEnabled,
    required bool isUploading,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isEnabled
            ? () => context.read<UploadFaceCubit>().uploadFace()
            : null,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: Colors.blue.shade800,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          disabledBackgroundColor: Colors.grey.shade400,
        ),
        child: isUploading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation(Colors.white),
                ),
              )
            : Text(
                context.l10n.settingUploadImage,
                style: context.lightTheme.textTheme.titleMedium!.copyWith(
                  color: Colors.white, // Chắc chắn chữ màu trắng
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}
