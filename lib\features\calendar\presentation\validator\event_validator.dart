import 'package:flutter/material.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import '../../../../core/extensions/l10n_extension.dart';

/// Validator class for calendar events
class EventValidator {
  EventValidator._();


  static String? validateTitle(String? value, BuildContext context) {
    final l10n = context.l10n;
    if (value == null || value.trim().isEmpty) {
      return l10n.eventTitleRequired;
    }
    if (value.trim().length < 3) {
      return l10n.eventTitleMinLength;
    }
    if (value.trim().length > 100) {
      return l10n.eventTitleMaxLength;
    }
    return null;
  }

  /// Validates event description
  static String? validateDescription(String? value, BuildContext context) {
    final l10n = context.l10n;
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 500) {
        return l10n.eventDescriptionMaxLength;
      }
    }
    return null;
  }

  /// Validates event location
  static String? validateLocation(String? value, BuildContext context) {
    final l10n = context.l10n;
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 200) {
        return l10n.eventLocationMaxLength;
      }
    }
    return null;
  }

  /// Validates event date range
  static String? validateDateRange(DateTime startDate, DateTime endDate, BuildContext context) {
    final l10n = context.l10n;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDateOnly = DateTime(startDate.year, startDate.month, startDate.day);

    // Check if start date is not in the past (allow today)
    if (startDateOnly.isBefore(today)) {
      AppLogger.info('Start date validation failed: $startDateOnly is before $today');
      return l10n.eventStartDatePast;
    }

    // Check if end date is after start date
    if (endDate.isBefore(startDate)) {
      return l10n.eventEndDateAfterStart;
    }

    return null;
  }

  /// Validates event time range
  static String? validateTimeRange(DateTime startTime, DateTime endTime, BuildContext context, {bool isAllDay = false}) {
    final l10n = context.l10n;
    if (isAllDay) {
      return null; // No time validation needed for all-day events
    }

    if (endTime.isBefore(startTime)) {
      return l10n.eventEndTimeAfterStart;
    }

    final duration = endTime.difference(startTime);
    if (duration.inMinutes < 15) {
      return l10n.eventMinDuration;
    }

    if (duration.inDays > 7) {
      return l10n.eventMaxDuration;
    }

    return null;
  }

  /// Validates event attendees
  static String? validateAttendees(List<String> attendees, BuildContext context) {
    final l10n = context.l10n;
    if (attendees.length > 50) {
      return l10n.eventMaxAttendees;
    }
    return null;
  }

  /// Validates recurring event settings
  static String? validateRecurring(bool isRecurring, String? recurrenceRule, BuildContext context) {
    final l10n = context.l10n;
    if (isRecurring && (recurrenceRule == null || recurrenceRule.trim().isEmpty)) {
      return l10n.eventRecurrenceRuleRequired;
    }
    return null;
  }

  /// Comprehensive event validation
  static List<String> validateCompleteEvent({
    required String? title,
    required String? description,
    required String? location,
    required DateTime startTime,
    required DateTime endTime,
    required List<String> attendees,
    required bool isAllDay,
    required bool isRecurring,
    String? recurrenceRule,
    required BuildContext context,
  }) {
    final errors = <String>[];

    // Validate title
    final titleError = validateTitle(title, context);
    if (titleError != null) errors.add(titleError);

    // Validate description
    final descriptionError = validateDescription(description, context);
    if (descriptionError != null) errors.add(descriptionError);

    // Validate location
    final locationError = validateLocation(location, context);
    if (locationError != null) errors.add(locationError);

    // Validate date range
    final dateRangeError = validateDateRange(startTime, endTime, context);
    if (dateRangeError != null) errors.add(dateRangeError);

    // Validate time range
    final timeRangeError = validateTimeRange(startTime, endTime, context, isAllDay: isAllDay);
    if (timeRangeError != null) errors.add(timeRangeError);

    // Validate attendees
    final attendeesError = validateAttendees(attendees, context);
    if (attendeesError != null) errors.add(attendeesError);

    // Validate recurring settings
    final recurringError = validateRecurring(isRecurring, recurrenceRule, context);
    if (recurringError != null) errors.add(recurringError);

    return errors;
  }

  /// Validates event form before submission
  static bool validateEventForm({
    required GlobalKey<FormState> formKey,
    required String? title,
    required DateTime startTime,
    required DateTime endTime,
    required List<String> attendees,
    required bool isAllDay,
    required bool isRecurring,
    String? recurrenceRule,
    required BuildContext context,
  }) {
    // First validate the form fields
    if (!formKey.currentState!.validate()) {
      return false;
    }

    // Then validate the complete event
    final errors = validateCompleteEvent(
      title: title,
      description: null, // Description is optional
      location: null, // Location is optional
      startTime: startTime,
      endTime: endTime,
      attendees: attendees,
      isAllDay: isAllDay,
      isRecurring: isRecurring,
      recurrenceRule: recurrenceRule,
      context: context,
    );

    if (errors.isNotEmpty) {
      // Show first error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errors.first),
          backgroundColor: Colors.red,
        ),
      );
      return false;
    }

    return true;
  }
}
