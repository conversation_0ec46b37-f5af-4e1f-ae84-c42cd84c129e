import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

// State của Cubit này chỉ đơn giản là một chuỗi string chứa thời gian đã định dạng.
class ClockCubit extends Cubit<String> {
  // Timer để cập nhật thời gian mỗi giây
  Timer? _timer;

  // Constructor
  ClockCubit() : super(_getCurrentTime()) {
    // Ngay khi Cubit được tạo, bắt đầu một bộ đếm thời gian
    _startTimer();
  }

  // Hàm private để bắt đầu Timer
  void _startTimer() {
    // Hủy timer cũ nếu có để tránh rò rỉ bộ nhớ
    _timer?.cancel();

    // Tạo một Timer mới lặp lại mỗi giây
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      // Mỗi giây, lấy thời gian hiện tại và emit (phát ra) nó như một state mới
      final String newTime = _getCurrentTime();

      // Chỉ emit nếu thời gian thực sự thay đổi (tránh build lại không cần thiết)
      if (newTime != state) {
        emit(newTime);
      }
    });
  }

  // Hàm private để lấy và định dạng thời gian hiện tại
  static String _getCurrentTime() {
    // Dùng package 'intl' để định dạng thời gian thành "HH:mm:ss" (Giờ:Phút:Giây)
    return DateFormat('HH:mm:ss').format(DateTime.now());
  }

  // Đây là hàm quan trọng! Khi Cubit không còn được sử dụng,
  // chúng ta phải hủy Timer để tránh nó chạy ngầm mãi mãi.
  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
