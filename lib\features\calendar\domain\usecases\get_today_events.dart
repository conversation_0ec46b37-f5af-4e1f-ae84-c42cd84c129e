import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

class GetTodayEvents implements UseCase<List<CalendarEvent>, NoParams> {
  final CalendarRepository repository;

  GetTodayEvents(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(NoParams params) async {
    return await repository.getTodayEvents();
  }
}
