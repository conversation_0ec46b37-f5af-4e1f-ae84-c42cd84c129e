import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';

import '../../../domain/entities/monthly_details.dart';

/// Lớ<PERSON> cơ sở trừu tượng cho tất cả các trạng thái của [AttendanceHistoryCubit].
abstract class AttendanceHistoryState extends Equatable {
  const AttendanceHistoryState();
  @override
  List<Object?> get props => [];
}

/// Trạng thái ban đầu, trư<PERSON><PERSON> khi có bất kỳ hành động nào.
class AttendanceHistoryInitial extends AttendanceHistoryState {}

/// Trạng thái đang tải dữ liệu từ API. UI có thể hiển thị vòng xoay loading.
class AttendanceHistoryLoading extends AttendanceHistoryState {}

/// Trạng thái tải dữ liệu thành công. Chứa toàn bộ dữ liệu cần thiết cho màn hình.
class AttendanceHistoryLoaded extends AttendanceHistoryState {
  /// Ch<PERSON>a danh sách chi tiết tất cả các ngày trong tháng được tải.
  final MonthlyDetails monthlyDetails;

  /// Chứa thông tin chi tiết của ngày hiện tại đang được chọn trên lịch.
  final AttendanceDayDetail? selectedDayInfo;

  /// Ngày đang được "focus" trên lịch (tháng/năm đang xem).
  final DateTime focusedDay;

  const AttendanceHistoryLoaded({
    required this.monthlyDetails,
    this.selectedDayInfo,
    required this.focusedDay,
  });

  @override
  List<Object?> get props => [monthlyDetails, selectedDayInfo, focusedDay];

  /// Hàm tiện ích để tạo ra một bản sao của state hiện tại với các giá trị được cập nhật.
  /// Rất hữu ích khi chỉ muốn thay đổi một phần của state (ví dụ: chỉ thay đổi `selectedDayInfo`).
  AttendanceHistoryLoaded copyWith({
    MonthlyDetails? monthlyDetails,
    // `ValueGetter` giúp phân biệt giữa `null` thực sự và không cung cấp giá trị,
    // cho phép chúng ta set selectedDayInfo thành null.
    ValueGetter<AttendanceDayDetail?>? selectedDayInfo,
    DateTime? focusedDay,
  }) {
    return AttendanceHistoryLoaded(
      monthlyDetails: monthlyDetails ?? this.monthlyDetails,
      selectedDayInfo: selectedDayInfo != null
          ? selectedDayInfo()
          : this.selectedDayInfo,
      focusedDay: focusedDay ?? this.focusedDay,
    );
  }
}

/// Trạng thái khi có lỗi xảy ra trong quá trình tải dữ liệu.
class AttendanceHistoryError extends AttendanceHistoryState {
  final String message;
  const AttendanceHistoryError(this.message);

  @override
  List<Object> get props => [message];
}
