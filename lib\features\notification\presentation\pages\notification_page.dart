import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/features/notification/presentation/pages/tab/all_notification_tab.dart';
import 'package:golderhr/features/notification/presentation/pages/tab/notification_customer.dart';
import 'package:golderhr/features/notification/presentation/pages/tab/notification_important.dart';
import 'package:golderhr/features/notification/presentation/pages/tab/notification_unread.dart';
import 'package:golderhr/injection_container.dart';
import 'package:golderhr/shared/theme/app_colors.dart';

import '../cubit/notification_cubit.dart';
import '../cubit/notification_state.dart';

class NotificationPage extends StatelessWidget {
  const NotificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => sl<NotificationCubit>()..fetchNotifications(),
      child: const _NotificationView(),
    );
  }
}

class _NotificationView extends StatefulWidget {
  const _NotificationView();

  @override
  State<_NotificationView> createState() => _NotificationViewState();
}

class _NotificationViewState extends State<_NotificationView>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          context.l10n.notificationTittle,
          style: context.lightTheme.textTheme.headlineLarge,
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(
            context.responsive.heightPercentage(6),
          ),
          child: Container(
            color: AppColors.background,
            child: TabBar(
              isScrollable: true,
              controller: _tabController,
              indicatorColor: AppColors.primary,
              labelColor: AppColors.primary,
              unselectedLabelColor: Colors.grey,
              tabs: [
                Tab(text: context.l10n.notificationImportant),
                Tab(text: context.l10n.notificationCustomers),
                Tab(text: context.l10n.notificationUnread),
                Tab(text: context.l10n.notificationAll),
              ],
            ),
          ),
        ),
      ),
      body: BlocBuilder<NotificationCubit, NotificationState>(
        builder: (context, state) {
          if (state is NotificationLoading || state is NotificationInitial) {
            return const Center(child: CircularProgressIndicator());
          }
          if (state is NotificationError) {
            return Center(child: Text(state.message));
          }
          return TabBarView(
            controller: _tabController,
            children: const [
              NotificationImportantTab(),
              NotificationCustomerTab(),
              NotificationUnreadTab(),
              AllNotificationsTab(),
            ],
          );
        },
      ),
    );
  }
}
