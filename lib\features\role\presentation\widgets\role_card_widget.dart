import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../../domain/entities/role_entity.dart';

class RoleCardWidget extends StatelessWidget {
  final RoleEntity role;
  final Function(String action) onRoleAction;

  const RoleCardWidget({
    super.key,
    required this.role,
    required this.onRoleAction,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = context.lightTheme;

    return Container(
      margin: responsive.padding(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.12),
          width: 1,
        ),
      ),
      child: Padding(
        padding: responsive.padding(all: 16),
        child: Row(
          children: [
            // Role Icon
            Container(
              width: responsive.scaleWidth(50),
              height: responsive.scaleHeight(50),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    theme.primaryColor.withValues(alpha: 0.15),
                    theme.primaryColor.withValues(alpha: 0.08),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(responsive.defaultRadius),
                border: Border.all(
                  color: theme.primaryColor.withValues(alpha: 0.2),
                  width: 1.5,
                ),
              ),
              child: Icon(
                Iconsax.security_user,
                color: theme.primaryColor,
                size: responsive.scaleRadius(24),
              ),
            ),

            SizedBox(width: responsive.scaleWidth(16)),

            // Role Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    role.name,
                    style: AppTextStyle.bold(
                      context,
                      size: responsive.adaptiveValue<double>(
                        mobile: 16,
                        tablet: 18,
                        mobileLandscape: 17,
                        tabletLandscape: 19,
                      ),
                      color: Colors.grey[800],
                    ),
                  ),
                  SizedBox(height: responsive.scaleHeight(6)),
                  if (role.createdAt != null)
                    Row(
                      children: [
                        Icon(
                          Iconsax.calendar,
                          size: responsive.scaleRadius(14),
                          color: Colors.grey[500],
                        ),
                        SizedBox(width: responsive.scaleWidth(6)),
                        Text(
                          'Created ${_formatDate(role.createdAt!)}',
                          style: AppTextStyle.regular(
                            context,
                            size: responsive.adaptiveValue<double>(
                              mobile: 12,
                              tablet: 13,
                              mobileLandscape: 12.5,
                              tabletLandscape: 14,
                            ),
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),

            // Action Menu
            Container(
              width: responsive.scaleWidth(40),
              height: responsive.scaleHeight(40),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(responsive.defaultRadius),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: PopupMenuButton<String>(
                onSelected: (value) => onRoleAction(value),
                icon: Icon(
                  Iconsax.more,
                  color: Colors.grey[700],
                  size: responsive.scaleRadius(18),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(responsive.defaultRadius),
                ),
                elevation: 8,
                shadowColor: Colors.grey.withValues(alpha: 0.3),
                offset: const Offset(0, 4),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: theme.primaryColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Iconsax.edit,
                            size: 16,
                            color: theme.primaryColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Edit Role',
                          style: AppTextStyle.medium(
                            context,
                            size: 14,
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Iconsax.trash,
                            size: 16,
                            color: Colors.red[600],
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Delete Role',
                          style: AppTextStyle.medium(
                            context,
                            size: 14,
                            color: Colors.red[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
