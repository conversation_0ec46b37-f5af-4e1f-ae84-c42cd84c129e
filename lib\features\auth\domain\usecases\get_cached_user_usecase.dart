import 'package:dartz/dartz.dart';
import 'package:golderhr/core/error/failures.dart';
import 'package:golderhr/core/usecases/usecase.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/features/auth/domain/repositories/auth_repository.dart';

/// UseCase này chịu trách nhiệm lấy thông tin người dùng đã được lưu trong cache.
class GetCachedUserUseCase implements UseCase<UserEntity?, NoParams> {
  final AuthRepository repository;

  GetCachedUserUseCase(this.repository);

  @override
  Future<Either<Failure, UserEntity?>> call(NoParams params) async {
    return await repository.getCachedUser();
  }
}
