import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';


import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';


import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

class GetCalendarEvents
    implements UseCase<List<CalendarEvent>, GetCalendarEventsParams> {
  final CalendarRepository repository;

  GetCalendarEvents(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(
      GetCalendarEventsParams params) async {
    return await repository.getCalendarEvents(
      startDate: params.startDate,
      endDate: params.endDate,
      type: params.type != null ? CalendarEventType.values.byName(params.type!) : null,
      page: params.page,
      limit: params.limit,
    );
  }
}

// Class Params riêng cho UseCase này
class GetCalendarEventsParams extends Equatable {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? type;
  final int? page;
  final int? limit;

  const GetCalendarEventsParams({
    this.startDate,
    this.endDate,
    this.type,
    this.page,
    this.limit,
  });

  @override
  List<Object?> get props => [startDate, endDate, type, page, limit];
}