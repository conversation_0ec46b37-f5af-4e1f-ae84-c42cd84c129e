{"name": "golder_hr_backend", "version": "1.0.0", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "ts-node-dev --respawn src/app.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "HRM and CRM backend with word management", "dependencies": {"@types/node-cron": "^3.0.11", "@types/socket.io": "^3.0.1", "axios": "^1.10.0", "bcrypt": "^5.1.1", "cloudinary": "^2.7.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.4.0", "express-validator": "^7.2.1", "firebase-admin": "^13.4.0", "helmet": "^8.0.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "mongoose-paginate-v2": "^1.9.0", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cron": "^4.1.1", "nodemailer": "^7.0.3", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.7", "@types/cors": "^2.8.17", "@types/express": "^5.0.3", "@types/express-validator": "^3.0.0", "@types/helmet": "^4.0.0", "@types/http-errors": "^2.0.4", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^24.0.1", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}