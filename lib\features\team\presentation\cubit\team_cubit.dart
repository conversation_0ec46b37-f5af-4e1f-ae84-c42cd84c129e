import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/team_entity.dart';
import '../../domain/usecases/get_user_teams.dart';
import '../../domain/usecases/get_team_details.dart';
import '../../domain/usecases/create_team.dart';
import '../../domain/usecases/get_team_stats.dart';
import '../../../../core/usecases/usecase.dart';

part 'team_state.dart';

class TeamCubit extends Cubit<TeamState> {
  final GetUserTeams getUserTeams;
  final GetTeamDetails getTeamDetails;
  final CreateTeam createTeam;
  final GetTeamStats getTeamStats;

  TeamCubit({
    required this.getUserTeams,
    required this.getTeamDetails,
    required this.createTeam,
    required this.getTeamStats,
  }) : super(TeamInitial());

  Future<void> loadUserTeams() async {
    emit(TeamLoading());
    
    final result = await getUserTeams(NoParams());
    
    result.fold(
      (failure) => emit(TeamError(failure.message)),
      (teams) => emit(TeamsLoaded(teams)),
    );
  }

  Future<void> loadTeamDetails(String teamId) async {
    emit(TeamLoading());
    
    final result = await getTeamDetails(GetTeamDetailsParams(teamId: teamId));
    
    result.fold(
      (failure) => emit(TeamError(failure.message)),
      (team) => emit(TeamDetailsLoaded(team)),
    );
  }

  Future<void> createNewTeam({
    required String name,
    String? description,
    String? departmentId,
    List<String>? memberIds,
  }) async {
    emit(TeamLoading());
    
    final result = await createTeam(CreateTeamParams(
      name: name,
      description: description,
      departmentId: departmentId,
      memberIds: memberIds,
    ));
    
    result.fold(
      (failure) => emit(TeamError(failure.message)),
      (team) {
        emit(TeamCreated(team));
        // Reload teams after creation
        loadUserTeams();
      },
    );
  }

  Future<void> loadTeamStats(String teamId) async {
    final result = await getTeamStats(GetTeamStatsParams(teamId: teamId));
    
    result.fold(
      (failure) => emit(TeamError(failure.message)),
      (stats) => emit(TeamStatsLoaded(stats)),
    );
  }

  void selectTeam(TeamEntity team) {
    emit(TeamSelected(team));
  }

  void clearSelection() {
    emit(TeamInitial());
  }

  void resetToTeamsList() {
    if (state is TeamsLoaded) {
      // Already showing teams list
      return;
    }
    loadUserTeams();
  }
}
