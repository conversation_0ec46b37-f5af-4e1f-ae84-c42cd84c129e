import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/services/socket_service.dart';
import '../../domain/entities/task_entity.dart';
import '../../domain/repositories/task_repository.dart';
import '../../domain/usecases/create_task.dart';
import '../../domain/usecases/get_user_tasks.dart';

part 'task_state.dart';

class TaskCubit extends Cubit<TaskState> {
  final GetUserTasks getUserTasks;
  final CreateTask createTask;
  final TaskRepository repository;
  final SocketService socketService;

  StreamSubscription? _taskUpdateSubscription;
  List<TaskEntity> _allTasks = [];
  Map<String, dynamic>? _currentStats;

  TaskCubit({
    required this.getUserTasks,
    required this.createTask,
    required this.repository,
    required this.socketService,
  }) : super(TaskInitial()) {
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    _taskUpdateSubscription = socketService.taskUpdateStream.listen((data) {
      _handleTaskUpdate(data);
    });
  }

  Future<void> loadUserTasks({
    String? status,
    String? priority,
    String? teamId,
    String? dueDate,
  }) async {
    emit(TaskLoading());

    final result = await getUserTasks(
      GetUserTasksParams(
        status: status,
        priority: priority,
        teamId: teamId,
        dueDate: dueDate,
      ),
    );

    result.fold((failure) => emit(TaskError(failure.message)), (tasks) {
      _allTasks = tasks;
      emit(TasksLoaded(tasks));
    });
  }

  Future<void> loadTeamTasks(String teamId, {String? status}) async {
    emit(TaskLoading());

    final result = await repository.getTeamTasks(teamId, status: status);

    result.fold((failure) => emit(TaskError(failure.message)), (tasks) {
      _allTasks = tasks;
      emit(TasksLoaded(tasks));
    });
  }

  Future<void> createNewTask({
    required String title,
    String? description,
    required String assignedTo,
    String? teamId,
    TaskPriority priority = TaskPriority.medium,
    DateTime? dueDate,
    List<String>? tags,
    double? estimatedHours,
  }) async {
    emit(TaskLoading());

    final result = await createTask(
      CreateTaskParams(
        title: title,
        description: description,
        assignedTo: assignedTo,
        teamId: teamId,
        priority: priority,
        dueDate: dueDate,
        tags: tags,
        estimatedHours: estimatedHours,
      ),
    );

    result.fold((failure) => emit(TaskError(failure.message)), (task) {
      emit(TaskCreated(task));
      // Reload tasks after creation
      _refreshCurrentTasks();
    });
  }

  Future<void> updateTaskStatus(String taskId, TaskStatus status) async {
    final result = await repository.updateTaskStatus(taskId, status.name);

    result.fold((failure) => emit(TaskError(failure.message)), (updatedTask) {
      _updateTaskInList(updatedTask);
      emit(TaskUpdated(updatedTask));
    });
  }

  Future<void> addComment(String taskId, String message) async {
    final result = await repository.addTaskComment(taskId, message);

    result.fold((failure) => emit(TaskError(failure.message)), (updatedTask) {
      _updateTaskInList(updatedTask);
      emit(TaskUpdated(updatedTask));
    });
  }

  Future<void> loadTaskStats({String? teamId}) async {
    final result = await repository.getTaskStats(teamId: teamId);

    result.fold((failure) => emit(TaskError(failure.message)), (stats) {
      _currentStats = stats;
      emit(TaskStatsLoaded(stats));
    });
  }

  void filterTasks({
    String? status,
    String? priority,
    bool? isOverdue,
    bool? isDueSoon,
  }) {
    List<TaskEntity> filteredTasks = List.from(_allTasks);

    if (status != null) {
      filteredTasks = filteredTasks
          .where((task) => task.status.name == status)
          .toList();
    }

    if (priority != null) {
      filteredTasks = filteredTasks
          .where((task) => task.priority.name == priority)
          .toList();
    }

    if (isOverdue == true) {
      filteredTasks = filteredTasks.where((task) => task.isOverdue).toList();
    }

    if (isDueSoon == true) {
      filteredTasks = filteredTasks.where((task) => task.isDueSoon).toList();
    }

    emit(TasksLoaded(filteredTasks));
  }

  void searchTasks(String query) {
    if (query.isEmpty) {
      emit(TasksLoaded(_allTasks));
      return;
    }

    final filteredTasks = _allTasks.where((task) {
      return task.title.toLowerCase().contains(query.toLowerCase()) ||
          (task.description?.toLowerCase().contains(query.toLowerCase()) ??
              false) ||
          (task.tags?.any(
                (tag) => tag.toLowerCase().contains(query.toLowerCase()),
              ) ??
              false);
    }).toList();

    emit(TasksLoaded(filteredTasks));
  }

  void _handleTaskUpdate(Map<String, dynamic> data) {
    final type = data['type'];
    final taskData = data['data'];

    switch (type) {
      case 'created':
        _handleTaskCreated(taskData);
        break;
      case 'updated':
        _handleTaskUpdated(taskData);
        break;
      case 'assigned':
        _handleTaskAssigned(taskData);
        break;
      case 'comment':
        _handleTaskComment(taskData);
        break;
    }
  }

  void _handleTaskCreated(Map<String, dynamic> taskData) {
    try {
      // Parse task data and add to list if relevant
      // This would need proper TaskModel.fromJson implementation
      _refreshCurrentTasks();
    } catch (e) {
      print('Error handling task created: $e');
    }
  }

  void _handleTaskUpdated(Map<String, dynamic> taskData) {
    try {
      // Update existing task in list
      _refreshCurrentTasks();
    } catch (e) {
      print('Error handling task updated: $e');
    }
  }

  void _handleTaskAssigned(Map<String, dynamic> taskData) {
    try {
      // Handle new task assignment
      _refreshCurrentTasks();
    } catch (e) {
      print('Error handling task assigned: $e');
    }
  }

  void _handleTaskComment(Map<String, dynamic> data) {
    try {
      // Handle new task comment
      final taskId = data['taskId'];
      final comment = data['comment'];

      // Update task in list with new comment
      _refreshCurrentTasks();
    } catch (e) {
      print('Error handling task comment: $e');
    }
  }

  void _updateTaskInList(TaskEntity updatedTask) {
    final index = _allTasks.indexWhere((task) => task.id == updatedTask.id);
    if (index != -1) {
      _allTasks[index] = updatedTask;
    }
  }

  void _refreshCurrentTasks() {
    // Reload current tasks based on last filter
    if (state is TasksLoaded) {
      emit(TasksLoaded(List.from(_allTasks)));
    }
  }

  void clearTasks() {
    _allTasks.clear();
    _currentStats = null;
    emit(TaskInitial());
  }

  @override
  Future<void> close() {
    _taskUpdateSubscription?.cancel();
    return super.close();
  }
}
