import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../../domain/entities/department_entity.dart';
import '../cubit/department_cubit.dart';

class DepartmentDeleteConfirmationDialog extends StatelessWidget {
  final DepartmentEntity department;

  const DepartmentDeleteConfirmationDialog({
    super.key,
    required this.department,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
      ),
      elevation: 10,
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: 320,
          tablet: 400,
          mobileLandscape: 360,
          tabletLandscape: 440,
        ),
        padding: responsive.padding(all: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(
            responsive.defaultRadius * 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning Icon
            Container(
              width: responsive.scaleWidth(64),
              height: responsive.scaleHeight(64),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.red.withValues(alpha: 0.15),
                    Colors.red.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(
                  responsive.defaultRadius * 2,
                ),
                border: Border.all(
                  color: Colors.red.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                Iconsax.warning_2,
                color: Colors.red[600],
                size: responsive.scaleRadius(32),
              ),
            ),

            SizedBox(height: responsive.scaleHeight(10)),

            // Title
            Text(
              context.l10n.deleteDepartment,
              style: AppTextStyle.bold(
                context,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 22,
                  mobileLandscape: 21,
                  tabletLandscape: 24,
                ),
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: responsive.scaleHeight(12)),

            // Content
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: AppTextStyle.regular(
                  context,
                  size: responsive.adaptiveValue<double>(
                    mobile: 14,
                    tablet: 15,
                    mobileLandscape: 14.5,
                    tabletLandscape: 16,
                  ),
                  color: Colors.grey[600],
                ),
                children: [
                   TextSpan(
                    text:context.l10n.deleteDepartmentDialogConfirmationMessagePart1,
                  ),
                  TextSpan(
                    text: '"${department.name}"',
                    style: AppTextStyle.bold(
                      context,
                      size: responsive.adaptiveValue<double>(
                        mobile: 14,
                        tablet: 15,
                        mobileLandscape: 14.5,
                        tabletLandscape: 16,
                      ),
                      color: Colors.red[600],
                    ),
                  ),
                  const TextSpan(text: '?\n\n'),
                   TextSpan(
                    text: context.l10n.deleteDepartmentDialogWarningPrefix,
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                   TextSpan(
                    text:
                        context.l10n.deleteDepartmentDialogWarningAssignment,
                  ),
                   TextSpan(
                    text: context.l10n.deleteDepartmentDialogWarningUndone,
                    style: TextStyle(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),

            SizedBox(height: responsive.scaleHeight(28)),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: responsive.padding(vertical: 14),
                      side: BorderSide(
                        color: Colors.grey.withValues(alpha: 0.3),
                        width: 1.5,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          responsive.defaultRadius,
                        ),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      l10n.cancel,
                      style: AppTextStyle.medium(
                        context,
                        size: 15,
                        color: Colors.grey[700],
                      ),
                    ),
                  ),
                ),

                SizedBox(width: responsive.scaleWidth(12)),

                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      context.read<DepartmentCubit>().deleteExistingDepartment(department.id);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[600],
                      foregroundColor:  AppColors.white,
                      padding: responsive.padding(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          responsive.defaultRadius,
                        ),
                      ),
                      elevation: 2,
                      shadowColor: Colors.red.withValues(alpha: 0.3),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Iconsax.trash,
                          size: responsive.scaleRadius(16),
                          color: AppColors.white,
                        ),
                        SizedBox(width: responsive.scaleWidth(8)),
                        Text(
                          context.l10n.delete,
                          style: AppTextStyle.medium(
                            context,
                            size: 15,
                            color: AppColors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
