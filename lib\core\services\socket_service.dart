import 'dart:async';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:flutter/foundation.dart';
import 'flutter_secure_storage.dart';

class SocketService {
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;
  SocketService._internal();

  io.Socket? _socket;
  bool _isConnected = false;
  String? _currentUserId;
  
  // Stream controllers for real-time events
  final StreamController<Map<String, dynamic>> _chatMessageController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _notificationController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _taskUpdateController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _teamUpdateController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _typingController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // Getters for streams
  Stream<Map<String, dynamic>> get chatMessageStream => _chatMessageController.stream;
  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;
  Stream<Map<String, dynamic>> get taskUpdateStream => _taskUpdateController.stream;
  Stream<Map<String, dynamic>> get teamUpdateStream => _teamUpdateController.stream;
  Stream<Map<String, dynamic>> get typingStream => _typingController.stream;

  bool get isConnected => _isConnected;
  String? get currentUserId => _currentUserId;

  Future<void> connect() async {
    try {
      final secureStorage = SecureStorageService();
      final token = await secureStorage.getToken();
      if (token == null) {
        debugPrint('🔌 No token found, cannot connect to socket');
        return;
      }

      // Get base URL from environment or use default
      const String baseUrl = String.fromEnvironment(
        'API_BASE_URL',
        defaultValue: 'http://localhost:3000',
      );

      _socket = io.io(baseUrl,
        io.OptionBuilder()
          .setTransports(['websocket'])
          .enableAutoConnect()
          .setAuth({'token': token})
          .build()
      );

      _setupEventListeners();
      
      debugPrint('🔌 Attempting to connect to socket server...');
    } catch (e) {
      debugPrint('🔌 Error connecting to socket: $e');
    }
  }

  void _setupEventListeners() {
    if (_socket == null) return;

    // Connection events
    _socket!.onConnect((_) {
      _isConnected = true;
      debugPrint('🔌 Connected to socket server');
    });

    _socket!.onDisconnect((_) {
      _isConnected = false;
      debugPrint('🔌 Disconnected from socket server');
    });

    _socket!.onConnectError((error) {
      debugPrint('🔌 Connection error: $error');
    });

    _socket!.onError((error) {
      debugPrint('🔌 Socket error: $error');
    });

    // Chat events
    _socket!.on('chat:message', (data) {
      debugPrint('💬 New chat message: $data');
      _chatMessageController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('chat:typing', (data) {
      debugPrint('⌨️ Typing indicator: $data');
      _typingController.add(Map<String, dynamic>.from(data));
    });

    _socket!.on('chat:reaction', (data) {
      debugPrint('😀 Message reaction: $data');
      _chatMessageController.add({
        'type': 'reaction',
        'data': data,
      });
    });

    _socket!.on('chat:pinned', (data) {
      debugPrint('📌 Message pinned: $data');
      _chatMessageController.add({
        'type': 'pinned',
        'data': data,
      });
    });

    // Task events
    _socket!.on('task:created', (data) {
      debugPrint('📋 Task created: $data');
      _taskUpdateController.add({
        'type': 'created',
        'data': data,
      });
    });

    _socket!.on('task:updated', (data) {
      debugPrint('📋 Task updated: $data');
      _taskUpdateController.add({
        'type': 'updated',
        'data': data,
      });
    });

    _socket!.on('task:assigned', (data) {
      debugPrint('📋 Task assigned: $data');
      _taskUpdateController.add({
        'type': 'assigned',
        'data': data,
      });
    });

    _socket!.on('task:comment', (data) {
      debugPrint('💬 Task comment: $data');
      _taskUpdateController.add({
        'type': 'comment',
        'data': data,
      });
    });

    // Team events
    _socket!.on('team:created', (data) {
      debugPrint('👥 Team created: $data');
      _teamUpdateController.add({
        'type': 'created',
        'data': data,
      });
    });

    _socket!.on('team:updated', (data) {
      debugPrint('👥 Team updated: $data');
      _teamUpdateController.add({
        'type': 'updated',
        'data': data,
      });
    });

    _socket!.on('meeting:created', (data) {
      debugPrint('📅 Meeting created: $data');
      _teamUpdateController.add({
        'type': 'meeting_created',
        'data': data,
      });
    });

    // Notification events
    _socket!.on('notification:new', (data) {
      debugPrint('🔔 New notification: $data');
      _notificationController.add(Map<String, dynamic>.from(data));
    });
  }

  // Chat methods
  void sendChatMessage({
    required String teamId,
    required String message,
    String type = 'text',
    List<Map<String, dynamic>>? attachments,
    List<String>? mentions,
    String? replyTo,
  }) {
    if (!_isConnected || _socket == null) {
      debugPrint('🔌 Socket not connected, cannot send message');
      return;
    }

    _socket!.emit('chat:send', {
      'teamId': teamId,
      'message': message,
      'type': type,
      'attachments': attachments ?? [],
      'mentions': mentions ?? [],
      'replyTo': replyTo,
    });
  }

  void sendTypingIndicator({
    required String teamId,
    required bool isTyping,
  }) {
    if (!_isConnected || _socket == null) return;

    _socket!.emit('chat:typing', {
      'teamId': teamId,
      'isTyping': isTyping,
    });
  }

  void reactToMessage({
    required String messageId,
    required String emoji,
  }) {
    if (!_isConnected || _socket == null) return;

    _socket!.emit('chat:react', {
      'messageId': messageId,
      'emoji': emoji,
    });
  }

  // Team methods
  void joinTeamRoom(String teamId) {
    if (!_isConnected || _socket == null) return;

    _socket!.emit('team:join', teamId);
    debugPrint('👥 Joined team room: $teamId');
  }

  void leaveTeamRoom(String teamId) {
    if (!_isConnected || _socket == null) return;

    _socket!.emit('team:leave', teamId);
    debugPrint('👥 Left team room: $teamId');
  }

  // Connection management
  void disconnect() {
    if (_socket != null) {
      _socket!.disconnect();
      _socket = null;
    }
    _isConnected = false;
    debugPrint('🔌 Socket disconnected manually');
  }

  void reconnect() {
    if (_socket != null) {
      _socket!.connect();
    } else {
      connect();
    }
  }

  // Cleanup
  void dispose() {
    disconnect();
    _chatMessageController.close();
    _notificationController.close();
    _taskUpdateController.close();
    _teamUpdateController.close();
    _typingController.close();
  }
}
