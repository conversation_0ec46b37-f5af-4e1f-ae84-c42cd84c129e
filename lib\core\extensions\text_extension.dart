import 'package:flutter/material.dart';

extension ResponsiveTextExtension on String {
  Widget responsiveText({
    required BuildContext context,
    TextStyle? style,
    int maxLines = 2,
    TextAlign? textAlign,
  }) {
    return Flexible(
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Text(
          this,
          maxLines: maxLines,
          overflow: TextOverflow.ellipsis,
          textAlign: textAlign,
          style: style ?? Theme.of(context).textTheme.bodyMedium,
        ),
      ),
    );
  }
}
