import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:golderhr/shared/widgets/gradient_background.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import '../cubit/manual_attendance_cubit.dart';
import '../cubit/manual_attendance_state.dart';
import '../widgets/manual_attendance_card.dart';
import '../widgets/manual_attendance_filter.dart';

class AdminManualAttendancePage extends StatefulWidget {
  const AdminManualAttendancePage({super.key});

  @override
  State<AdminManualAttendancePage> createState() => _AdminManualAttendancePageState();
}

class _AdminManualAttendancePageState extends State<AdminManualAttendancePage> {
  ManualAttendanceStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  void _loadRequests() {
    context.read<ManualAttendanceCubit>().getManualAttendanceRequests(
      status: _selectedStatus,
      startDate: _startDate,
      endDate: _endDate,
    );
  }

  void _onFilterChanged({
    ManualAttendanceStatus? status,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    setState(() {
      _selectedStatus = status;
      _startDate = startDate;
      _endDate = endDate;
    });
    _loadRequests();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => context.pop(),
                      icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                    ),
                    const SizedBox(width: 8),
                    const Icon(
                      Iconsax.clipboard_text,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Quản lý chấm công thủ công',
                        style: TextStyle(
                          fontSize: responsive.fontSize(20),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _loadRequests,
                      icon: const Icon(Icons.refresh, color: Colors.white),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 16),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Filter section
                      ManualAttendanceFilter(
                        selectedStatus: _selectedStatus,
                        startDate: _startDate,
                        endDate: _endDate,
                        onFilterChanged: _onFilterChanged,
                      ),

                      // Requests list
                      Expanded(
                        child: BlocBuilder<ManualAttendanceCubit, ManualAttendanceState>(
                          builder: (context, state) {
                            if (state is ManualAttendanceLoading) {
                              return const Center(
                                child: CircularProgressIndicator(),
                              );
                            }

                            if (state is ManualAttendanceError) {
                              return Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Iconsax.warning_2,
                                      size: 64,
                                      color: Colors.red.shade300,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Lỗi tải dữ liệu',
                                      style: TextStyle(
                                        fontSize: responsive.fontSize(18),
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red.shade700,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      state.message,
                                      style: TextStyle(
                                        fontSize: responsive.fontSize(14),
                                        color: Colors.grey.shade600,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 16),
                                    ElevatedButton.icon(
                                      onPressed: _loadRequests,
                                      icon: const Icon(Icons.refresh),
                                      label: const Text('Thử lại'),
                                    ),
                                  ],
                                ),
                              );
                            }

                            if (state is ManualAttendanceLoaded) {
                              if (state.requests.isEmpty) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Iconsax.document,
                                        size: 64,
                                        color: Colors.grey.shade400,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'Không có yêu cầu nào',
                                        style: TextStyle(
                                          fontSize: responsive.fontSize(18),
                                          fontWeight: FontWeight.bold,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Chưa có yêu cầu chấm công thủ công nào',
                                        style: TextStyle(
                                          fontSize: responsive.fontSize(14),
                                          color: Colors.grey.shade500,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              }

                              return RefreshIndicator(
                                onRefresh: () async => _loadRequests(),
                                child: ListView.builder(
                                  padding: const EdgeInsets.all(16),
                                  itemCount: state.requests.length,
                                  itemBuilder: (context, index) {
                                    final request = state.requests[index];
                                    return ManualAttendanceCard(
                                      request: request,
                                      onReview: (status, note) {
                                        context.read<ManualAttendanceCubit>().reviewManualAttendance(
                                          id: request.id,
                                          status: status,
                                          adminNote: note,
                                        );
                                      },
                                    );
                                  },
                                ),
                              );
                            }

                            return const SizedBox.shrink();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
