import '../../domain/entities/leave_policy.dart';

class LeavePolicyModel extends LeavePolicy {
  const LeavePolicyModel({
    required super.leaveType,
    required super.description,
    required super.maxDaysPerYear,
    required super.maxDaysPerRequest,
    required super.advanceNoticeDays,
    super.carryOverDays = 0,
    super.isActive = true,
  });

  factory LeavePolicyModel.fromJson(Map<String, dynamic> json) {
    return LeavePolicyModel(
      leaveType: json['leaveType'] ?? '',
      description: json['description'] ?? '',
      maxDaysPerYear: json['maxDaysPerYear'] ?? 0,
      maxDaysPerRequest: json['maxDaysPerRequest'] ?? 0,
      advanceNoticeDays: json['advanceNoticeDays'] ?? 0,
      carryOverDays: json['carryOverDays'] ?? 0,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'leaveType': leaveType,
      'description': description,
      'maxDaysPerYear': maxDaysPerYear,
      'maxDaysPerRequest': maxDaysPerRequest,
      'advanceNoticeDays': advanceNoticeDays,
      'carryOverDays': carryOverDays,
      'isActive': isActive,
    };
  }
} 