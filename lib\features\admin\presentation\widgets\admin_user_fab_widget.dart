import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

class AdminUserFabWidget extends StatelessWidget {
  final VoidCallback onCreateUser;
  final VoidCallback onRefresh;

  const AdminUserFabWidget({
    super.key,
    required this.onCreateUser,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Refresh FAB
        FloatingActionButton(
          onPressed: onRefresh,
          heroTag: "refresh_fab",
          backgroundColor: Colors.grey[700],
          child: const Icon(
            Icons.refresh,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 16),
        
        // Create User FAB
        FloatingActionButton.extended(
          onPressed: onCreateUser,
          heroTag: "create_user_fab",
          icon: const Icon(Iconsax.user_add),
          label: const Text('Add User'),
        ),
      ],
    );
  }
}
