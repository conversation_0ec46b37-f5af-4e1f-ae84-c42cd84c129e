import 'package:dartz/dartz.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/logger/app_logger.dart';
import '../../../../injection_container.dart';
import '../../../notification/domain/usecases/notify_admin_new_request.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../../domain/entities/overtime_summary_entity.dart';
import '../../domain/repositories/overtime_repository.dart';
import '../datasources/overtime_remote_data_source.dart';

class OvertimeRepositoryImpl implements OvertimeRepository {
  final OvertimeRemoteDataSource remoteDataSource;

  OvertimeRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, OvertimeSummaryEntity>> getOvertimeSummary() async {
    try {
      final result = await remoteDataSource.getOvertimeSummary();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<OvertimeRequestEntity>>> getOvertimeHistory({
    int page = 1,
    int limit = 10,
    OvertimeStatus? status,
  }) async {
    try {
      final result = await remoteDataSource.getOvertimeHistory(
        page: page,
        limit: limit,
        status: status,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, List<ApproverEntity>>> getApprovers() async {
    try {
      final result = await remoteDataSource.getApprovers();
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, OvertimeRequestEntity>> submitOvertimeRequest({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  }) async {
    try {
      final result = await remoteDataSource.submitOvertimeRequest(
        date: date,
        startTime: startTime,
        endTime: endTime,
        reason: reason,
        type: type,
        approverId: approverId,
      );

      // Gửi thông báo đến admin sau khi submit thành công
      AppLogger.info('🔔 Starting admin notification for overtime request...');
      AppLogger.info(
        '📋 Request details: ID=${result.id}, Employee=${result.employeeName}',
      );

      try {
        final notifyAdminUseCase = sl<NotifyAdminNewRequest>();
        AppLogger.info(
          '✅ NotifyAdminNewRequest use case retrieved successfully',
        );

        final notificationParams = NotifyAdminNewRequestParams(
          requestType: 'overtime',
          requestId: result.id,
          employeeName: result.employeeName,
          requestDetails:
              '${type.name} vào ${date.day}/${date.month}/${date.year} từ ${startTime.hour}:${startTime.minute.toString().padLeft(2, '0')} đến ${endTime.hour}:${endTime.minute.toString().padLeft(2, '0')}',
        );

        AppLogger.info(
          '📤 Calling notification use case with params: $notificationParams',
        );

        final notificationResult = await notifyAdminUseCase(notificationParams);

        notificationResult.fold(
          (failure) {
            AppLogger.error(
              '❌ Notification failed with error: ${failure.message}',
            );
          },
          (_) {
            AppLogger.info('✅ Admin notification sent successfully!');
          },
        );
      } catch (e, stackTrace) {
        // Log lỗi nhưng không fail toàn bộ request
        AppLogger.error('💥 Exception in admin notification: $e');
        AppLogger.error('📍 Stack trace: $stackTrace');
        print('🚨 OVERTIME NOTIFICATION ERROR: $e');
        print('📍 STACK TRACE: $stackTrace');
      }

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, OvertimeRequestEntity>> updateOvertimeRequest({
    required String requestId,
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  }) async {
    try {
      final result = await remoteDataSource.updateOvertimeRequest(
        requestId: requestId,
        date: date,
        startTime: startTime,
        endTime: endTime,
        reason: reason,
        type: type,
        approverId: approverId,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, bool>> cancelOvertimeRequest(String requestId) async {
    try {
      final result = await remoteDataSource.cancelOvertimeRequest(requestId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Either<Failure, OvertimeRequestEntity>> getOvertimeRequestById(
    String requestId,
  ) async {
    try {
      final result = await remoteDataSource.getOvertimeRequestById(requestId);
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(
        ServerFailure('An unexpected error occurred: ${e.toString()}'),
      );
    }
  }
}
