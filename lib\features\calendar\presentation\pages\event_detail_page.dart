import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../utils/calendar_dialogs.dart';

class EventDetailPage extends StatelessWidget {
  final CalendarEvent event;

  const EventDetailPage({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0.5,
        title: Text(
          'Event Details',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showEditEventDialog(context),
            icon: const Icon(Icons.edit, color: Colors.blue),
            tooltip: 'Edit',
          ),
          IconButton(
            onPressed: () => _showDeleteConfirmDialog(context),
            icon: const Icon(Icons.delete, color: Colors.red),
            tooltip: 'Delete',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: _buildEventCard(context),
      ),
    );
  }

  Widget _buildEventCard(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Center(
            child: Text(
              event.title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: context.rf(20),
                color: AppColors.textPrimary,
              ),
            ),
          ),
          SizedBox(height: context.rh(20)),

          // Info sections with colored icons
          _buildInfoRow(
            context,
            Icons.label_outline,
            'Type',
            _getEventTypeDisplayName(event.type),
            iconColor: Colors.deepPurple,
          ),
          SizedBox(height: context.rh(12)),

          _buildInfoRow(
            context,
            Icons.access_time,
            'Time',
            _formatEventTime(),
            iconColor: Colors.orange,
          ),
          SizedBox(height: context.rh(12)),

          if (event.location?.isNotEmpty == true) ...[
            _buildInfoRow(
              context,
              Icons.location_on_outlined,
              'Location',
              event.location!,
              iconColor: Colors.redAccent,
            ),
            SizedBox(height: context.rh(12)),
          ],

          if (event.description?.isNotEmpty == true) ...[
            _buildInfoRow(
              context,
              Icons.description_outlined,
              'Description',
              event.description!,
              iconColor: Colors.blueGrey,
            ),
            SizedBox(height: context.rh(12)),
          ],

          if (event.attendees.isNotEmpty) ...[
            _buildInfoRow(
              context,
              Icons.people_outline,
              'Attendees (${event.attendees.length})',
              event.attendees.map((a) => a.fullname).join(', '),
              iconColor: Colors.teal,
            ),
            SizedBox(height: context.rh(12)),
          ],

          _buildInfoRow(
            context,
            Icons.person_outline,
            'Created by',
            event.createdBy.fullname,
            iconColor: Colors.indigo,
          ),
          SizedBox(height: context.rh(12)),

          _buildInfoRow(
            context,
            Icons.calendar_today_outlined,
            'Created',
            _formatDate(event.createdAt),
            iconColor: Colors.green,
          ),

          if (event.isRecurring) ...[
            SizedBox(height: context.rh(12)),
            _buildInfoRow(
              context,
              Icons.repeat,
              'Recurring',
              event.recurrenceRule ?? 'Yes',
              iconColor: Colors.pink,
            ),
          ],
        ],
      ),

    );
  }

  Widget _buildInfoRow(
      BuildContext context,
      IconData icon,
      String label,
      String value, {
        Color? iconColor,
      }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: context.rf(20),
            color: iconColor ?? AppColors.textSecondary,
          ),
          SizedBox(width: context.rw(12)),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Label
                Expanded(
                  flex: 4,
                  child: Text(
                    label,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),

                // Value
                Expanded(
                  flex: 6,
                  child: Text(
                    value,
                    textAlign: TextAlign.right,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  // Helpers
  void _showEditEventDialog(BuildContext context) {
    final calendarCubit = context.read<CalendarCubit>();
    CalendarDialogs.showAddEventDialogWithCubit(
      context,
      calendarCubit: calendarCubit,
      eventToEdit: event,
      selectedDate: DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      ),
    );
    // Note: Calendar will be refreshed automatically by the dialog's cubit
  }

  Future<void> _showDeleteConfirmDialog(BuildContext context) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (shouldDelete == true && context.mounted) {
      await _deleteEvent(context);
    }
  }

  Future<void> _deleteEvent(BuildContext context) async {
    final cubit = context.read<CalendarCubit>();

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      await cubit.deleteEvent(event.id);

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();

        // Refresh calendar events
        await cubit.loadEvents();

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Event deleted successfully'),
              backgroundColor: Colors.green,
            ),
          );

          // Go back to calendar page
          context.pop();
        }
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete event: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatEventTime() {
    if (event.isAllDay) return 'All day';
    final start = '${event.startTime.hour.toString().padLeft(2, '0')}:${event.startTime.minute.toString().padLeft(2, '0')}';
    final end = '${event.endTime.hour.toString().padLeft(2, '0')}:${event.endTime.minute.toString().padLeft(2, '0')}';

    if (_isSameDay(event.startTime, event.endTime)) {
      return '$start - $end';
    } else {
      return '${_formatDate(event.startTime)} $start → ${_formatDate(event.endTime)} $end';
    }
  }

  String _formatDate(DateTime date) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  bool _isSameDay(DateTime d1, DateTime d2) {
    return d1.year == d2.year && d1.month == d2.month && d1.day == d2.day;
  }

  String _getEventTypeDisplayName(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting:
        return 'Meeting';
      case CalendarEventType.leave:
        return 'Leave';
      case CalendarEventType.holiday:
        return 'Holiday';
      case CalendarEventType.training:
        return 'Training';
      case CalendarEventType.event:
        return 'Event';
      case CalendarEventType.other:
        return 'Other';
      case CalendarEventType.unknown:
        return 'Unknown';
    }
  }
}
