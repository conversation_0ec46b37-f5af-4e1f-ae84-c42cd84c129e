import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart'; // <PERSON><PERSON>ả sử bạn có extension này

class TimeCard extends StatelessWidget {
  final String label;
  final String time;
  final IconData icon;
  final Color color;

  const TimeCard({
    super.key,
    required this.label,
    required this.time,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Container(
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.8)),
      ),
      child: <PERSON>umn(
        children: [
          Icon(icon, color: color, size: responsive.fontSize(24)),
          SizedBox(height: responsive.heightPercentage(0.5)),
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: Colors.grey[700]),
          ),
          Text(
            time,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
