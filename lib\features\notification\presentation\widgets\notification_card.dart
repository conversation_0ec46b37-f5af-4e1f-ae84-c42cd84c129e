import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../../../../core/routes/app_routes.dart';
import '../../../../shared/widgets/responsive_spacer.dart';
import '../../data/model/notification_model.dart';
import '../cubit/notification_cubit.dart';

class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onMarkAsRead;

  const NotificationCard({
    super.key,
    required this.notification,
    required this.onMarkAsRead,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUnread = !notification.isRead;

    final unreadBackgroundColor = notification.color.withValues(alpha: 0.08);
    final iconBackgroundColor = notification.color.withValues(alpha: 0.15);
    final borderColor = theme.dividerColor.withValues(alpha: 0.5);

    return InkWell(onTap: () async {
      onMarkAsRead();
      final result = await context.pushNamed(
        AppRoutes
            .notificationDetailRelative,
        pathParameters: { 'id': notification.id, },
        extra: notification,
      );

      if (result == true && context.mounted) {
        context.read<NotificationCubit>().fetchNotifications();
      }
    },
      borderRadius: BorderRadius.circular(16),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: context.responsive.padding(all: 16),
        decoration: BoxDecoration(
          color: isUnread ? unreadBackgroundColor : theme.cardColor,
          borderRadius: BorderRadius.circular(16),
          border: notification.isImportant
              ? Border.all(color: Colors.amber.shade600, width: 1.5)
              : Border.all(color: borderColor),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // [THÊM MỚI] BỌC HERO CHO ICON
            Hero(
              tag: 'notification_icon_${notification.id}',
              // Tag phải duy nhất cho mỗi thông báo
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Container(
                    padding: context.responsive.padding(all: 12),
                    decoration: BoxDecoration(
                      color: iconBackgroundColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      notification.icon,
                      color: notification.color,
                      size: context.responsive.fontSize(24),
                    ),
                  ),
                  if (isUnread)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.primary,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: theme.cardColor,
                            width: 1.5,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            ResponsiveSpacer(
              mobileSize: 16,
              tabletSize: 18,
              mobileLandscapeSize: 16,
              tabletLandscapeSize: 18,
              axis: Axis.horizontal,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // [THÊM MỚI] BỌC HERO CHO TITLE
                  Hero(
                    tag: 'notification_title_${notification.id}',
                    // Bọc Text bằng Material để tránh lỗi render text (vạch kẻ vàng) trong quá trình chuyển cảnh Hero
                    child: Material(
                      type: MaterialType.transparency,
                      child: Text(
                        notification.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: isUnread
                              ? FontWeight.bold
                              : FontWeight.normal,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  ResponsiveSpacer(
                    mobileSize: 4,
                    tabletSize: 6,
                    mobileLandscapeSize: 4,
                    tabletLandscapeSize: 6,
                  ),
                  // Phần message không cần Hero animation
                  Text(
                    notification.message,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  ResponsiveSpacer(
                    mobileSize: 8,
                    tabletSize: 10,
                    mobileLandscapeSize: 8,
                    tabletLandscapeSize: 10,
                  ),
                  Row(
                    children: [
                      if (notification.isImportant)
                        Padding(
                          padding: context.responsive.padding(right: 8.0),
                          child: Icon(
                            Icons.star_rounded,
                            color: Colors.amber.shade700,
                            size: 18,
                          ),
                        ),
                      // [THÊM MỚI] BỌC HERO CHO TIME
                      Hero(
                        tag: 'notification_time_${notification.id}',
                        child: Material(
                          type: MaterialType.transparency,
                          child: Text(
                            timeago.format(
                              notification.timestamp,
                              locale: 'vi',
                            ),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
