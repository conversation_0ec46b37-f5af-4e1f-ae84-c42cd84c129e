import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../logger/app_logger.dart';

class DioInterceptor extends Interceptor {
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.debug('REQUEST[${options.method}] => PATH: ${options.uri}');
      AppLogger.debug('Headers: ${options.headers}');
      if (options.data != null) {
        AppLogger.debug('Data: ${options.data}');
      }
    }
    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    if (kDebugMode) {
      AppLogger.info(
        'RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.uri}',
      );
      AppLogger.info('Data: ${response.data}');
    }
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    if (kDebugMode) {
      AppLogger.error(
        'ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.uri}',
        err,
      );
      AppLogger.error('Message: ${err.message}');
      if (err.response != null) {
        AppLogger.error('Error Data: ${err.response?.data}');
      }
    }

    // Retry logic for connection timeouts and network errors
    if (_shouldRetry(err)) {
      final retryCount = err.requestOptions.extra['retryCount'] ?? 0;

      if (retryCount < maxRetries) {
        AppLogger.info('Retrying request (${retryCount + 1}/$maxRetries)...');

        // Wait before retry
        await Future.delayed(retryDelay);

        // Update retry count
        err.requestOptions.extra['retryCount'] = retryCount + 1;

        try {
          // Create a new Dio instance for retry
          final retryDio = Dio();
          retryDio.options.baseUrl = err.requestOptions.baseUrl;
          retryDio.options.connectTimeout = err.requestOptions.connectTimeout;
          retryDio.options.receiveTimeout = err.requestOptions.receiveTimeout;
          retryDio.options.sendTimeout = err.requestOptions.sendTimeout;
          retryDio.options.headers.addAll(err.requestOptions.headers);

          final response = await retryDio.fetch(err.requestOptions);
          return handler.resolve(response);
        } catch (e) {
          // If retry fails, continue with original error
          AppLogger.error('Retry failed: $e');
        }
      }
    }

    return handler.next(err);
  }

  bool _shouldRetry(DioException err) {
    // Retry on connection timeout, receive timeout, or connection errors
    return err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.connectionError ||
        (err.type == DioExceptionType.unknown &&
            err.error.toString().contains('Connection timed out'));
  }
}
