import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';
import 'package:golderhr/shared/widgets/responsive_spacer.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../../shared/widgets/action_item_model.dart';
import '../../../cubit/user_cubit.dart';
import '../../../faceDetection/presentation/cubit/face_checkin_cubit.dart';
import '../../../more/presentation/pages/more_page.dart';
import '../cubit/home_cubit.dart';
import '../widgets/attendance_section.dart';
import '../widgets/greeting_section.dart';
import '../widgets/home_announcements_section.dart';
import '../widgets/home_app_bar.dart';
import '../widgets/home_top_actions.dart';
import '../widgets/quick_stats_section.dart';



class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> with WidgetsBindingObserver {



  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      _refreshAttendanceData();
    }
  }

  Future<void> _refreshAttendanceData() async {
    // Refresh dữ liệu attendance khi quay về trang home
    final faceDetectionCubit = context.read<FaceDetectionCubit>();
    await faceDetectionCubit.checkAttendanceStatus();

    // Fetch lại notification khi quay về trang home hoặc kéo refresh
    final homeCubit = context.read<HomeCubit>();
    final l10n = AppLocalizations.of(context)!;
    await homeCubit.fetchNotificationsHome(l10n: l10n);
  }

  @override
  Widget build(BuildContext context) {
    final UserEntity? user = context.watch<UserCubit>().state;
    final List<ActionItem> actions = MorePage.getAllActions(context, user);
    return Scaffold(
      appBar: const HomeAppBar(),
      body: SafeArea(
        top: false,
        bottom: true,
        child: Stack(
          children: [
            RefreshIndicator(
              onRefresh: _refreshAttendanceData,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const GreetingSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    const QuickStatsSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    const AttendanceSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),
                    HomeAnnouncementsSection(),
                    const ResponsiveSpacer(mobileSize: 24, tabletSize: 26),

                    HomeTopActions(actions: actions),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
