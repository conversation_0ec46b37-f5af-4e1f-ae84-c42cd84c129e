import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class AppTextStyle {
  static TextStyle _style(
    BuildContext context, {
    required double size,
    FontWeight weight = FontWeight.normal,
    FontStyle fontStyle = FontStyle.normal,
    Color? color,
  }) {
    return TextStyle(
      fontSize: context.rf(size),
      fontWeight: weight,
      fontStyle: fontStyle,
      fontFamily: 'Roboto',
      color: color ?? Colors.black,
    );
  }

  static TextStyle light(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, weight: FontWeight.w300, color: color);

  static TextStyle regular(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, weight: FontWeight.w400, color: color);

  static TextStyle medium(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, weight: FontWeight.w500, color: color);

  static TextStyle bold(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, weight: FontWeight.w700, color: color);

  static TextStyle black(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, weight: FontWeight.w900, color: color);

  static TextStyle italic(
    BuildContext context, {
    double size = 14,
    Color? color,
  }) => _style(context, size: size, fontStyle: FontStyle.italic, color: color);
}
