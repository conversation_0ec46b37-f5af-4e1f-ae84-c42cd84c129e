import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/attendance_history.dart';
import '../entities/monthly_details.dart';
import '../entities/monthly_summary.dart';
import '../entities/today_summary.dart';
import '../entities/weekly_summary.dart';

abstract class AttendanceRepositoryV1 {
  Future<Either<Failure, TodaySummary>> getTodaySummary();

  Future<Either<Failure, WeeklySummary>> getWeeklySummary();

  Future<Either<Failure, MonthlySummary>> getMonthlySummary();

  Future<Either<Failure, PaginatedAttendanceHistory>> getAttendanceHistory({
    required int page,
    required int limit,
  });

  Future<Either<Failure, MonthlyDetails>> getMonthlyDetails({
    required int year,
    required int month,
  });
}
