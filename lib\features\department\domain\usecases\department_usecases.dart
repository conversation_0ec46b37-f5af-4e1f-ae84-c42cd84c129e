import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/department_entity.dart';
import '../repositories/department_repository.dart';
import '../../data/models/department_model.dart';

// Get all departments use case
class GetAllDepartments
    implements UseCase<DepartmentListResult, GetAllDepartmentsParams> {
  final DepartmentRepository repository;

  GetAllDepartments(this.repository);

  @override
  Future<Either<Failure, DepartmentListResult>> call(
    GetAllDepartmentsParams params,
  ) async {
    return await repository.getAllDepartments(
      page: params.page,
      limit: params.limit,
      search: params.search,
      includeDeleted: params.includeDeleted,
    );
  }
}

class GetAllDepartmentsParams {
  final int page;
  final int limit;
  final String? search;
  final bool includeDeleted;

  const GetAllDepartmentsParams({
    this.page = 1,
    this.limit = 10,
    this.search,
    this.includeDeleted = false,
  });
}

// Get department by ID use case
class GetDepartmentById implements UseCase<DepartmentEntity, String> {
  final DepartmentRepository repository;

  GetDepartmentById(this.repository);

  @override
  Future<Either<Failure, DepartmentEntity>> call(String departmentId) async {
    return await repository.getDepartmentById(departmentId);
  }
}

// Create department use case
class CreateDepartment
    implements UseCase<DepartmentEntity, CreateDepartmentParams> {
  final DepartmentRepository repository;

  CreateDepartment(this.repository);

  @override
  Future<Either<Failure, DepartmentEntity>> call(
    CreateDepartmentParams params,
  ) async {
    // Check if department name already exists
    final nameExistsResult = await repository.checkDepartmentNameExists(
      params.name,
    );

    final nameExists = nameExistsResult.fold(
      (failure) => false, // If check fails, proceed with creation
      (exists) => exists,
    );

    if (nameExists) {
      return Left(ValidationFailure('Department name already exists'));
    }

    // Check if department code already exists (if provided)
    if (params.code != null && params.code!.isNotEmpty) {
      final codeExistsResult = await repository.checkDepartmentCodeExists(
        params.code!,
      );

      final codeExists = codeExistsResult.fold(
        (failure) => false, // If check fails, proceed with creation
        (exists) => exists,
      );

      if (codeExists) {
        return Left(ValidationFailure('Department code already exists'));
      }
    }

    return repository.createDepartment(
      name: params.name,
      description: params.description,
      code: params.code,
      parentId: params.parentId,
    );
  }
}

// Update department use case
class UpdateDepartment
    implements UseCase<DepartmentEntity, UpdateDepartmentParams> {
  final DepartmentRepository repository;

  UpdateDepartment(this.repository);

  @override
  Future<Either<Failure, DepartmentEntity>> call(
    UpdateDepartmentParams params,
  ) async {
    // Check if new name already exists (excluding current department)
    if (params.name != null) {
      final nameExistsResult = await repository.checkDepartmentNameExists(
        params.name!,
        excludeId: params.departmentId,
      );

      final nameExists = nameExistsResult.fold(
        (failure) => false, // If check fails, proceed with update
        (exists) => exists,
      );

      if (nameExists) {
        return Left(ValidationFailure('Department name already exists'));
      }
    }

    // Check if new code already exists (excluding current department)
    if (params.code != null && params.code!.isNotEmpty) {
      final codeExistsResult = await repository.checkDepartmentCodeExists(
        params.code!,
        excludeId: params.departmentId,
      );

      final codeExists = codeExistsResult.fold(
        (failure) => false, // If check fails, proceed with update
        (exists) => exists,
      );

      if (codeExists) {
        return Left(ValidationFailure('Department code already exists'));
      }
    }

    return await repository.updateDepartment(
      params.departmentId,
      name: params.name,
      description: params.description,
      code: params.code,
      isActive: params.isActive,
      isDisabled: params.isDisabled,
      parentId: params.parentId,
    );
  }
}

// Delete department use case
class DeleteDepartment implements UseCase<void, String> {
  final DepartmentRepository repository;

  DeleteDepartment(this.repository);

  @override
  Future<Either<Failure, void>> call(String departmentId) async {
    return await repository.deleteDepartment(departmentId);
  }
}

// Restore department use case
class RestoreDepartment implements UseCase<DepartmentEntity, String> {
  final DepartmentRepository repository;

  RestoreDepartment(this.repository);

  @override
  Future<Either<Failure, DepartmentEntity>> call(String departmentId) async {
    return await repository.restoreDepartment(departmentId);
  }
}

// Toggle department status use case
class ToggleDepartmentStatus implements UseCase<DepartmentEntity, String> {
  final DepartmentRepository repository;

  ToggleDepartmentStatus(this.repository);

  @override
  Future<Either<Failure, DepartmentEntity>> call(String departmentId) async {
    return await repository.toggleDepartmentStatus(departmentId);
  }
}

// Get departments for dropdown use case
class GetDepartmentsForDropdown
    implements UseCase<List<DepartmentEntity>, NoParams> {
  final DepartmentRepository repository;

  GetDepartmentsForDropdown(this.repository);

  @override
  Future<Either<Failure, List<DepartmentEntity>>> call(NoParams params) async {
    return await repository.getDepartmentsForDropdown();
  }
}

// Get department hierarchy use case
class GetDepartmentHierarchy
    implements UseCase<List<DepartmentEntity>, NoParams> {
  final DepartmentRepository repository;

  GetDepartmentHierarchy(this.repository);

  @override
  Future<Either<Failure, List<DepartmentEntity>>> call(NoParams params) async {
    return await repository.getDepartmentHierarchy();
  }
}

// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure(String message) : super(message);
}
