import 'package:dio/dio.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/error/exceptions.dart';
import '../models/team_model.dart';
import '../models/team_chat_model.dart';

abstract class TeamRemoteDataSource {
  Future<List<TeamModel>> getUserTeams();
  Future<TeamModel> getTeamDetails(String teamId);
  Future<TeamModel> createTeam(Map<String, dynamic> teamData);
  Future<void> addTeamMember(String teamId, String userId, String role);
  Future<Map<String, dynamic>> getTeamStats(String teamId);

  // Chat
  Future<Map<String, dynamic>> getTeamChatHistory(String teamId, {int page = 1, int limit = 50});
  Future<void> pinMessage(String teamId, String messageId);

  // Meetings
  Future<void> createMeeting(String teamId, Map<String, dynamic> meetingData);
}

class TeamRemoteDataSourceImpl implements TeamRemoteDataSource {
  final DioClient dioClient;

  TeamRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<TeamModel>> getUserTeams() async {
    try {
      final response = await dioClient.get('/api/teams');

      if (response.statusCode == 200) {
        final List<dynamic> teamsJson = response.data['data'];
        return teamsJson.map((json) => TeamModel.fromJson(json)).toList();
      } else {
        throw ServerException('Failed to get teams');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<TeamModel> getTeamDetails(String teamId) async {
    try {
      final response = await dioClient.get('/api/teams/$teamId');

      if (response.statusCode == 200) {
        return TeamModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to get team details');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<TeamModel> createTeam(Map<String, dynamic> teamData) async {
    try {
      final response = await dioClient.post('/api/teams', data: teamData);

      if (response.statusCode == 201) {
        return TeamModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to create team');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<void> addTeamMember(String teamId, String userId, String role) async {
    try {
      final response = await dioClient.post(
        '/api/teams/$teamId/members',
        data: {'userId': userId, 'role': role},
      );

      if (response.statusCode != 201) {
        throw ServerException('Failed to add team member');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<Map<String, dynamic>> getTeamStats(String teamId) async {
    try {
      final response = await dioClient.get('/api/teams/$teamId/stats');

      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException('Failed to get team stats');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<Map<String, dynamic>> getTeamChatHistory(String teamId, {int page = 1, int limit = 50}) async {
    try {
      final response = await dioClient.get(
        '/api/teams/$teamId/chat',
        queryParameters: {'page': page, 'limit': limit},
      );

      if (response.statusCode == 200) {
        final data = response.data['data'];
        return {
          'messages': (data['messages'] as List)
              .map((json) => TeamChatModel.fromJson(json))
              .toList(),
          'total': data['total'],
          'hasMore': data['hasMore'],
        };
      } else {
        throw ServerException('Failed to get chat history');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<void> pinMessage(String teamId, String messageId) async {
    try {
      final response = await dioClient.put('/api/teams/$teamId/chat/$messageId/pin');

      if (response.statusCode != 200) {
        throw ServerException('Failed to pin message');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<void> createMeeting(String teamId, Map<String, dynamic> meetingData) async {
    try {
      final response = await dioClient.post('/api/teams/$teamId/meetings', data: meetingData);

      if (response.statusCode != 201) {
        throw ServerException('Failed to create meeting');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }
}
