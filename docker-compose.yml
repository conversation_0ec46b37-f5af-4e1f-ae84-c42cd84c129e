version: '3.8'

services:
  # Node.js Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: ${NODE_ENV:-development}
    container_name: golder_hr_backend
    restart: unless-stopped
    ports:
      - "${APP_PORT:-3000}:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - MONGO_URL=${MONGO_URL}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      - COOKIE_SECRET=${COOKIE_SECRET}
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT:-587}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY_ID=${FIREBASE_PRIVATE_KEY_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_CLIENT_ID=${FIREBASE_CLIENT_ID}
      - FACE_VERIFICATION_URL=http://localhost:5000
      - BCRYPT_ROUNDS=${BCRYPT_ROUNDS:-12}
      - CORS_ORIGIN=${CORS_ORIGIN:-*}
    volumes:
      - uploads_data:/app/uploads
      - ./logs:/app/logs
      # Mount source code for hot-reload
      - ./src:/app/src
    networks:
      - golder_hr_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  uploads_data:
    driver: local

networks:
  golder_hr_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
