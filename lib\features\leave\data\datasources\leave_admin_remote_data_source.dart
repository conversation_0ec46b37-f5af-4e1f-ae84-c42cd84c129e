import '../../../../core/network/dio_client.dart';
import '../../../../core/error/exceptions.dart';
import '../models/leave_request_model.dart';

abstract class LeaveAdminRemoteDataSource {
  Future<List<LeaveRequestModel>> getAllLeaveRequests({
    int page = 1,
    int limit = 10,
    String? status,
  });
  Future<LeaveRequestModel> approveLeaveRequest(String requestId);
  Future<LeaveRequestModel> rejectLeaveRequest(
    String requestId,
    String rejectionReason,
  );
}

class LeaveAdminRemoteDataSourceImpl implements LeaveAdminRemoteDataSource {
  final DioClient dioClient;

  LeaveAdminRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<LeaveRequestModel>> getAllLeaveRequests({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    try {
      print(
        '🌐 [LEAVE_ADMIN_DATA_SOURCE] getAllLeaveRequests - page: $page, limit: $limit, status: $status',
      );

      final queryParams = <String, dynamic>{'page': page, 'limit': limit};

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      final response = await dioClient.get(
        '/api/leave/admin/all',
        queryParameters: queryParams,
      );

      print('🌐 [LEAVE_ADMIN_DATA_SOURCE] Response: ${response.data}');

      if (response.data['success'] == true) {
        final List<dynamic> requestsJson =
            response.data['data']['requests'] ?? [];
        return requestsJson
            .map((json) => LeaveRequestModel.fromJson(json))
            .toList();
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to fetch leave requests',
        );
      }
    } catch (e) {
      print('❌ [LEAVE_ADMIN_DATA_SOURCE] Error: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Failed to fetch leave requests: $e');
    }
  }

  @override
  Future<LeaveRequestModel> approveLeaveRequest(String requestId) async {
    try {
      print(
        '🌐 [LEAVE_ADMIN_DATA_SOURCE] approveLeaveRequest - requestId: $requestId',
      );

      final response = await dioClient.put(
        '/api/leave/admin/$requestId/approve',
      );

      print('🌐 [LEAVE_ADMIN_DATA_SOURCE] Approve response: ${response.data}');

      if (response.data['success'] == true) {
        return LeaveRequestModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to approve leave request',
        );
      }
    } catch (e) {
      print('❌ [LEAVE_ADMIN_DATA_SOURCE] Approve error: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Failed to approve leave request: $e');
    }
  }

  @override
  Future<LeaveRequestModel> rejectLeaveRequest(
    String requestId,
    String rejectionReason,
  ) async {
    try {
      print(
        '🌐 [LEAVE_ADMIN_DATA_SOURCE] rejectLeaveRequest - requestId: $requestId, reason: $rejectionReason',
      );

      final response = await dioClient.put(
        '/api/leave/admin/$requestId/reject',
        data: {'rejectionReason': rejectionReason},
      );

      print('🌐 [LEAVE_ADMIN_DATA_SOURCE] Reject response: ${response.data}');

      if (response.data['success'] == true) {
        return LeaveRequestModel.fromJson(response.data['data']);
      } else {
        throw ServerException(
          response.data['message'] ?? 'Failed to reject leave request',
        );
      }
    } catch (e) {
      print('❌ [LEAVE_ADMIN_DATA_SOURCE] Reject error: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('Failed to reject leave request: $e');
    }
  }
}
