
import '../../../auth/data/models/user_model.dart';
import '../../domain/entities/calendar_event.dart';

class CalendarEventModel extends CalendarEvent {
  const CalendarEventModel({
    required super.id,
    required super.title,
    super.description,
    required super.startTime,
    required super.endTime,
    required super.isAllDay,
    required super.type,
    required super.color,
    super.location,
    required super.attendees,
    required super.createdBy,
    required super.isRecurring,
    super.recurrenceRule,
    required super.isDeleted,
    required super.createdAt,
    required super.updatedAt,
  });

  factory CalendarEventModel.fromJson(Map<String, dynamic> json) {
    // Debug: Print raw JSON to see what backend returns
    print('🔍 CalendarEventModel.fromJson - Raw JSON:');
    print('createdBy: ${json['createdBy']}');
    print('attendees: ${json['attendees']}');

    // Helper function để parse type một cách an toàn
    CalendarEventType parseEventType(String? typeString) {
      return CalendarEventType.values.firstWhere(
            (e) => e.name == typeString,
        orElse: () => CalendarEventType.unknown,
      );
    }

    // Helper function để parse user từ JSON hoặc ObjectId
    UserModel parseUser(dynamic userData) {
      if (userData is Map<String, dynamic>) {
        return UserModel.fromJson(userData);
      } else if (userData is String) {
        return UserModel(
          id: userData,
          email: '',
          fullname: 'Unknown User',
          role: 'user',
        );
      } else {
        // Fallback cho trường hợp khác
        return UserModel(
          id: '',
          email: '',
          fullname: 'Unknown User',
          role: 'user',
        );
      }
    }

    // Helper function để parse danh sách attendees
    List<UserModel> parseAttendees(dynamic attendeesData) {
      if (attendeesData is List) {
        return attendeesData.map((attendeeData) => parseUser(attendeeData)).toList();
      } else {
        return [];
      }
    }

    return CalendarEventModel(
      id: json['_id'] ?? json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      startTime: DateTime.parse(json['startTime']),
      endTime: DateTime.parse(json['endTime']),
      isAllDay: json['isAllDay'] ?? false,
      type: parseEventType(json['type']),
      color: json['color'] ?? '#2196F3',
      location: json['location'],
      attendees: parseAttendees(json['attendees']),
      createdBy: parseUser(json['createdBy']),
      isRecurring: json['isRecurring'] ?? false,
      recurrenceRule: json['recurrenceRule'],
      isDeleted: json['isDeleted'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }


}