import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import 'event_list_widget.dart';

class WeekViewWidget extends StatelessWidget {
  const WeekViewWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarCubit, CalendarState>(
      builder: (context, state) {
        final cubit = context.read<CalendarCubit>();
        final selectedDate = state.selectedDate;
        final startOfWeek = _getStartOfWeek(selectedDate ?? DateTime.now());

        return SingleChildScrollView(
          padding: context.responsive.padding(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              _buildWeekHeader(context, cubit, startOfWeek),
              SizedBox(height: context.rh(16)),
              _buildWeekDays(context, cubit, startOfWeek),
              SizedBox(height: context.rh(16)),
              _buildWeekEvents(context, cubit, startOfWeek),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWeekHeader(
    BuildContext context,
    CalendarCubit cubit,
    DateTime startOfWeek,
  ) {
    final l10n = context.l10n;
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return Container(
      padding: context.responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            l10n.calendarWeekViewTitle,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Row(
            children: [
              IconButton(
                onPressed: () => _navigateWeek(cubit, -1),
                icon: Icon(
                  Icons.chevron_left,
                  color: AppColors.textSecondary,
                  size: context.rf(20),
                ),
              ),
              Text(
                '${_getShortDateString(startOfWeek)} - ${_getShortDateString(endOfWeek)}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              ),
              IconButton(
                onPressed: () => _navigateWeek(cubit, 1),
                icon: Icon(
                  Icons.chevron_right,
                  color: AppColors.textSecondary,
                  size: context.rf(20),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWeekDays(
    BuildContext context,
    CalendarCubit cubit,
    DateTime startOfWeek,
  ) {
    final dayAbbreviations = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    final selectedDate = cubit.selectedDate;

    return Container(
      padding: context.responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: List.generate(7, (index) {
          final date = startOfWeek.add(Duration(days: index));
          final isSelected = _isSameDay(date, selectedDate ?? DateTime.now());
          final isToday = _isSameDay(date, DateTime.now());
          final hasEvents = cubit.hasEventsOnDate(date);

          return Expanded(
            child: GestureDetector(
              onTap: () => cubit.selectDate(date),
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: context.rw(2)),
                padding: context.responsive.padding(vertical: 12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? AppColors.primaryBlue.withValues(alpha: 0.15)
                      : isToday
                      ? AppColors.primaryGreen.withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                  border: isToday && !isSelected
                      ? Border.all(color: AppColors.primaryGreen, width: 1)
                      : null,
                ),
                child: Column(
                  children: [
                    Text(
                      dayAbbreviations[date.weekday % 7],
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: AppColors.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: context.rh(4)),
                    Text(
                      date.day.toString(),
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: isToday || isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                        color: isSelected
                            ? AppColors.primaryBlue
                            : isToday
                            ? AppColors.primaryGreen
                            : AppColors.textPrimary,
                      ),
                    ),
                    if (hasEvents) ...[
                      SizedBox(height: context.rh(4)),
                      Container(
                        width: context.rw(6),
                        height: context.rw(6),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? AppColors.primaryBlue
                              : AppColors.secondary,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildWeekEvents(
    BuildContext context,
    CalendarCubit cubit,
    DateTime startOfWeek,
  ) {
    final l10n = context.l10n;
    final weekEvents = cubit.getEventsForWeek(startOfWeek);

    if (weekEvents.isEmpty) {
      return _buildNoEventsCard(context, l10n);
    }

    // Group events by date
    final groupedEvents = <DateTime, List<CalendarEventEntity>>{};
    for (final event in weekEvents) {
      final eventDate = DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      );
      groupedEvents.putIfAbsent(eventDate, () => []).add(event);
    }

    return Container(
      padding: context.responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.event_note,
                color: AppColors.primaryBlue,
                size: context.rf(20),
              ),
              SizedBox(width: context.rw(8)),
              Text(
                l10n.calendarWeekEventsTitle,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          SizedBox(height: context.rh(16)),
          GroupedEventListWidget(
            groupedEvents: groupedEvents,
            showDateHeaders: true,
          ),
        ],
      ),
    );
  }

  Widget _buildNoEventsCard(BuildContext context, dynamic l10n) {
    return Container(
      width: double.infinity,
      padding: context.responsive.padding(all: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.event_busy_outlined,
            size: context.rf(48),
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          SizedBox(height: context.rh(12)),
          Text(
            l10n.noEventsForThisWeek,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  void _navigateWeek(CalendarCubit cubit, int direction) {
    final currentDate = cubit.selectedDate ?? DateTime.now();
    final newDate = currentDate.add(Duration(days: 7 * direction));
    cubit.selectDate(newDate);
  }

  DateTime _getStartOfWeek(DateTime date) {
    final weekday = date.weekday == 7 ? 0 : date.weekday;
    return date.subtract(Duration(days: weekday));
  }

  String _getShortDateString(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]}';
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
