# Hệ Thống Thông Báo Admin

## Tổng Quan
Hệ thống thông báo admin được thiết kế để tự động gửi thông báo push notification đến admin khi có đơn request mới từ user (leave request hoặc overtime request).

## Kiến Trúc

### 1. Client-side (Flutter App)
- **Firebase Cloud Messaging (FCM)**: Xử lý push notifications
- **Notification Service**: Quản lý FCM token và gửi request đến server
- **Auto Notification**: Tự động gửi thông báo khi submit request thành công

### 2. Server-side API Endpoints
- `POST /api/notifications/fcm-token`: Đăng ký FCM token
- `POST /api/notifications/admin/new-request`: G<PERSON>i thông báo đến admin về request mới

## Luồng Hoạt Động

### Khi User Tạo Leave Request:
1. User submit leave request qua app
2. App gọi API `/api/leave/submit`
3. Server xử lý và lưu request
4. **Tự động gọi notification service** để thông báo admin
5. Server gửi push notification đến tất cả admin devices
6. Admin nhận thông báo trên điện thoại

### Khi User Tạo Overtime Request:
1. User submit overtime request qua app
2. App gọi API `/api/overtime/submit`
3. Server xử lý và lưu request
4. **Tự động gọi notification service** để thông báo admin
5. Server gửi push notification đến tất cả admin devices
6. Admin nhận thông báo trên điện thoại

## Implementation Details

### 1. Notification Data Source
```dart
// lib/features/notification/data/datasources/notification_remote_data_source.dart
Future<void> notifyAdminNewRequest({
  required String requestType, // 'leave' hoặc 'overtime'
  required String requestId,
  required String employeeName,
  required String requestDetails,
});
```

### 2. Use Case
```dart
// lib/features/notification/domain/usecases/notify_admin_new_request.dart
class NotifyAdminNewRequest implements UseCase<void, NotifyAdminNewRequestParams>
```

### 3. Repository Integration
- **Leave Repository**: Tự động gọi notification sau khi submit thành công
- **Overtime Repository**: Tự động gọi notification sau khi submit thành công

## Server Requirements

### API Endpoint: `/api/notifications/admin/new-request`
**Method**: POST

**Request Body**:
```json
{
  "requestType": "leave", // hoặc "overtime"
  "requestId": "request-id-123",
  "employeeName": "Tên Nhân Viên",
  "requestDetails": "Chi tiết request"
}
```

**Response**:
```json
{
  "success": true,
  "message": "Notification sent successfully"
}
```

### Server Logic Cần Implement:
1. **Xác định admin users**: Query database để lấy danh sách users có role admin
2. **Lấy FCM tokens**: Query FCM tokens của admin users
3. **Gửi push notification**: Sử dụng Firebase Admin SDK để gửi notification
4. **Lưu notification record**: Lưu vào database để tracking

### Example Server Implementation (Node.js):
```javascript
app.post('/api/notifications/admin/new-request', async (req, res) => {
  try {
    const { requestType, requestId, employeeName, requestDetails } = req.body;
    
    // 1. Lấy danh sách admin users
    const adminUsers = await User.find({ role: 'admin' });
    
    // 2. Lấy FCM tokens của admin
    const adminTokens = await FCMToken.find({ 
      userId: { $in: adminUsers.map(u => u._id) } 
    });
    
    // 3. Tạo notification message
    const message = {
      notification: {
        title: `Đơn ${requestType} mới`,
        body: `${employeeName} đã gửi đơn ${requestType}: ${requestDetails}`
      },
      data: {
        type: 'new_request',
        requestType,
        requestId,
        employeeName
      }
    };
    
    // 4. Gửi đến tất cả admin tokens
    const tokens = adminTokens.map(t => t.token);
    if (tokens.length > 0) {
      await admin.messaging().sendMulticast({
        tokens,
        ...message
      });
    }
    
    // 5. Lưu notification record
    await Notification.create({
      type: 'new_request',
      title: message.notification.title,
      body: message.notification.body,
      data: message.data,
      recipients: adminUsers.map(u => u._id),
      createdAt: new Date()
    });
    
    res.json({ success: true, message: 'Notification sent successfully' });
  } catch (error) {
    console.error('Error sending admin notification:', error);
    res.status(500).json({ success: false, message: 'Failed to send notification' });
  }
});
```

## Testing

### Unit Tests
- `test/features/notification/domain/usecases/notify_admin_new_request_test.dart`
- `test/features/notification/admin_notification_integration_test.dart`

### Manual Testing
1. Tạo leave request từ user account
2. Kiểm tra admin có nhận được push notification
3. Tạo overtime request từ user account
4. Kiểm tra admin có nhận được push notification

## Troubleshooting

### Không nhận được thông báo:
1. **Kiểm tra FCM token**: Đảm bảo admin đã đăng ký FCM token
2. **Kiểm tra server logs**: Xem có lỗi khi gửi notification không
3. **Kiểm tra notification permissions**: Đảm bảo app có quyền gửi notification
4. **Kiểm tra network**: Đảm bảo device có kết nối internet

### Debug Commands:
```bash
# Kiểm tra FCM tokens trong database
# Kiểm tra notification logs
# Test gửi notification thủ công
```

## Future Enhancements
1. **Notification Categories**: Phân loại thông báo theo loại request
2. **Custom Sounds**: Âm thanh riêng cho từng loại thông báo
3. **Notification History**: Lịch sử thông báo trong app
4. **Push Notification Settings**: Cho phép admin bật/tắt từng loại thông báo
