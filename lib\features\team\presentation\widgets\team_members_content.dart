import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/theme/app_theme_helper.dart';
import '../../domain/entities/team_entity.dart';
import '../cubit/team_cubit.dart';

class TeamMembersContent extends StatefulWidget {
  final TeamEntity team;

  const TeamMembersContent({super.key, required this.team});

  @override
  State<TeamMembersContent> createState() => _TeamMembersContentState();
}

class _TeamMembersContentState extends State<TeamMembersContent> {
  String _searchQuery = '';
  String _selectedRole = 'all';

  @override
  void initState() {
    super.initState();
    // Load team details to get members if not already loaded
    if (widget.team.members == null || widget.team.members!.isEmpty) {
      context.read<TeamCubit>().loadTeamDetails(widget.team.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.background,
      child: <PERSON>umn(
        children: [
          // Header with search and filters
          _buildHeader(),
          // Members list
          Expanded(
            child: BlocBuilder<TeamCubit, TeamState>(
              builder: (context, state) {
                if (state is TeamLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is TeamDetailsLoaded) {
                  return _buildMembersList(state.team.members ?? []);
                }

                return _buildMembersList(widget.team.members ?? []);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Team Members',
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (widget.team.canManage)
                ElevatedButton.icon(
                  onPressed: () => _showAddMemberDialog(),
                  icon: const Icon(Icons.person_add, size: 18),
                  label: const Text('Add Member'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              // Search bar
              Expanded(
                flex: 2,
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: TextField(
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value.toLowerCase();
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Search members...',
                      hintStyle: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: AppColors.textSecondary,
                        size: 20,
                      ),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Role filter
              Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.border),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedRole,
                    onChanged: (value) {
                      setState(() {
                        _selectedRole = value!;
                      });
                    },
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('All Roles')),
                      DropdownMenuItem(value: 'leader', child: Text('Leaders')),
                      DropdownMenuItem(value: 'member', child: Text('Members')),
                      DropdownMenuItem(value: 'viewer', child: Text('Viewers')),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMembersList(List<TeamMemberEntity> members) {
    // Filter members based on search and role
    final filteredMembers = members.where((member) {
      final matchesSearch = _searchQuery.isEmpty ||
          member.fullname.toLowerCase().contains(_searchQuery) ||
          (member.email?.toLowerCase().contains(_searchQuery) ?? false);

      final matchesRole = _selectedRole == 'all' || member.role == _selectedRole;

      return matchesSearch && matchesRole && member.isActive;
    }).toList();

    if (filteredMembers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty || _selectedRole != 'all'
                  ? 'No members found matching your criteria'
                  : 'No team members yet',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(20),
      itemCount: filteredMembers.length,
      itemBuilder: (context, index) {
        final member = filteredMembers[index];
        return _buildMemberCard(member);
      },
    );
  }

  Widget _buildMemberCard(TeamMemberEntity member) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar
              CircleAvatar(
                radius: 24,
                backgroundColor: AppColors.primary,
                backgroundImage: member.avatar != null
                    ? NetworkImage(member.avatar!)
                    : null,
                child: member.avatar == null
                    ? Text(
                        member.fullname.substring(0, 1).toUpperCase(),
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              // Member info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          member.fullname,
                          style: AppTextStyles.bodyLarge.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildRoleBadge(member.role ?? 'member'),
                      ],
                    ),
                    if (member.email != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        member.email!,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    if (member.position != null || member.department != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        [member.position, member.department]
                            .where((e) => e != null)
                            .join(' • '),
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                    if (member.joinedAt != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Joined ${_formatDate(member.joinedAt!)}',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // Actions
              if (widget.team.canManage && !member.isTeamLeader)
                PopupMenuButton<String>(
                  onSelected: (value) => _handleMemberAction(value, member),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'change_role',
                      child: Row(
                        children: [
                          Icon(Icons.admin_panel_settings, size: 16),
                          SizedBox(width: 8),
                          Text('Change Role'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'remove',
                      child: Row(
                        children: [
                          Icon(Icons.remove_circle, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Remove', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  child: Icon(
                    Icons.more_vert,
                    color: AppColors.textSecondary,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleBadge(String role) {
    Color backgroundColor;
    Color textColor;
    String displayRole;

    switch (role.toLowerCase()) {
      case 'leader':
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        displayRole = 'LEADER';
        break;
      case 'member':
        backgroundColor = AppColors.secondary.withValues(alpha: 0.1);
        textColor = AppColors.secondary;
        displayRole = 'MEMBER';
        break;
      case 'viewer':
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        displayRole = 'VIEWER';
        break;
      default:
        backgroundColor = AppColors.textSecondary.withValues(alpha: 0.1);
        textColor = AppColors.textSecondary;
        displayRole = role.toUpperCase();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        displayRole,
        style: AppTextStyles.bodySmall.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} weeks ago';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else {
      return '${(difference.inDays / 365).floor()} years ago';
    }
  }

  void _handleMemberAction(String action, TeamMemberEntity member) {
    switch (action) {
      case 'change_role':
        _showChangeRoleDialog(member);
        break;
      case 'remove':
        _showRemoveMemberDialog(member);
        break;
    }
  }

  void _showAddMemberDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Team Member'),
        content: const SizedBox(
          width: 400,
          child: Text('Add member functionality will be implemented here.\n\nThis will include:\n• User search\n• Role selection\n• Invitation system'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement add member functionality
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }

  void _showChangeRoleDialog(TeamMemberEntity member) {
    String selectedRole = member.role ?? 'member';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Change Role for ${member.fullname}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Member'),
              subtitle: const Text('Can view and participate in team activities'),
              value: 'member',
              groupValue: selectedRole,
              onChanged: (value) {
                selectedRole = value!;
                Navigator.pop(context);
                _changeRole(member, selectedRole);
              },
            ),
            RadioListTile<String>(
              title: const Text('Viewer'),
              subtitle: const Text('Can only view team activities'),
              value: 'viewer',
              groupValue: selectedRole,
              onChanged: (value) {
                selectedRole = value!;
                Navigator.pop(context);
                _changeRole(member, selectedRole);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showRemoveMemberDialog(TeamMemberEntity member) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Team Member'),
        content: Text('Are you sure you want to remove ${member.fullname} from this team?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _removeMember(member);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _changeRole(TeamMemberEntity member, String newRole) {
    // TODO: Implement role change API call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Role change for ${member.fullname} will be implemented'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _removeMember(TeamMemberEntity member) {
    // TODO: Implement remove member API call
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Remove ${member.fullname} will be implemented'),
        backgroundColor: AppColors.warning,
      ),
    );
  }
}
