import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../cubit/leave_admin_cubit.dart';

class LeaveAdminFilterWidget extends StatefulWidget {
  const LeaveAdminFilterWidget({super.key});

  @override
  State<LeaveAdminFilterWidget> createState() => _LeaveAdminFilterWidgetState();
}

class _LeaveAdminFilterWidgetState extends State<LeaveAdminFilterWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final List<FilterOption> _filterOptions = [
    FilterOption(value: null, label: 'All', icon: Icons.list_rounded),
    FilterOption(
      value: 'pending',
      label: 'Pending',
      icon: Icons.schedule_rounded,
    ),
    FilterOption(
      value: 'approved',
      label: 'Approved',
      icon: Icons.check_circle_rounded,
    ),
    FilterOption(
      value: 'rejected',
      label: 'Rejected',
      icon: Icons.cancel_rounded,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimation = Tween<double>(begin: -50.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              padding: responsive.padding(all: 16),
              decoration: BoxDecoration(
                color:AppColors.background,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding:context.responsive.padding(all: 8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child:  Icon(
                          Icons.filter_list_rounded,
                          color: Colors.blue,
                          size: context.responsive.fontSize(20),
                        ),
                      ),
                       SizedBox(width: context.responsive.widthPercentage(2)),
                      Text(
                        context.l10n.filterRequests,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: Colors.grey[800],
                            ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(2)),
                  BlocBuilder<LeaveAdminCubit, LeaveAdminState>(
                    builder: (context, state) {
                      return SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: _filterOptions.map((option) {
                            final isSelected =
                                state.selectedStatus == option.value;
                            return Padding(
                              padding: const EdgeInsets.only(right: 12),
                              child: _FilterChip(
                                option: option,
                                isSelected: isSelected,
                                onTap: () {
                                  context
                                      .read<LeaveAdminCubit>()
                                      .filterByStatus(option.value);
                                },
                              ),
                            );
                          }).toList(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class FilterOption {
  final String? value;
  final String label;
  final IconData icon;

  FilterOption({required this.value, required this.label, required this.icon});
}

class _FilterChip extends StatefulWidget {
  final FilterOption option;
  final bool isSelected;
  final VoidCallback onTap;

  const _FilterChip({
    required this.option,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<_FilterChip> createState() => _FilterChipState();
}

class _FilterChipState extends State<_FilterChip>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Color _getStatusColor() {
    switch (widget.option.value) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor();

    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) => _controller.reverse(),
      onTapCancel: () => _controller.reverse(),
      onTap: widget.onTap,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: context.responsive.padding(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: widget.isSelected
                ? LinearGradient(
                    colors: [color, color.withValues(alpha: 0.8)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : LinearGradient(
                    colors: [Colors.grey.shade50, Colors.grey.shade100],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: widget.isSelected
                  ? color.withValues(alpha: 0.3)
                  : Colors.grey.withValues(alpha: 0.3),
              width: 1.5,
            ),
            boxShadow: widget.isSelected
                ? [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.option.icon,
                size: context.responsive.fontSize(18),
                color: widget.isSelected ? AppColors.white : color,
              ),
               SizedBox(width: context.responsive.widthPercentage(1)),
              Text(
                widget.option.label,
                style: TextStyle(
                  color: widget.isSelected ? AppColors.white : Colors.grey[700],
                  fontSize: context.responsive.fontSize(14),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
