import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class DropdownTile extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final List<String> options;
  final ValueChanged<String?> onChanged;

  const DropdownTile({
    super.key,

    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.options,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final responsive = context.responsive;
    return Padding(
      padding: responsive.padding(all: 16),
      child: Row(
        children: [
          Container(
            padding: responsive.padding(all: 10),
            decoration: BoxDecoration(
              color: color.withAlpha((255 * 0.1).round()),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: responsive.fontSize(20)),
          ),
          SizedBox(width: responsive.widthPercentage(4)),
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          Container(
            padding: responsive.padding(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.inputDecorationTheme.fillColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButton<String>(
              value: value,
              onChanged: onChanged,
              underline: Container(),
              isDense: true,
              style: theme.textTheme.labelMedium,
              items: options.map((String option) {
                return DropdownMenuItem<String>(
                  value: option,
                  child: Text(option, style: theme.textTheme.labelMedium),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
