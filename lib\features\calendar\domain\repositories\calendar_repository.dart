import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/calendar_event.dart';
import '../params/create_event_params.dart';
import '../params/update_event_params.dart';


abstract class CalendarRepository {

  Future<Either<Failure, List<CalendarEvent>>> getCalendarEvents({
    DateTime? startDate,
    DateTime? endDate,
    CalendarEventType? type,
    int? page,
    int? limit,
  });


  Future<Either<Failure, CalendarEvent>> getCalendarEventById(String eventId);


  Future<Either<Failure, CalendarEvent>> createCalendarEvent(
      CreateEventParams params);


  Future<Either<Failure, CalendarEvent>> updateCalendarEvent(
      String eventId, UpdateEventParams params);


  Future<Either<Failure, void>> deleteCalendarEvent(String eventId);

  // Additional methods to match backend
  Future<Either<Failure, Map<String, dynamic>>> getCalendarSummary();

  Future<Either<Failure, List<CalendarEvent>>> getTodayEvents();

  Future<Either<Failure, List<CalendarEvent>>> getUpcomingEvents({int? limit});

  Future<Either<Failure, List<CalendarEvent>>> getWeeklyEvents({required DateTime weekStart});

  Future<Either<Failure, List<CalendarEvent>>> checkEventConflicts({
    required DateTime startTime,
    required DateTime endTime,
    String? excludeEventId,
  });
}