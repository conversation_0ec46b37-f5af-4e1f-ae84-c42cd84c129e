import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../repositories/leave_repository.dart';
import 'usecase.dart';

class GetApprovers implements UseCase<List<Map<String, dynamic>>, NoParams> {
  final LeaveRepository repository;

  GetApprovers(this.repository);

  @override
  Future<Either<Failure, List<Map<String, dynamic>>>> call(NoParams params) async {
    return await repository.getApprovers();
  }
}
