import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';

class AdminUserEmptyStateWidget extends StatelessWidget {
  const AdminUserEmptyStateWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: responsive.adaptiveValue<double>(
              mobile: 64,
              tablet: 80,
              mobileLandscape: 72,
              tabletLandscape: 96,
            ),
            color: Colors.grey[400],
          ),
          SizedBox(height: responsive.scaleHeight(16)),
          Text(
            'No users found', // l10n.noUsersFound,
            style: AppTextStyle.medium(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Try adjusting your filters or create a new user', // l10n.tryAdjustingFilters,
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
