import 'package:dio/dio.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/error/exceptions.dart';
import '../models/task_model.dart';

abstract class TaskRemoteDataSource {
  Future<List<TaskModel>> getUserTasks({
    String? status,
    String? priority,
    String? teamId,
    String? dueDate,
  });
  Future<List<TaskModel>> getTeamTasks(String teamId, {String? status});
  Future<TaskModel> createTask(Map<String, dynamic> taskData);
  Future<TaskModel> updateTaskStatus(String taskId, String status);
  Future<TaskModel> addTaskComment(String taskId, String message);
  Future<Map<String, dynamic>> getTaskStats({String? teamId});
}

class TaskRemoteDataSourceImpl implements TaskRemoteDataSource {
  final DioClient dioClient;

  TaskRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<TaskModel>> getUserTasks({
    String? status,
    String? priority,
    String? teamId,
    String? dueDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (status != null) queryParams['status'] = status;
      if (priority != null) queryParams['priority'] = priority;
      if (teamId != null) queryParams['teamId'] = teamId;
      if (dueDate != null) queryParams['dueDate'] = dueDate;

      final response = await dioClient.get('/api/tasks/my', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        final List<dynamic> tasksJson = response.data['data'];
        return tasksJson.map((json) => TaskModel.fromJson(json)).toList();
      } else {
        throw ServerException('Failed to get user tasks');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<List<TaskModel>> getTeamTasks(String teamId, {String? status}) async {
    try {
      final queryParams = status != null ? {'status': status} : <String, dynamic>{};
      final response = await dioClient.get('/api/tasks/team/$teamId', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        final List<dynamic> tasksJson = response.data['data'];
        return tasksJson.map((json) => TaskModel.fromJson(json)).toList();
      } else {
        throw ServerException('Failed to get team tasks');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<TaskModel> createTask(Map<String, dynamic> taskData) async {
    try {
      final response = await dioClient.post('/api/tasks', data: taskData);
      
      if (response.statusCode == 201) {
        return TaskModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to create task');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<TaskModel> updateTaskStatus(String taskId, String status) async {
    try {
      final response = await dioClient.put(
        '/tasks/$taskId/status',
        data: {'status': status},
      );
      
      if (response.statusCode == 200) {
        return TaskModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to update task status');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<TaskModel> addTaskComment(String taskId, String message) async {
    try {
      final response = await dioClient.post(
        '/tasks/$taskId/comments',
        data: {'message': message},
      );
      
      if (response.statusCode == 200) {
        return TaskModel.fromJson(response.data['data']);
      } else {
        throw ServerException('Failed to add comment');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }

  @override
  Future<Map<String, dynamic>> getTaskStats({String? teamId}) async {
    try {
      final queryParams = teamId != null ? {'teamId': teamId} : <String, dynamic>{};
      final response = await dioClient.get('/api/tasks/stats', queryParameters: queryParams);
      
      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        throw ServerException('Failed to get task stats');
      }
    } on DioException catch (e) {
      throw ServerException(e.response?.data['message'] ?? 'Network error occurred');
    } catch (e) {
      throw ServerException('Unexpected error occurred');
    }
  }
}
