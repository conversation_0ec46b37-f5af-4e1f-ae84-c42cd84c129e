// ... (imports gi<PERSON> nguyên) ...

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/features/home/<USER>/widgets/time_card.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:iconsax/iconsax.dart';
import 'package:intl/intl.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../l10n/app_localizations.dart';
import '../../../faceDetection/domain/entities/attendance_status_entity.dart'; // QUAN TRỌNG
import '../../../faceDetection/presentation/cubit/face_checkin_cubit.dart';
import '../../../faceDetection/presentation/cubit/face_checkin_state.dart';
import '../../../home/<USER>/widgets/digital_clock.dart';

class AttendanceSection extends StatelessWidget {
  const AttendanceSection({super.key});

  String _formatTime(DateTime? time) {
    if (time == null) {
      return "--:--:--";
    }
    final localTime = time.toLocal();
    return DateFormat("HH:mm:ss").format(localTime);
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    return BlocBuilder<FaceDetectionCubit, FaceDetectionState>(
      buildWhen: (prev, current) =>
          prev.isLoading != current.isLoading ||
          prev.attendanceStatus != current.attendanceStatus,
      builder: (context, state) {
        final attendanceStatus = state.attendanceStatus;

        final lastCheckInDisplayTime = _formatTime(
          attendanceStatus?.lastCheckInTime,
        );
        final lastCheckOutDisplayTime = _formatTime(
          attendanceStatus?.lastCheckOutTime,
        );
        final checkInsCount = attendanceStatus?.checkInsCount ?? 0;
        final checkOutsCount = attendanceStatus?.checkOutsCount ?? 0;

        String buttonText = l10n.homeFaceRecognition;
        IconData buttonIcon = Iconsax.scan_barcode;
        VoidCallback? buttonAction;
        bool isButtonEnabled = false;

        bool isCurrentlyWorking = false;
        if (attendanceStatus != null) {
          isCurrentlyWorking =
              attendanceStatus.hasCheckedIn &&
              !attendanceStatus.hasCheckedOut &&
              (attendanceStatus.nextAction == NextActionType.CHECK_OUT);
        }

        if (state.isLoading && attendanceStatus == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (attendanceStatus != null) {
          if (attendanceStatus.nextAction == NextActionType.CHECK_IN &&
              attendanceStatus.canCheckIn) {
            buttonText = l10n.detectFaceCheckIn;
            if (checkInsCount > 0) {
              buttonText = l10n.startNewSession; // "Bắt đầu phiên mới"
            }
            buttonIcon = Iconsax.login;
            buttonAction = () async {
              await context.pushNamed(
                'faceDetection',
                pathParameters: {'mode': 'checkIn'},
              );
              // Refresh dữ liệu khi quay về từ face detection
              if (context.mounted) {
                context.read<FaceDetectionCubit>().checkAttendanceStatus();
              }
            };
            isButtonEnabled = true;
          } else if (attendanceStatus.nextAction == NextActionType.CHECK_OUT &&
              attendanceStatus.canCheckOut) {
            buttonText = l10n.detectFaceCheckOut;
            buttonIcon = Iconsax.logout;
            buttonAction = () async {
              await context.pushNamed(
                'faceDetection',
                pathParameters: {'mode': 'checkOut'},
              );
              // Refresh dữ liệu khi quay về từ face detection
              if (context.mounted) {
                context.read<FaceDetectionCubit>().checkAttendanceStatus();
              }
            };
            isButtonEnabled = true;
          } else if (attendanceStatus.nextAction == NextActionType.UNKNOWN &&
              !attendanceStatus.canCheckIn &&
              !attendanceStatus.canCheckOut &&
              checkInsCount > 0 &&
              checkInsCount == checkOutsCount) {
            // Tất cả các phiên đã check-out đầy đủ, và API không cho phép thêm.
            buttonText = l10n.allSessionsCompleted;
            buttonIcon = Iconsax.task_square;
            isButtonEnabled = false;
          } else {
            // Các trường hợp khác: isLoading, lỗi từ server nhưng vẫn có data cũ, etc.
            buttonText = state.isLoading
                ? l10n.loading
                : l10n.noActionAvailable;
            buttonIcon = state.isLoading
                ? Icons.hourglass_empty
                : Iconsax.info_circle;
            isButtonEnabled = false;
          }
        } else {
          // attendanceStatus is null
          buttonText = state.isLoading
              ? l10n.loading
              : l10n.retryInitialization;
          buttonIcon = state.isLoading ? Icons.hourglass_empty : Icons.refresh;
          buttonAction = state.isLoading
              ? null
              : () => context.read<FaceDetectionCubit>().initialize();
          isButtonEnabled = !state.isLoading;
        }

        return Container(
          padding: responsive.padding(all: 20),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.withValues(alpha: 0.6)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(
                context,
                l10n,
                responsive,
                isCurrentlyWorking,
                checkInsCount,
              ),
              SizedBox(height: responsive.heightPercentage(1.5)),
              if (checkInsCount > 0 ||
                  checkOutsCount > 0) // Chỉ hiển thị nếu có dữ liệu
                _buildCountRow(
                  l10n,
                  responsive,
                  checkInsCount,
                  checkOutsCount,
                  context,
                ),
              if (checkInsCount > 0 || checkOutsCount > 0)
                SizedBox(height: responsive.heightPercentage(1.5)),
              const DigitalClock(),
              SizedBox(height: responsive.heightPercentage(2.0)),
              // TimeCard giờ sẽ hiển thị lastCheckInTime và lastCheckOutTime
              _buildCheckInOutRow(
                l10n,
                responsive,
                lastCheckInDisplayTime,
                lastCheckOutDisplayTime,
              ),
              SizedBox(height: responsive.heightPercentage(2.5)),
              _buildActionButton(
                context,
                responsive,
                buttonText,
                buttonIcon,
                isButtonEnabled ? buttonAction : null,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    AppLocalizations l10n,
    Responsive responsive,
    bool isCurrentlyWorking, // Thay isPresent bằng isCurrentlyWorking
    int checkInsCount, // Thêm checkInsCount
  ) {
    String title = l10n.homeTodayAttendance;
    // if (checkInsCount > 0) { // Có thể thêm thông tin vào title nếu muốn
    //   title = "${l10n.homeTodayAttendance} ($checkInsCount ${l10n.sessions.toLowerCase()})";
    // }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            title,
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        if (isCurrentlyWorking) // Chỉ hiển thị "Working" nếu đang trong phiên
          Container(
            padding: responsive.padding(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(30),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Iconsax.clock,
                  color: Colors.blue[700],
                  size: responsive.fontSize(14),
                ),
                SizedBox(width: responsive.widthPercentage(1.0)),
                Text(
                  l10n.working, // "Đang làm việc"
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue[700],
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          )
        else if (checkInsCount >
            0) // Nếu không đang làm việc nhưng đã có checkin
          Container(
            padding: responsive.padding(horizontal: 10, vertical: 5),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(30),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              l10n.notCurrentlyWorking, // "Ngoài giờ làm việc"
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[700],
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCountRow(
    AppLocalizations l10n,
    Responsive responsive,
    int checkIns,
    int checkOuts,
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildCountItem(
          context,
          Iconsax.login_1,
          l10n.totalCheckIns,
          checkIns,
          Colors.green,
        ),
        _buildCountItem(
          context,
          Iconsax.logout_1,
          l10n.totalCheckOuts,
          checkOuts,
          Colors.red,
        ),
      ],
    );
  }

  Widget _buildCountItem(
    BuildContext context,
    IconData icon,
    String label,
    int count,
    Color color,
  ) {
    final responsive = context.responsive;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: responsive.fontSize(20)),
        SizedBox(height: responsive.heightPercentage(0.5)),
        Text(
          '$count',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        ),
      ],
    );
  }

  // _buildCheckInOutRow và _buildActionButton giữ nguyên hoặc tùy chỉnh icon/text theo ý bạn
  Widget _buildCheckInOutRow(
    AppLocalizations l10n,
    Responsive responsive,
    String lastCheckInTime, // Đổi tên để rõ nghĩa là thời gian lần cuối
    String lastCheckOutTime,
  ) {
    return Row(
      children: [
        Expanded(
          child: TimeCard(
            label: l10n.lastCheckIn, // "Lần Check In cuối"
            time: lastCheckInTime,
            icon: Iconsax.login_1,
            color: Colors.teal, // Đổi màu cho khác
          ),
        ),
        SizedBox(width: responsive.widthPercentage(3.0)),
        Expanded(
          child: TimeCard(
            label: l10n.lastCheckOut, // "Lần Check Out cuối"
            time: lastCheckOutTime,
            icon: Iconsax.logout_1,
            color: Colors.orange, // Đổi màu cho khác
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    Responsive responsive,
    String label,
    IconData icon,
    VoidCallback? onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          padding: responsive.padding(vertical: 16),
          backgroundColor: onPressed != null
              ? AppColors.primaryBlue
              : Colors.grey.shade300,
          foregroundColor: onPressed != null
              ? AppColors.primaryBlue
              : Colors.grey.shade600,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          disabledBackgroundColor: AppColors.primaryBlue,
          disabledForegroundColor:AppColors.primaryBlue,
        ),
        icon: Icon(icon, size: responsive.fontSize(20), color: AppColors.white,),
        label: Text(
          label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
