import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:intl/intl.dart';

import '../../../leave/presentation/widgets/request_card_info_item.dart';
import '../../../leave/presentation/widgets/request_status_chip.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_admin_cubit.dart';
import 'overtime_rejection_dialog.dart';

class OvertimeAdminRequestCard extends StatelessWidget {
  final OvertimeRequestEntity request;
  final bool isProcessing;

  const OvertimeAdminRequestCard({
    super.key,
    required this.request,
    required this.isProcessing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 0.5,
      shadowColor: Colors.grey.withValues(alpha: 0.1),
      color:AppColors.cardBackground,
      margin: context.responsive.padding(vertical: 8, horizontal: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: AppColors.border
        )
      ),
      child: Padding(
        padding: context.responsive.padding(all: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            ListTile(
              contentPadding: EdgeInsets.zero,
              title: Text(
                request.employeeName,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              subtitle: Padding(
                padding: context.responsive.padding(top: 4.0),
                child: Text(
                  _formatDate(request.date),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
              trailing: RequestStatusChip(
                status: _covertStatus(request.status),
              ),
            ),

            const Divider(height: 24),

            // Phần thông tin
            RequestCardInfoItem(
              icon: Icons.access_time_rounded,
              label: context.l10n.time,
              value: _formatTimeRange(request.startTime, request.endTime),
            ),
             SizedBox(height: context.responsive.heightPercentage(1.7)),
            RequestCardInfoItem(
              icon: Icons.schedule_rounded,
              label: context.l10n.duration,
              value: '${request.hours.toStringAsFixed(1)}h',
            ),
            SizedBox(height: context.responsive.heightPercentage(1.7)),
            RequestCardInfoItem(
              icon: Icons.description_outlined,
              label: context.l10n.reason,
              value: request.reason,
            ),

            // Lý do từ chối
            if (request.rejectionReason != null) ...[
              SizedBox(height: context.responsive.heightPercentage(1.7)),
              RequestCardInfoItem(
                icon: Icons.gpp_bad_outlined,
                label: context.l10n.rejectionReason,
                value: request.rejectionReason!,
                valueColor: Colors.red[700],
              ),
            ],

            // Các nút hành động
            if (request.status == OvertimeStatus.pending) ...[
              const SizedBox(height: 24),
              _ActionButtons(
                isProcessing: isProcessing,
                onApprove: () => _approveRequest(context),
                onReject: () => _showRejectDialog(context),
              ),
            ],

            // Thông tin người duyệt
            if (request.status != OvertimeStatus.pending &&
                request.approvedAt != null) ...[
              const Divider(height: 32),
              _ApprovalInfo(
                status: request.status,
                approvedAt: request.approvedAt!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  RequestStatus _covertStatus(OvertimeStatus status) {
    switch (status) {
      case OvertimeStatus.pending:
        return RequestStatus.pending;
      case OvertimeStatus.approved:
        return RequestStatus.approved;
      case OvertimeStatus.rejected:
        return RequestStatus.rejected;
    }
  }

  void _approveRequest(BuildContext context) {
    context.read<OvertimeAdminCubit>().approveRequest(request.id);
  }

  void _showRejectDialog(BuildContext context) {
    final cubit = context.read<OvertimeAdminCubit>();
    showDialog(
      context: context,
      builder: (dialogContext) => OvertimeRejectionDialog(
        onReject: (reason) => cubit.rejectRequest(request.id, reason),
      ),
    );
  }

  // Helper methods
  String _formatDate(DateTime date) {
    return DateFormat('dd MMMM yyyy').format(date);
  }

  String _formatTimeRange(DateTime startTime, DateTime endTime) {
    final correctedStart = startTime.add(const Duration(hours: 7));
    final correctedEnd = endTime.add(const Duration(hours: 7));
    final start = DateFormat('HH:mm').format(correctedStart);
    final end = DateFormat('HH:mm').format(correctedEnd);
    return '$start - $end';
  }
}

// Các widget nội bộ được sao chép từ Leave Card để đảm bảo tính nhất quán
class _ActionButtons extends StatelessWidget {
  final bool isProcessing;
  final VoidCallback onApprove;
  final VoidCallback onReject;

  const _ActionButtons({
    required this.isProcessing,
    required this.onApprove,
    required this.onReject,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: isProcessing ? null : onReject,
            icon: const Icon(Icons.close),
            label: const Text('Reject'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: BorderSide(color: Colors.red.withValues(alpha: 0.5)),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: isProcessing ? null : onApprove,
            icon: const Icon(Icons.check),
            label: const Text('Approve'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
              elevation: 1,
            ),
          ),
        ),
      ],
    );
  }
}

class _ApprovalInfo extends StatelessWidget {
  final OvertimeStatus status;
  final DateTime approvedAt;

  const _ApprovalInfo({required this.status, required this.approvedAt});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isApproved = status == OvertimeStatus.approved;
    final color = isApproved ? Colors.green : Colors.red;
    final text = isApproved ? 'Approved' : 'Rejected';

    return Text.rich(
      TextSpan(
        style: theme.textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
        children: [
          TextSpan(
            text: text,
            style: TextStyle(fontWeight: FontWeight.bold, color: color),
          ),
          TextSpan(
            text: ' at ${DateFormat('dd/MM/yyyy HH:mm').format(approvedAt)}',
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }
}
