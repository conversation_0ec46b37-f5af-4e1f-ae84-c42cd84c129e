import 'package:dio/dio.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';

// Import các model cần thiết
import '../models/monthly_details_model.dart';
import '../models/monthly_summary_model.dart';

import '../models/paginated_attendance_history_model.dart';
import '../models/today_summary_model.dart';
import '../models/weekly_summary_model.dart';

abstract class AttendanceRemoteDataSourceV1 {
  Future<TodaySummaryModel> getTodaySummary();

  Future<WeeklySummaryModel> getWeeklySummary();

  Future<MonthlySummaryModel> getMonthlySummary();

  Future<MonthlyDetailsModel> getMonthlyDetails({
    required int year,
    required int month,
  });


  Future<PaginatedAttendanceHistoryModel> getAttendanceHistory({
    required int page,
    required int limit,
  });
}

class AttendanceRemoteDataSourceImpl implements AttendanceRemoteDataSourceV1 {
  final DioClient dioClient;

  AttendanceRemoteDataSourceImpl({required this.dioClient});

  // Hàm helper _getFromApi đã rất tốt, giữ nguyên.
  Future<T> _getFromApi<T>({
    required String endpoint,
    required T Function(dynamic json) fromJson,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final Response response = await dioClient.get(
        endpoint,
        queryParameters: queryParameters,
      );
      final responseData = response.data['data'];
      return fromJson(responseData);
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  // Các phương thức khác giữ nguyên
  @override
  Future<TodaySummaryModel> getTodaySummary() {
    return _getFromApi(
      endpoint: '/api/attendance/today-summary',
      fromJson: (json) => TodaySummaryModel.fromJson(json),
    );
  }

  @override
  Future<WeeklySummaryModel> getWeeklySummary() {
    return _getFromApi(
      endpoint: '/api/attendance/summary/week',
      fromJson: (json) => WeeklySummaryModel.fromJson(json),
    );
  }

  @override
  Future<MonthlySummaryModel> getMonthlySummary() {
    return _getFromApi(
      endpoint: '/api/attendance/summary/month',
      fromJson: (json) => MonthlySummaryModel.fromJson(json),
    );
  }

  @override
  Future<MonthlyDetailsModel> getMonthlyDetails({
    required int year,
    required int month,
  }) {
    return _getFromApi(
      endpoint: '/api/attendance/monthly-details',
      queryParameters: {'year': year, 'month': month},
      fromJson: (json) => MonthlyDetailsModel.fromJson(json),
    );
  }

  // 4. SỬA HÀM NÀY
  @override
  Future<PaginatedAttendanceHistoryModel> getAttendanceHistory({
    required int page,
    required int limit,
  }) {
    return _getFromApi(
      endpoint: '/api/attendance/history',
      queryParameters: {'page': page, 'limit': limit},
      // 5. GỌI ĐÚNG HÀM PARSE
      fromJson: (json) => PaginatedAttendanceHistoryModel.fromJson(json),
    );
  }
}
