import 'package:equatable/equatable.dart';

import '../../domain/entities/overtime_request_entity.dart';
import '../../domain/entities/overtime_summary_entity.dart';

enum OvertimeStateStatus { initial, loading, success, error }

class OvertimeState extends Equatable {
  final OvertimeStateStatus status;
  final OvertimeSummaryEntity? summary;
  final List<OvertimeRequestEntity> history;
  final List<ApproverEntity> approvers;
  final bool isLoadingHistory;
  final bool isLoadingApprovers;
  final bool isSubmitting;
  final String? errorMessage;
  final String? successMessage;
  final int currentPage;
  final bool hasMoreData;
  final OvertimeStatus? selectedFilter;
  final ApproverEntity? selectedApprover;

  const OvertimeState({
    this.status = OvertimeStateStatus.initial,
    this.summary,
    this.history = const [],
    this.approvers = const [],
    this.isLoadingHistory = false,
    this.isLoadingApprovers = false,
    this.isSubmitting = false,
    this.errorMessage,
    this.successMessage,
    this.currentPage = 1,
    this.hasMoreData = true,
    this.selectedFilter,
    this.selectedApprover,
  });

  @override
  List<Object?> get props => [
    status,
    summary,
    history,
    approvers,
    isLoadingHistory,
    isLoadingApprovers,
    isSubmitting,
    errorMessage,
    successMessage,
    currentPage,
    hasMoreData,
    selectedFilter,
    selectedApprover,
  ];

  OvertimeState copyWith({
    OvertimeStateStatus? status,
    OvertimeSummaryEntity? summary,
    List<OvertimeRequestEntity>? history,
    List<ApproverEntity>? approvers,
    bool? isLoadingHistory,
    bool? isLoadingApprovers,
    bool? isSubmitting,
    String? errorMessage,
    String? successMessage,
    int? currentPage,
    bool? hasMoreData,
    OvertimeStatus? selectedFilter,
    ApproverEntity? selectedApprover,
    bool clearErrorMessage = false,
    bool clearSuccessMessage = false,
    bool clearSelectedApprover = false,
  }) {
    return OvertimeState(
      status: status ?? this.status,
      summary: summary ?? this.summary,
      history: history ?? this.history,
      approvers: approvers ?? this.approvers,
      isLoadingHistory: isLoadingHistory ?? this.isLoadingHistory,
      isLoadingApprovers: isLoadingApprovers ?? this.isLoadingApprovers,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      errorMessage: clearErrorMessage
          ? null
          : (errorMessage ?? this.errorMessage),
      successMessage: clearSuccessMessage
          ? null
          : (successMessage ?? this.successMessage),
      currentPage: currentPage ?? this.currentPage,
      hasMoreData: hasMoreData ?? this.hasMoreData,
      selectedFilter: selectedFilter ?? this.selectedFilter,
      selectedApprover: clearSelectedApprover
          ? null
          : (selectedApprover ?? this.selectedApprover),
    );
  }
}
