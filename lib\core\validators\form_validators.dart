import 'package:flutter/material.dart';
import '../extensions/l10n_extension.dart';

/// Centralized form validators for the entire application
class FormValidators {
  FormValidators._();

  // ===== BASIC VALIDATORS =====
  
  /// Validates full name field
  static String? validateFullName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.loginPleaseEnterFullName;
    }
    if (value.trim().length < 2) {
      return 'Full name must be at least 2 characters';
    }
    return null;
  }

  /// Validates email field
  static String? validateEmail(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.loginPleaseEnterEmail;
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
      return context.l10n.loginPleaseEnterEmail;
    }
    return null;
  }

  /// Validates password field
  static String? validatePassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.loginPleaseEnterPassword;
    }
    if (value.length < 6) {
      return context.l10n.loginPasswordMinLength;
    }
    return null;
  }

  /// Validates phone number field
  static String? validatePhoneNumber(String? value, BuildContext context) {
    if (value != null && value.isNotEmpty) {
      if (!RegExp(r'^\+?[0-9]{10,15}$').hasMatch(value)) {
        return context.l10n.phoneInvalid;
      }
    }
    return null;
  }

  // ===== ADMIN USER VALIDATORS =====
  
  /// Validates first name field
  static String? validateFirstName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.firstNameRequired;
    }
    if (value.trim().length < 2) {
      return 'First name must be at least 2 characters';
    }
    return null;
  }

  /// Validates last name field
  static String? validateLastName(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.lastNameRequired;
    }
    if (value.trim().length < 2) {
      return 'Last name must be at least 2 characters';
    }
    return null;
  }

  /// Validates role field
  static String? validateRole(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.roleRequired;
    }
    return null;
  }

  // ===== LEAVE VALIDATORS =====
  
  /// Validates leave reason field
  static String? validateLeaveReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.reasonRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.reasonMinLength;
    }
    if (value.trim().length > 500) {
      return context.l10n.reasonMaxLength;
    }
    return null;
  }

  static String? validateReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.reasonRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.reasonMinLength;
    }
    if (value.trim().length > 500) {
      return context.l10n.reasonMaxLength;
    }
    return null;
  
  }

  /// Validates rejection reason field
  static String? validateRejectionReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.rejectionReasonRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.rejectionReasonTooShort;
    }
    if (value.trim().length > 500) {
      return context.l10n.rejectionReasonTooLong;
    }
    return null;
  }

  // ===== OVERTIME VALIDATORS =====
  
  /// Validates overtime reason field
  static String? validateOvertimeReason(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return context.l10n.reasonIsRequired;
    }
    if (value.trim().length < 10) {
      return context.l10n.reasonTooShort;
    }
    if (value.trim().length > 500) {
      return context.l10n.reasonTooLong;
    }
    return null;
  }

  /// Validates date field
  static String? validateDate(BuildContext context, DateTime? date) {
    if (date == null) {
      return context.l10n.pleaseSelectADate;
    }
    final today = DateTime.now();
    final todayOnly = DateTime(today.year, today.month, today.day);
    final selectedDateOnly = DateTime(date.year, date.month, date.day);

    if (selectedDateOnly.isBefore(todayOnly)) {
      return context.l10n.cannotSelectPastDates;
    }
    return null;
  }

  // ===== CALENDAR EVENT VALIDATORS =====
  
  /// Validates event title field
  static String? validateEventTitle(String? value, BuildContext context) {
    if (value == null || value.trim().isEmpty) {
      return 'Event title is required';
    }
    if (value.trim().length < 3) {
      return 'Event title must be at least 3 characters';
    }
    if (value.trim().length > 100) {
      return 'Event title must not exceed 100 characters';
    }
    return null;
  }

  /// Validates event description field
  static String? validateEventDescription(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 500) {
        return 'Event description must not exceed 500 characters';
      }
    }
    return null;
  }

  /// Validates event location field
  static String? validateEventLocation(String? value, BuildContext context) {
    if (value != null && value.trim().isNotEmpty) {
      if (value.trim().length > 200) {
        return 'Event location must not exceed 200 characters';
      }
    }
    return null;
  }

  /// Validates event time range
  static String? validateEventTimeRange(DateTime startTime, DateTime endTime, BuildContext context) {
    if (endTime.isBefore(startTime)) {
      return 'End time must be after start time';
    }
    
    final duration = endTime.difference(startTime);
    if (duration.inMinutes < 15) {
      return 'Event duration must be at least 15 minutes';
    }
    
    if (duration.inDays > 7) {
      return 'Event duration cannot exceed 7 days';
    }
    
    return null;
  }

  // ===== GENERIC VALIDATORS =====
  
  /// Validates required field
  static String? validateRequired(String? value, BuildContext context, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  /// Validates minimum length
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value != null && value.trim().length < minLength) {
      return '$fieldName must be at least $minLength characters';
    }
    return null;
  }

  /// Validates maximum length
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.trim().length > maxLength) {
      return '$fieldName must not exceed $maxLength characters';
    }
    return null;
  }
  
  static String? validateConfirmPassword(
    String? value,
    String password,
    BuildContext context,
  ) {
    if (value == null || value.isEmpty) {
      return context.l10n.registerPleaseConfirmPassword;
    }
    if (value != password) {
      return context.l10n.registerPasswordsDoNotMatch;
    }
    return null;
  }
  
  static String? validateStrongPassword(String? value, BuildContext context) {
    if (value == null || value.isEmpty) {
      return context.l10n.passwordRequired;
    }
    if (value.length < 8) {
      return context.l10n.passwordTooShort;
    }
    // Check for uppercase, lowercase, number, and special character
    if (!RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
    ).hasMatch(value)) {
      return context.l10n.passwordTooWeak;
    }
    return null;
  }
}
