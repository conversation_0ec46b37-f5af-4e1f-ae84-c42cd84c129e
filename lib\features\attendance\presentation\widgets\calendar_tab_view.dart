import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/monthly_details.dart';
import 'calendar_section_widget.dart';
import 'day_details_section_widget.dart';

class CalendarTabView extends StatefulWidget {
  final MonthlyDetails monthlyDetails;
  final DateTime focusedDay;
  final AttendanceDayDetail? selectedDayInfo;

  const CalendarTabView({
    super.key,
    required this.monthlyDetails,
    required this.focusedDay,
    this.selectedDayInfo,
  });

  @override
  State<CalendarTabView> createState() => _CalendarTabViewState();
}

class _CalendarTabViewState extends State<CalendarTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  // Helper để chuyển đổi List<Entity> thành Map, tiện lợi cho TableCalendar.
  Map<DateTime, AttendanceDayDetail> _createEventMap(
    List<AttendanceDayDetail> details,
  ) {
    return {
      for (var detail in details)
        DateTime.utc(detail.date.year, detail.date.month, detail.date.day):
            detail,
    };
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final responsive = context.responsive;
    final eventMap = _createEventMap(widget.monthlyDetails.dailyDetails);

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
        ),
      ),
      child: SingleChildScrollView(
        padding: responsive.padding(all: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Calendar Section
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: CalendarSectionWidget(
                focusedDay: widget.focusedDay,
                selectedDay: widget.selectedDayInfo?.date,
                eventMap: eventMap,
              ),
            ),

            SizedBox(height: responsive.heightPercentage(2)),

            // Day Details Section
            if (widget.selectedDayInfo != null) ...[
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: DayDetailsSectionWidget(
                  selectedDayInfo: widget.selectedDayInfo,
                ),
              ),
            ] else ...[
              // Empty state when no day is selected
              Container(
                width: double.infinity,
                padding: responsive.padding(all: 24.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 48,
                      color: AppColors.textSecondary.withValues(alpha: 0.5),
                    ),
                    SizedBox(height: responsive.heightPercentage(1)),
                    Text(
                      'Select a date to view details',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: responsive.heightPercentage(0.5)),
                    Text(
                      'Tap on any date in the calendar above to see attendance details',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.textSecondary.withValues(alpha: 0.7),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],

            SizedBox(height: responsive.heightPercentage(2)),
          ],
        ),
      ),
    );
  }
}
