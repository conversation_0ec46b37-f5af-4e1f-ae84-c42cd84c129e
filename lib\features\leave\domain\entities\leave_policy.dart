class LeavePolicy {
  final String leaveType;
  final String description;
  final int maxDaysPerYear;
  final int maxDaysPerRequest;
  final int advanceNoticeDays;
  final int carryOverDays;
  final bool isActive;

  const LeavePolicy({
    required this.leaveType,
    required this.description,
    required this.maxDaysPerYear,
    required this.maxDaysPerRequest,
    required this.advanceNoticeDays,
    this.carryOverDays = 0,
    this.isActive = true,
  });

  // Helper getter for display title
  String get title {
    switch (leaveType) {
      case 'annual':
        return 'Annual Leave';
      case 'sick':
        return 'Sick Leave';
      case 'personal':
        return 'Personal Leave';
      case 'maternity':
        return 'Maternity Leave';
      case 'unpaid':
        return 'Unpaid Leave';
      default:
        return leaveType;
    }
  }

  // Helper getter for backward compatibility
  int? get maxDays => maxDaysPerYear;
}
