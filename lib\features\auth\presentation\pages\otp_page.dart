import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';

import '../../../../core/animation/auth_page_animation_mixin.dar.dart';
import '../../../../core/animation/fade_slide_animation.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../shared/widgets/gradient_background.dart';
import '../cubit/auth_cubit.dart';
import '../cubit/auth_state.dart';
import '../widgets/arrow_appbar.dart';
import '../widgets/auth_card.dart';
import '../widgets/auth_welcome_section.dart';

class OtpPage extends StatefulWidget {
  const OtpPage({super.key});

  @override
  State<OtpPage> createState() => _OtpPageState();
}

class _OtpPageState extends State<OtpPage>
    with TickerProviderStateMixin, AuthPageAnimationMixin {
  final _formKey = GlobalKey<FormState>();

  Timer? _timer;
  int _start = 600;
  bool _canResend = false;

  final List<TextEditingController> _otpControllers = List.generate(
    4,
    (_) => TextEditingController(),
  );
  final List<FocusNode> _focusNodes = List.generate(4, (_) => FocusNode());

  @override
  void initState() {
    super.initState();
    initializeAnimations();
    startTimer();
  }

  void startTimer() {
    _timer?.cancel();
    _canResend = false;
    _start = 600;

    _timer = Timer.periodic(const Duration(seconds: 1), (Timer currentTimer) {
      if (_start == 0) {
        setState(() {
          _canResend = true;
          currentTimer.cancel();
        });
      } else {
        setState(() {
          _start--;
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _otpControllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    disposeAnimations();
    super.dispose();
    _timer?.cancel();
  }

  void _handleOtpSubmit() {
    if (_formKey.currentState!.validate()) {
      final otp = _otpControllers.map((c) => c.text).join();
      context.read<AuthCubit>().verifyOtp(otp);
    }
  }

  void _handleResendCode() {
    startTimer();
    context.read<AuthCubit>().resendOtp();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state is ForgotPasswordOtpVerified) {
          _timer?.cancel();
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: context.l10n.otpVerifySuccessfully,
          );
          context.push(AppRoutes.newPassword);
        } else if (state is AuthError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.message,
            isError: true,
          );
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        extendBodyBehindAppBar: true,
        body: GradientBackground(
          child: SafeArea(
            child: SingleChildScrollView(
              keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
              physics: const ClampingScrollPhysics(),
              child: SizedBox(
                height:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top,
                child: Padding(
                  padding: responsive.padding(horizontal: 24, vertical: 16),
                  child: Column(
                    children: [
                      const ArrowAppBar(),
                      SizedBox(height: responsive.heightPercentage(8)),
                      FadeSlideAnimation(
                        fadeAnimation: fadeAnimation,
                        slideAnimation: slideAnimation,
                        child: AuthWelcomeSection(
                          title: context.l10n.authVerifyOtp,
                          subtitle: context.l10n.authSubTitleVerifyOtp,
                          showLogo: false,
                        ),
                      ),
                      SizedBox(height: responsive.heightPercentage(4)),
                      FadeSlideAnimation(
                        fadeAnimation: fadeAnimation,
                        slideAnimation: slideAnimation,
                        child: AuthCard(
                          child: Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: List.generate(4, (index) {
                                    return SizedBox(
                                      width: responsive.widthPercentage(15),
                                      child: TextFormField(
                                        controller: _otpControllers[index],
                                        focusNode: _focusNodes[index],
                                        keyboardType: TextInputType.number,
                                        textAlign: TextAlign.center,
                                        maxLength: 1,
                                        inputFormatters: [
                                          FilteringTextInputFormatter
                                              .digitsOnly,
                                        ],
                                        decoration: InputDecoration(
                                          counterText: '',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(
                                              20,
                                            ),
                                          ),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                vertical: 12,
                                              ),
                                        ),
                                        validator: (value) =>
                                            value!.isEmpty ? '' : null,
                                        onChanged: (value) {
                                          if (value.length == 1 && index < 3) {
                                            _focusNodes[index + 1]
                                                .requestFocus();
                                          } else if (value.isEmpty &&
                                              index > 0) {
                                            _focusNodes[index - 1]
                                                .requestFocus();
                                          }
                                          if (index == 3 && value.length == 1) {
                                            _handleOtpSubmit();
                                          }
                                        },
                                      ),
                                    );
                                  }),
                                ),
                                SizedBox(
                                  height: responsive.heightPercentage(3),
                                ),
                                BlocBuilder<AuthCubit, AuthState>(
                                  builder: (context, state) {
                                    final isLoading = state is AuthLoading;
                                    return isLoading
                                        ? const CircularProgressIndicator()
                                        : ButtonCustom(
                                            text: context.l10n.authVerifyOtp,
                                            onPressed: _handleOtpSubmit,
                                          );
                                  },
                                ),
                                SizedBox(
                                  height: responsive.heightPercentage(2),
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      context.l10n.otpNotReceived,
                                      style: context
                                          .lightTheme
                                          .textTheme
                                          .bodyLarge!
                                          .copyWith(
                                            fontWeight: FontWeight.bold,
                                          ),
                                    ),
                                    TextButton(
                                      onPressed: _canResend
                                          ? _handleResendCode
                                          : null,
                                      child: _canResend
                                          ? Text(context.l10n.resendCode)
                                          : Text(
                                              '${(_start ~/ 60).toString().padLeft(2, '0')}:${(_start % 60).toString().padLeft(2, '0')}',
                                              style: context
                                                  .lightTheme
                                                  .textTheme
                                                  .bodyLarge!
                                                  .copyWith(color: Colors.grey),
                                            ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
