import 'package:flutter/material.dart';

import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../widgets/add_event_dialog.dart';
import '../widgets/agenda_view_widget.dart';
import '../widgets/calendar_header_widget.dart';
import '../widgets/calendar_tab_bar_widget.dart';
import '../widgets/month_view_widget.dart';
import '../widgets/search_events_dialog.dart';
import '../widgets/week_view_widget.dart';

class CalendarView extends StatefulWidget {
  const CalendarView({super.key});

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CalendarHeaderWidget(),
        SizedBox(height: context.rh(16)),
        CalendarTabBarWidget(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [MonthViewWidget(), WeekViewWidget(), AgendaViewWidget()],
          ),
        ),
      ],
    );
  }

  static void showAddEventDialog(BuildContext context) {
    // Use CalendarDialogs instead for proper provider setup
    // CalendarDialogs.showAddEventDialog(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => const AddEventDialog(),
    );
  }

  static void showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => const SearchEventsDialog(),
    );
  }

  static Future<bool> showDeleteConfirmDialog(
    BuildContext context,
    String eventTitle,
  ) async {
    final l10n = context.l10n;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          l10n.deleteEvent,
          style: const TextStyle(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          l10n.confirmDeleteEvent,
          style: const TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              l10n.cancel,
              style: const TextStyle(color: AppColors.textSecondary),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(l10n.delete),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  static void showEventOptionsBottomSheet(
    BuildContext context,
    String eventId,
    String eventTitle,
  ) {
    final l10n = context.l10n;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(context.rw(20)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withOpacity(0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(height: context.rh(20)),
            Text(
              eventTitle,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: context.rh(24)),
            _buildBottomSheetOption(
              context,
              icon: Icons.edit_outlined,
              title: l10n.editEvent,
              onTap: () {
                Navigator.pop(context);
                showAddEventDialog(context); // Open add/edit dialog
              },
            ),
            _buildBottomSheetOption(
              context,
              icon: Icons.delete_outline,
              title: l10n.deleteEvent,
              isDestructive: true,
              onTap: () async {
                Navigator.pop(context);
                final shouldDelete = await showDeleteConfirmDialog(
                  context,
                  eventTitle,
                );
                if (shouldDelete) {
                  // No-op for delete since no Cubit
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        l10n.eventDeletedSuccessfully,
                        style: const TextStyle(color: Colors.white),
                      ),
                      backgroundColor: AppColors.success,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      margin: EdgeInsets.all(context.rw(16)),
                    ),
                  );
                }
              },
            ),
            SizedBox(height: context.rh(16)),
          ],
        ),
      ),
    );
  }

  static Widget _buildBottomSheetOption(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: context.rw(16),
          vertical: context.rh(16),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? AppColors.error : AppColors.textPrimary,
              size: context.rf(24),
            ),
            SizedBox(width: context.rw(16)),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isDestructive ? AppColors.error : AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
