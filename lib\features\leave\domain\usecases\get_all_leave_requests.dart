import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/leave_request.dart';
import '../repositories/leave_admin_repository.dart';

class GetAllLeaveRequests
    implements UseCase<List<LeaveRequest>, GetAllLeaveRequestsParams> {
  final LeaveAdminRepository repository;

  GetAllLeaveRequests(this.repository);

  @override
  Future<Either<Failure, List<LeaveRequest>>> call(
    GetAllLeaveRequestsParams params,
  ) async {
    return await repository.getAllLeaveRequests(
      page: params.page,
      limit: params.limit,
      status: params.status,
    );
  }
}

class GetAllLeaveRequestsParams extends Equatable {
  final int page;
  final int limit;
  final String? status;

  const GetAllLeaveRequestsParams({
    this.page = 1,
    this.limit = 10,
    this.status,
  });

  @override
  List<Object?> get props => [page, limit, status];
}
