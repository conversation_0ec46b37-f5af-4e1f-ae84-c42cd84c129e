import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../entities/overtime_request_entity.dart';
import '../entities/overtime_summary_entity.dart';

abstract class OvertimeRepository {
  /// Get overtime summary for current user
  Future<Either<Failure, OvertimeSummaryEntity>> getOvertimeSummary();

  /// Get overtime history for current user
  Future<Either<Failure, List<OvertimeRequestEntity>>> getOvertimeHistory({
    int page = 1,
    int limit = 10,
    OvertimeStatus? status,
  });

  /// Get list of approvers (admin, hr, manager roles)
  Future<Either<Failure, List<ApproverEntity>>> getApprovers();

  /// Submit new overtime request
  Future<Either<Failure, OvertimeRequestEntity>> submitOvertimeRequest({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  });

  /// Update overtime request (for editing pending requests)
  Future<Either<Failure, OvertimeRequestEntity>> updateOvertimeRequest({
    required String requestId,
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    String? approverId,
  });

  /// Cancel overtime request (only pending requests)
  Future<Either<Failure, bool>> cancelOvertimeRequest(String requestId);

  /// Get overtime request details by ID
  Future<Either<Failure, OvertimeRequestEntity>> getOvertimeRequestById(
    String requestId,
  );
}
