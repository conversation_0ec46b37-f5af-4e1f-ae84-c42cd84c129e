import 'dart:io';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import 'package:golderhr/core/services/device_info_service.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/location_widget.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:golderhr/shared/widgets/text_field_custom.dart';
import 'package:iconsax/iconsax.dart';
import 'package:image_picker/image_picker.dart';

import '../../domain/usecase/submit_manual_attendance.dart';

class ManualAttendancePage extends StatefulWidget {
  final bool isCheckIn;
  final Position? currentLocation;

  const ManualAttendancePage({
    super.key,
    required this.isCheckIn,
    this.currentLocation,
  });

  @override
  State<ManualAttendancePage> createState() => _ManualAttendancePageState();
}

class _ManualAttendancePageState extends State<ManualAttendancePage> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _reasonController = TextEditingController();

  File? _failureImage;
  Map<String, dynamic> _deviceInfo = {};
  Position? _currentLocation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _getDeviceInfo();
    // Khởi tạo location từ widget.currentLocation nếu có
    if (widget.currentLocation != null) {
      _currentLocation = widget.currentLocation;
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _reasonController.dispose();
    super.dispose();
  }

  Future<void> _getDeviceInfo() async {
    try {
      // Use the centralized device info service
      final deviceInfoService = DeviceInfoService();
      final info = await deviceInfoService.getDeviceInfo();

      // Thêm thông tin vị trí nếu có
      if (widget.currentLocation != null) {
        info['location'] = {
          'latitude': widget.currentLocation!.latitude,
          'longitude': widget.currentLocation!.longitude,
          'accuracy': widget.currentLocation!.accuracy,
          'timestamp': widget.currentLocation!.timestamp.toIso8601String(),
        };
      }

      setState(() {
        _deviceInfo = info;
      });
    } catch (e) {
      AppLogger.error('Error getting device info: $e');
    }
  }

  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _failureImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(
          context,
          title: 'Lỗi',
          message: 'Lỗi khi chụp ảnh: $e',
          isError: true,
        );
      }
    }
  }

  void _removeImage() {
    setState(() {
      _failureImage = null;
    });
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final useCase = GetIt.I<SubmitManualAttendanceUseCase>();
      final result = await useCase.call(
        ManualAttendanceParams(
          fullName: _fullNameController.text.trim(),
          reason: _reasonController.text.trim(),
          failureImage: _failureImage,
          deviceInfo: _deviceInfo,
          isCheckIn: widget.isCheckIn,
          location: _currentLocation ?? widget.currentLocation,
          address: null, // Địa chỉ sẽ được lấy từ backend dựa trên tọa độ
        ),
      );

      result.fold(
        (failure) {
          if (mounted) {
            showTopSnackBar(
              context,
              title: 'Lỗi',
              message: failure.message,
              isError: true,
            );
          }
        },
        (_) {
          if (mounted) {
            showTopSnackBar(
              context,
              title: 'Thành công',
              message: widget.isCheckIn
                  ? 'Chấm công vào thủ công thành công!'
                  : 'Chấm công ra thủ công thành công!',
              isError: false,
            );
            context.pop();
          }
        },
      );
    } catch (e) {
      if (mounted) {
        showTopSnackBar(
          context,
          title: 'Lỗi',
          message: 'Lỗi khi gửi: $e',
          isError: true,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;
    final theme = context.lightTheme;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              title: Text(
                widget.isCheckIn
                    ? 'Chấm công vào thủ công'
                    : 'Chấm công ra thủ công',
                style: theme.textTheme.headlineLarge,
              ),
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.grey[800],
                  size: responsive.fontSize(20),
                ),
                onPressed: () => context.pop(),
              ),
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
              floating: true,
              snap: true,
              pinned: false,
            ),
          ];
        },
        body: Container(
          color: Colors.white,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                          // Info card
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.info_circle,
                                  color: Colors.blue.shade600,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'Vui lòng điền đầy đủ thông tin để hoàn tất chấm công thủ công',
                                    style: TextStyle(
                                      fontSize: responsive.fontSize(14),
                                      color: Colors.blue.shade800,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Họ và tên
                          _buildFieldLabel('Họ và tên *', context),
                          const SizedBox(height: 8),
                          TextFieldCustom(
                            controller: _fullNameController,
                            hintText: 'Nhập họ và tên đầy đủ',
                            prefixIcon: Iconsax.user,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Vui lòng nhập họ và tên';
                              }
                              if (value.trim().length < 2) {
                                return 'Họ và tên phải có ít nhất 2 ký tự';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // Lý do chấm công thất bại
                          _buildFieldLabel(
                            'Lý do chấm công thất bại *',
                            context,
                          ),
                          const SizedBox(height: 8),
                          TextFieldCustom(
                            controller: _reasonController,
                            hintText:
                                'Mô tả lý do không thể chấm công bằng khuôn mặt',
                            prefixIcon: Iconsax.message_text,
                            maxLines: 2,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Vui lòng nhập lý do';
                              }
                              if (value.trim().length < 10) {
                                return 'Lý do phải có ít nhất 10 ký tự';
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 20),

                          // Hình ảnh chấm công thất bại
                          _buildFieldLabel(
                            'Hình ảnh chấm công thất bại (tùy chọn)',
                            context,
                          ),
                          const SizedBox(height: 8),
                          _buildImageSection(context),

                          const SizedBox(height: 20),

                          // Location info
                          _buildLocationSection(context),

                          const SizedBox(height: 32),
                          // Submit button
                          ButtonCustom(
                            text: widget.isCheckIn
                                ? 'Xác nhận chấm công vào'
                                : 'Xác nhận chấm công ra',
                            onPressed: _isLoading ? null : _submit,
                            isLoading: _isLoading,
                            backgroundColor: widget.isCheckIn
                                ? Colors.green
                                : Colors.orange,
                          ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFieldLabel(String label, BuildContext context) {
    return Text(
      label,
      style: TextStyle(
        fontSize: context.responsive.fontSize(14),
        fontWeight: FontWeight.w600,
        color: Colors.grey.shade700,
      ),
    );
  }

  Widget _buildImageSection(BuildContext context) {
    final responsive = context.responsive;
    final l10n = context.l10n;

    if (_failureImage == null) {
      return InkWell(
        onTap: _pickImage,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 300,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 2),
            borderRadius: BorderRadius.circular(12),
            color: Colors.grey.shade50,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Iconsax.camera, size: 40, color: Colors.grey.shade600),
              const SizedBox(height: 12),
              Text(
                'Chụp ảnh minh chứng',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: responsive.fontSize(16),
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Ảnh chụp màn hình lỗi hoặc khuôn mặt',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: responsive.fontSize(12),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.file(
            _failureImage!,
            height: 400,
            width: double.infinity,
            fit: BoxFit.cover,
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: InkWell(
            onTap: _removeImage,
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.close, color: Colors.white, size: 18),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLocationSection(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: LocationWidget(
        initialLocation: widget.currentLocation,
        onLocationChanged: (position) {
          setState(() {
            _currentLocation = position;
          });
        },
        showAccuracy: true,
        autoGetLocation: widget.currentLocation == null,
      ),
    );
  }
}
