import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/extensions/l10n_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/theme/app_text_styles.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../../../shared/widgets/wheel_time_picker.dart';
import '../../../upload_employee_face/domain/entities/user_for_dropdown_entity.dart';
import '../../../upload_employee_face/presentation/cubit/upload_face_cubit.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import '../validator/event_validator.dart';

class AddEventDialog extends StatefulWidget {
  final CalendarEventEntity? eventToEdit;
  final DateTime? selectedDate;

  const AddEventDialog({super.key, this.eventToEdit, this.selectedDate});

  @override
  State<AddEventDialog> createState() => _AddEventDialogState();
}

class _AddEventDialogState extends State<AddEventDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  DateTime _startTime = DateTime.now();
  DateTime _endTime = DateTime.now().add(const Duration(hours: 1));
  CalendarEventType _selectedEventType = CalendarEventType.meeting;
  bool _isAllDay = false;
  bool _isRecurring = false;
  String _selectedColor = '#2196F3';
  List<String> _selectedAttendees = [];
  List<UserForDropdownEntity> _availableUsers = [];

  @override
  void initState() {
    super.initState();
    _initializeFields();
    _loadUsers();
  }

  void _initializeFields() {
    if (widget.eventToEdit != null) {
      final event = widget.eventToEdit!;
      _titleController.text = event.title;
      _descriptionController.text = event.description ?? '';
      _locationController.text = event.location ?? '';
      _selectedDate = DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      );
      _startTime = event.startTime;
      _endTime = event.endTime;
      _selectedEventType = event.type;
      _isAllDay = event.isAllDay;
      _isRecurring = event.isRecurring;
      _selectedColor = event.color;
      _selectedAttendees = event.attendees.map((user) => user.id).toList();
    } else {
      _selectedDate = widget.selectedDate ?? DateTime.now();
      _startTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        9,
        0,
      );
      _endTime = _startTime.add(const Duration(hours: 1));
    }
  }

  void _loadUsers() async {
    final uploadFaceCubit = context.read<UploadFaceCubit>();
    await uploadFaceCubit.fetchUsers();
    if (uploadFaceCubit.state.users.isNotEmpty) {
      setState(() {
        _availableUsers = uploadFaceCubit.state.users;
      });
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final isEditing = widget.eventToEdit != null;

    return BlocListener<CalendarCubit, CalendarState>(
      listenWhen: (previous, current) {
        return (previous.isProcessing != current.isProcessing) ||
            (previous.successMessage != current.successMessage) ||
            (previous.error != current.error);
      },
      listener: (context, state) {
        if (state.successMessage != null && !state.isProcessing) {
          showTopSnackBar(
            context,
            title: l10n.success,
            message: state.successMessage!,
            isError: false,
          );

          // Clear messages immediately
          context.read<CalendarCubit>().clearMessages();

          // Close dialog after a short delay to allow snackbar to show
          final navigator = Navigator.of(context);
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted) {
              navigator.pop();
            }
          });
        }

        // Handle error case
        if (state.error != null && !state.isProcessing) {
          // Show error message
          showTopSnackBar(
            context,
            title: l10n.error,
            message: state.error!,
            isError: true,
          );

          // Clear error message after showing
          context.read<CalendarCubit>().clearMessages();
        }
      },
      child: Dialog(
        child: Container(
          width: context.responsive.widthPercentage(90),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          child: Material(
            borderRadius: BorderRadius.circular(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context, l10n, isEditing),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Form(
                      key: _formKey,
                      child: Column(
                        children: [
                          _buildTitleField(context, l10n),
                          const SizedBox(height: 16),
                          _buildDescriptionField(context, l10n),
                          const SizedBox(height: 16),
                          _buildDateTimeFields(context, l10n),
                          const SizedBox(height: 16),
                          _buildEventTypeField(context, l10n),
                          const SizedBox(height: 16),
                          _buildLocationField(context, l10n),
                          const SizedBox(height: 16),
                          _buildAttendeesField(context, l10n),
                          const SizedBox(height: 16),
                          _buildOptionsFields(context, l10n),
                        ],
                      ),
                    ),
                  ),
                ),
                _buildActions(context, l10n, isEditing),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, dynamic l10n, bool isEditing) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Icon(
            isEditing ? Icons.edit : Icons.add,
            color: AppColors.primaryBlue,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              isEditing ? l10n.editEvent : l10n.addEvent,
              style: AppTextStyle.bold(
                context,
                size: 18,
                color: AppColors.textPrimary,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _titleController,
      decoration: InputDecoration(
        labelText: l10n.eventTitle,
        prefixIcon: const Icon(Icons.title),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      validator: (value) => EventValidator.validateTitle(value, context),
    );
  }

  Widget _buildDescriptionField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _descriptionController,
      maxLines: 1,
      decoration: InputDecoration(
        labelText: l10n.eventDescription,
        prefixIcon: const Icon(Icons.description),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildDateTimeFields(BuildContext context, dynamic l10n) {
    return Column(
      children: [
        // Date selection
        InkWell(
          onTap: _selectDate,
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: l10n.eventDate,
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              _formatDate(_selectedDate),
              style: context.lightTheme.textTheme.titleMedium,
            ),
          ),
        ),
        if (!_isAllDay) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => _selectTime(true),
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: context.l10n.startTime,
                      prefixIcon: const Icon(Icons.access_time),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _formatTime(_startTime),
                      style: context.lightTheme.textTheme.titleMedium,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: InkWell(
                  onTap: () => _selectTime(false),
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: context.l10n.endTime,
                      prefixIcon: const Icon(Icons.access_time_filled),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      _formatTime(_endTime),
                      style: context.lightTheme.textTheme.titleMedium,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildEventTypeField(BuildContext context, dynamic l10n) {
    return DropdownButtonFormField<CalendarEventType>(
      value: _selectedEventType,
      decoration: InputDecoration(
        labelText: l10n.eventType,
        prefixIcon: Icon(_getEventTypeIcon(_selectedEventType)),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      items: CalendarEventType.values.map((type) {
        return DropdownMenuItem(
          value: type,
          child: Row(
            children: [
              const SizedBox(width: 8),
              Text(_getEventTypeDisplayName(type)),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() {
            _selectedEventType = value;
          });
        }
      },
    );
  }

  Widget _buildLocationField(BuildContext context, dynamic l10n) {
    return TextFormField(
      controller: _locationController,
      decoration: InputDecoration(
        labelText: l10n.eventLocation,
        prefixIcon: const Icon(Icons.location_on),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Widget _buildAttendeesField(BuildContext context, dynamic l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          l10n.attendees,
          style: AppTextStyle.medium(
            context,
            size: 16,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // Selected attendees display
              if (_selectedAttendees.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(12),
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _selectedAttendees.map((userId) {
                      UserForDropdownEntity? foundUser;
                      try {
                        foundUser = _availableUsers.firstWhere(
                          (u) => u.id == userId,
                        );
                      } catch (e) {
                        foundUser = null;
                      }
                      final user =
                          foundUser ??
                          const UserForDropdownEntity(
                            id: 'unknown',
                            fullname: 'Unknown User',
                            email: '',
                          );
                      return Chip(
                        label: Text(user.fullname),
                        deleteIcon: const Icon(Icons.close, size: 18),
                        onDeleted: () {
                          setState(() {
                            _selectedAttendees.remove(userId);
                          });
                        },
                      );
                    }).toList(),
                  ),
                ),
              // Add attendee button
              ListTile(
                leading: const Icon(Icons.person_add),
                title: Text(
                  _selectedAttendees.isEmpty
                      ? l10n.addAttendees
                      : l10n.addMoreAttendees,
                ),
                onTap: _showAttendeesSelector,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOptionsFields(BuildContext context, dynamic l10n) {
    return Row(
      children: [
        Expanded(
          child: Row(
            children: [
              Checkbox(
                value: _isAllDay,
                onChanged: (value) {
                  setState(() {
                    _isAllDay = value ?? false;
                    if (_isAllDay) {
                      _startTime = DateTime(
                        _selectedDate.year,
                        _selectedDate.month,
                        _selectedDate.day,
                      );
                      _endTime = DateTime(
                        _selectedDate.year,
                        _selectedDate.month,
                        _selectedDate.day,
                        23,
                        59,
                      );
                    } else {
                      _startTime = DateTime(
                        _selectedDate.year,
                        _selectedDate.month,
                        _selectedDate.day,
                        9,
                        0,
                      );
                      _endTime = _startTime.add(const Duration(hours: 1));
                    }
                  });
                },
              ),
              Text(l10n.allDay),
            ],
          ),
        ),
        Expanded(
          child: Row(
            children: [
              Checkbox(
                value: _isRecurring,
                onChanged: (value) {
                  setState(() {
                    _isRecurring = value ?? false;
                  });
                },
              ),
              Text(l10n.recurring),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context, dynamic l10n, bool isEditing) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: BlocBuilder<CalendarCubit, CalendarState>(
        builder: (context, state) {
          return Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: state.isProcessing
                      ? null
                      : () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.white.withValues(alpha: 0.5),
                    foregroundColor: Colors.red,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(l10n.cancel),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: state.isProcessing ? null : _saveEvent,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: state.isProcessing
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Text(isEditing ? l10n.save : l10n.addEvent),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showAttendeesSelector() {
    showDialog(
      context: context,
      builder: (context) => AttendeesSelector(
        availableUsers: _availableUsers,
        selectedAttendees: _selectedAttendees,
        onSelectionChanged: (updatedAttendees) {
          setState(() {
            _selectedAttendees = updatedAttendees;
          });
        },
      ),
    );
  }

  void _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _saveEvent() async {
    // Validate form first
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate event using EventValidator
    final validationErrors = EventValidator.validateCompleteEvent(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      location: _locationController.text.trim(),
      startTime: _startTime,
      endTime: _endTime,
      attendees: _selectedAttendees,
      isAllDay: _isAllDay,
      isRecurring: _isRecurring,
      context: context,
    );

    if (validationErrors.isNotEmpty) {
      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: validationErrors.first,
        isError: true,
      );
      return;
    }

    final cubit = context.read<CalendarCubit>();

    if (widget.eventToEdit != null) {
      await cubit.updateEvent(
        eventId: widget.eventToEdit!.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        startDate: _startTime,
        endDate: _endTime,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        type: _selectedEventType,
        color: _selectedColor,
        attendees: _selectedAttendees,
        isAllDay: _isAllDay,
        isRecurring: _isRecurring,
      );
    } else {
      await cubit.createEvent(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        startDate: _startTime,
        endDate: _endTime,
        location: _locationController.text.trim().isEmpty
            ? null
            : _locationController.text.trim(),
        type: _selectedEventType,
        color: _selectedColor,
        attendees: _selectedAttendees,
        isAllDay: _isAllDay,
        isRecurring: _isRecurring,
      );
    }

    // Refresh calendar events to sync UI with database
    await cubit.loadEvents();
  }

  String _formatDate(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]} ${date.year}';
  }

  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  void _selectTime(bool isStartTime) async {
    final initialTime = TimeOfDay.fromDateTime(
      isStartTime ? _startTime : _endTime,
    );
    final l10n = context.l10n;

    showDialog(
      context: context,
      builder: (context) => WheelTimePicker(
        initialTime: initialTime,
        title: isStartTime ? l10n.startTime : l10n.endTime,
        onTimeSelected: (picked) {
          setState(() {
            final newDateTime = DateTime(
              _selectedDate.year,
              _selectedDate.month,
              _selectedDate.day,
              picked.hour,
              picked.minute,
            );

            if (isStartTime) {
              _startTime = newDateTime;
              // Ensure end time is after start time
              if (_endTime.isBefore(_startTime)) {
                _endTime = _startTime.add(const Duration(hours: 1));
              }
            } else {
              _endTime = newDateTime;
              // Ensure end time is after start time
              if (_endTime.isBefore(_startTime)) {
                _startTime = _endTime.subtract(const Duration(hours: 1));
              }
            }
          });
        },
      ),
    );
  }

  IconData _getEventTypeIcon(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.leave:
        return Icons.time_to_leave;
      case CalendarEventType.holiday:
        return Icons.celebration;
      case CalendarEventType.training:
        return Icons.school;
      case CalendarEventType.event:
        return Icons.event;
      case CalendarEventType.other:
        return Icons.more_horiz;
      case CalendarEventType.unknown:
        return Icons.help_outline;
    }
  }

  String _getEventTypeDisplayName(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting:
        return 'Meeting';
      case CalendarEventType.leave:
        return 'Leave';
      case CalendarEventType.holiday:
        return 'Holiday';
      case CalendarEventType.training:
        return 'Training';
      case CalendarEventType.event:
        return 'Event';
      case CalendarEventType.other:
        return 'Other';
      case CalendarEventType.unknown:
        return 'Unknown';
    }
  }
}

// Separate widget for attendees selector to avoid rebuilding parent dialog
class AttendeesSelector extends StatefulWidget {
  final List<UserForDropdownEntity> availableUsers;
  final List<String> selectedAttendees;
  final Function(List<String>) onSelectionChanged;

  const AttendeesSelector({
    super.key,
    required this.availableUsers,
    required this.selectedAttendees,
    required this.onSelectionChanged,
  });

  @override
  State<AttendeesSelector> createState() => _AttendeesSelectorState();
}

class _AttendeesSelectorState extends State<AttendeesSelector> {
  late List<String> _localSelectedAttendees;

  @override
  void initState() {
    super.initState();
    _localSelectedAttendees = List.from(widget.selectedAttendees);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return AlertDialog(
      title: Text(l10n.selectAttendees),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: widget.availableUsers.isEmpty
            ? const Center(child: CircularProgressIndicator())
            : ListView.builder(
                itemCount: widget.availableUsers.length,
                itemBuilder: (context, index) {
                  final user = widget.availableUsers[index];
                  final isSelected = _localSelectedAttendees.contains(user.id);
                  return CheckboxListTile(
                    title: Text(user.fullname),
                    subtitle: Text(user.email),
                    value: isSelected,
                    onChanged: (bool? value) {
                      setState(() {
                        if (value == true) {
                          if (!_localSelectedAttendees.contains(user.id)) {
                            _localSelectedAttendees.add(user.id);
                          }
                        } else {
                          _localSelectedAttendees.remove(user.id);
                        }
                      });
                    },
                  );
                },
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              l10n.cancel,
              style: AppTextStyle.medium(context, color: AppColors.textPrimary),
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            widget.onSelectionChanged(_localSelectedAttendees);
            Navigator.of(context).pop();
          },
          child: Container(
            decoration: BoxDecoration(
              color: AppColors.primaryBlue,
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              context.l10n.done,
              style: AppTextStyle.medium(context, color: AppColors.white),
            ),
          ),
        ),
      ],
    );
  }
}
