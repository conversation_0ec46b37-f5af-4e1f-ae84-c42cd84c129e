import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/features/calendar/domain/usecases/get_calendar_events.dart';


import '../../domain/entities/calendar_event.dart';
import '../../domain/params/create_event_params.dart';
import '../../domain/params/update_event_params.dart';
import '../../domain/usecases/create_calendar_event.dart';
import '../../domain/usecases/delete_calendar_event.dart';
import '../../domain/usecases/update_calendar_event.dart';
import 'calendar_state.dart';

class CalendarCubit extends Cubit<CalendarState> {
  final GetCalendarEvents getCalendarEvents;
  final CreateCalendarEvent createCalendarEvent;
  final UpdateCalendarEvent updateCalendarEvent;
  final DeleteCalendarEvent deleteCalendarEvent;
  // final GetCalendarEventById getCalendarEventById;

  DateTime _currentMonth = DateTime.now();

  CalendarCubit({
    required this.getCalendarEvents,
    required this.createCalendarEvent,
    required this.updateCalendarEvent,
    required this.deleteCalendarEvent,
    // required this.getCalendarEventById,
  }) : super(CalendarState()) {
    loadEvents();
  }

  // Getters
  DateTime get currentMonth => _currentMonth;
  DateTime? get selectedDate => state.selectedDate;

  // Load events for current month
  Future<void> loadEvents() async {
    emit(state.copyWith(isLoading: true, error: null));

    final startOfMonth = DateTime(_currentMonth.year, _currentMonth.month, 1);
    final endOfMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 0);

    final result = await getCalendarEvents(GetCalendarEventsParams(
      startDate: startOfMonth,
      endDate: endOfMonth,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isLoading: false,
        error: failure.message,
      )),
      (events) => emit(state.copyWith(
        isLoading: false,
        events: events,
        error: null,
      )),
    );
  }

  // Change month
  void changeMonth(DateTime newMonth) {
    _currentMonth = newMonth;
    loadEvents();
  }

  // Select date
  void selectDate(DateTime? date) {
    emit(state.copyWith(selectedDate: date, selectedEvent: null));
  }

  // Get events for specific date
  List<CalendarEvent> getEventsForDate(DateTime date) {
    return state.events.where((event) {
      final eventDate = DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      );
      final targetDate = DateTime(date.year, date.month, date.day);
      return eventDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  // Clear success message
  void clearMessages() {
    emit(state.copyWith(successMessage: null, error: null));
  }


   Future<void> createEvent({
    required String title,
    required String description,
    required DateTime startDate,
    required DateTime endDate,
    String? location,
    CalendarEventType type = CalendarEventType.meeting,
    String? color,
    List<String> attendees = const [],
    bool isAllDay = false,
    bool isRecurring = false,
    String? recurrenceRule,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await createCalendarEvent(CreateEventParams(
      title: title,
      description: description,
      startTime: startDate,
      endTime: endDate,
      isAllDay: isAllDay,
      type: type,
      location: location,
      attendees: attendees,
      isRecurring: isRecurring,
      recurrenceRule: recurrenceRule,
      color: color,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isProcessing: false,
        error: failure.message,
      )),
      (event) {
        // Add new event to current list
        final updatedEvents = List<CalendarEvent>.from(state.events)
          ..add(event)
          ..sort((a, b) => a.startTime.compareTo(b.startTime));

        emit(state.copyWith(
          isProcessing: false,
          events: updatedEvents,
          successMessage: 'Event created successfully',
          error: null,
        ));
      },
    );
  }



  Future<void> updateEvent({
    required String eventId,
    String? title,
    String? description,
    DateTime? startDate,
    DateTime? endDate,
    String? location,
    CalendarEventType? type,
    String? color,
    List<String>? attendees,
    bool? isAllDay,
    bool? isRecurring,
    String? recurrenceRule,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await updateCalendarEvent(UpdateEventParams(
      eventId: eventId,
      title: title,
      description: description,
      startTime: startDate,
      endTime: endDate,
      isAllDay: isAllDay,
      type: type,
      location: location,
      attendees: attendees,
      isRecurring: isRecurring,
      recurrenceRule: recurrenceRule,
      color: color,
    ));

    result.fold(
      (failure) => emit(state.copyWith(
        isProcessing: false,
        error: failure.message,
      )),
      (updatedEvent) {
        // Update event in current list
        final updatedEvents = state.events.map((event) {
          return event.id == eventId ? updatedEvent : event;
        }).toList()
          ..sort((a, b) => a.startTime.compareTo(b.startTime));

        emit(state.copyWith(
          isProcessing: false,
          events: updatedEvents,
          successMessage: 'Event updated successfully',
          error: null,
        ));
      },
    );
  }

  // Delete event method
  Future<void> deleteEvent(String eventId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await deleteCalendarEvent(eventId);

    result.fold(
      (failure) {
        emit(state.copyWith(
          isProcessing: false,
          error: failure.message,
        ));
      },
      (success) {
        // Remove from local list after successful deletion
        final updatedEvents = state.events.where((event) => event.id != eventId).toList();

        emit(state.copyWith(
          isProcessing: false,
          events: updatedEvents,
          successMessage: 'Event deleted successfully',
          error: null,
        ));
      },
    );
  }

  // Select event for viewing details
  void selectEvent(CalendarEvent? event) {
    emit(state.copyWith(selectedEvent: event));
  }

  // Navigate to previous month
  void previousMonth() {
    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1);
    loadEvents();
  }

  // Navigate to next month
  void nextMonth() {
    _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1);
    loadEvents();
  }

  // Check if date has events
  bool hasEventsOnDate(DateTime date) {
    return getEventsForDate(date).isNotEmpty;
  }

  // Get events for a week
  List<CalendarEvent> getEventsForWeek(DateTime startOfWeek) {
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return state.events.where((event) {
      final eventDate = DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      );
      return eventDate.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
             eventDate.isBefore(endOfWeek.add(const Duration(days: 1)));
    }).toList();
  }

  // Search events
  void searchEvents(String query) {
    if (query.trim().isEmpty) {
      emit(state.copyWith(searchResults: []));
      return;
    }

    final results = state.events.where((event) {
      return event.title.toLowerCase().contains(query.toLowerCase()) ||
             (event.description?.toLowerCase().contains(query.toLowerCase()) ?? false) ||
             (event.location?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();

    emit(state.copyWith(searchResults: results));
  }
}

