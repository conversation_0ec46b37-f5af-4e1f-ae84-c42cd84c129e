import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/extensions/text_extension.dart';
import 'package:golderhr/features/department/validators/create_dialog_validator.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../../domain/entities/department_entity.dart';
import '../cubit/department_cubit.dart';
import '../cubit/department_state.dart';

class CreateDepartmentDialog extends StatefulWidget {
  const CreateDepartmentDialog({super.key});

  @override
  State<CreateDepartmentDialog> createState() => _CreateDepartmentDialogState();
}

class _CreateDepartmentDialogState extends State<CreateDepartmentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _codeController = TextEditingController();

  String? _selectedParentId;
  List<DepartmentEntity> _parentDepartments = [];

  @override
  void initState() {
    super.initState();
    _loadParentDepartments();
  }

  void _loadParentDepartments() {
    context.read<DepartmentCubit>().loadDepartmentsForDropdown();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius),
      ),
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: responsive.widthPercentage(90),
          tablet: responsive.widthPercentage(70),
          mobileLandscape: responsive.widthPercentage(80),
          tabletLandscape: responsive.widthPercentage(60),
        ),
        constraints: BoxConstraints(
          maxWidth: responsive.adaptiveValue<double>(
            mobile: 500,
            tablet: 600,
            mobileLandscape: 550,
            tabletLandscape: 650,
          ),
          maxHeight: responsive.heightPercentage(85),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: theme.primaryColor,
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: responsive.adaptiveValue<double>(
                      mobile: 18,
                      tablet: 20,
                      mobileLandscape: 19,
                      tabletLandscape: 22,
                    ),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    child: Icon(
                      Iconsax.building,
                      color: Colors.white,
                      size: responsive.adaptiveValue<double>(
                        mobile: 18,
                        tablet: 20,
                        mobileLandscape: 19,
                        tabletLandscape: 22,
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.l10n.createDepartmentDialogTitle,
                          style: AppTextStyle.bold(
                            context,
                            size: 18,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          context.l10n.createDepartmentDialogSubtitle,
                          style: AppTextStyle.regular(
                            context,
                            size: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Form Content
            Expanded(
              child: BlocConsumer<DepartmentCubit, DepartmentState>(
                listener: (context, state) {
                  if (!state.isProcessing &&
                      !state.hasError &&
                      state.hasSuccess) {
                    showTopSnackBar(context, title: context.l10n.success, message: state.successMessage!);
                    Navigator.pop(context);
                  }

                  // Update parent departments list
                  if (state.dropdownDepartments.isNotEmpty) {
                    setState(() {
                      _parentDepartments = state.dropdownDepartments;
                    });
                  }
                },
                builder: (context, state) {
                  if (state.isProcessing) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  return SingleChildScrollView(
                    padding: responsive.responsivePadding,
                    child: Form(
                      key: _formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Basic Information Section
                          _buildSectionHeader(
                            context.l10n.basicInformation,
                            Iconsax.building,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _nameController,
                            label: context.l10n.departmentName,
                            icon: Iconsax.building,
                            validator: (value) =>
                                CreateDialogValidator.validateDepartmentName(
                                  value,
                                  context,
                                ),
                            hint: context.l10n.departmentHintName,
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _descriptionController,
                            label: context.l10n.departmentDescription,
                            icon: Iconsax.document_text,
                            hint: context.l10n.departmentHintDescription,
                            maxLines: 2,
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          _buildTextField(
                            controller: _codeController,
                            label: context.l10n.departmentCode,
                            icon: Iconsax.code,
                            validator: (value) =>
                                CreateDialogValidator.validateDepartmentCode(
                                  value,
                                  context,
                                ),
                            hint: context.l10n.departmentHintCode,
                          ),

                          SizedBox(height: responsive.defaultSpacing * 1.5),

                          // Hierarchy Section
                          _buildSectionHeader(
                            context.l10n.hierarchy,
                            Iconsax.hierarchy_square_2,
                          ),
                          SizedBox(height: responsive.defaultSpacing),

                          // Parent Department Dropdown
                          DropdownButtonFormField<String>(
                            value: _selectedParentId,
                            decoration: InputDecoration(
                              labelText:context.l10n.parentDepartment,
                              labelStyle: context.lightTheme.textTheme.labelLarge,
                              prefixIcon: Icon(
                                Iconsax.hierarchy_square_2,
                                size: responsive.adaptiveValue<double>(
                                  mobile: 20,
                                  tablet: 22,
                                  mobileLandscape: 21,
                                  tabletLandscape: 24,
                                ),
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  responsive.defaultRadius,
                                ),
                              ),
                              filled: true,
                              fillColor: Colors.grey.shade50,
                              contentPadding: responsive.padding(
                                horizontal: 16,
                                vertical: 12,
                              ),
                            ),
                            hint:  Text(
                              context.l10n.selectDepartment,
                            ),
                            items: [
                               DropdownMenuItem<String>(
                                value: null,
                                child: context.l10n.noParentTopLevel.responsiveText(context: context),
                              ),
                              ..._parentDepartments.map((dept) {
                                return DropdownMenuItem<String>(
                                  value: dept.id,
                                  child: Text(dept.name),
                                );
                              }),
                            ],
                            onChanged: (value) {
                              setState(() {
                                _selectedParentId = value;
                              });
                            },
                          ),

                          SizedBox(height: responsive.defaultSpacing),

                          // Info card
                          Container(
                            padding: responsive.padding(all: 12),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              borderRadius: BorderRadius.circular(
                                responsive.defaultRadius,
                              ),
                              border: Border.all(color: Colors.blue.shade200),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.info_circle,
                                  color: Colors.blue.shade600,
                                  size: responsive.scaleRadius(16),
                                ),
                                SizedBox(width: responsive.scaleWidth(8)),
                                Expanded(
                                  child: Text(
                                    context.l10n.departmentInfoCardUniquenessAndHierarchy,
                                    style: context.lightTheme.textTheme.bodyLarge!.copyWith(
                                      color: AppColors.primaryBlue.withValues(alpha: 0.9)
                                    )
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),

            // Action Buttons
            Container(
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(responsive.defaultRadius),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: Text(
                        l10n.cancel,
                        style: context.lightTheme.textTheme.bodyLarge
                      ),
                    ),
                  ),
                  SizedBox(width: responsive.scaleWidth(16)),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _createDepartment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryBlue,
                        foregroundColor: AppColors.white,
                        padding: responsive.padding(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            responsive.defaultRadius,
                          ),
                        ),
                      ),
                      child: context.l10n.createDepartment.responsiveText(
                        context: context,
                        style: context.lightTheme.textTheme.bodyLarge!.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final responsive = context.responsive;

    return Row(
      children: [
        Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 18,
            tablet: 20,
            mobileLandscape: 19,
            tabletLandscape: 22,
          ),
          color: Colors.grey[700],
        ),
        SizedBox(width: responsive.scaleWidth(8)),
        Text(
          title,
          style: AppTextStyle.bold(context, size: 16, color: Colors.black87),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    String? hint,
    int maxLines = 1,
  }) {
    final responsive = context.responsive;

    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines,
      style: AppTextStyle.regular(context, size: 14),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: AppTextStyle.regular(context, size: 12),
        hintStyle: AppTextStyle.regular(
          context,
          size: 12,
          color: Colors.grey[500],
        ),
        prefixIcon: Icon(
          icon,
          size: responsive.adaptiveValue<double>(
            mobile: 20,
            tablet: 22,
            mobileLandscape: 21,
            tabletLandscape: 24,
          ),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(responsive.defaultRadius),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: responsive.padding(horizontal: 16, vertical: 12),
      ),
    );
  }

  void _createDepartment() {
    if (_formKey.currentState!.validate()) {
      context.read<DepartmentCubit>().createNewDepartment(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        code: _codeController.text.trim().isEmpty
            ? null
            : _codeController.text.trim(),
        parentId: _selectedParentId,
      );
    }
  }
}
