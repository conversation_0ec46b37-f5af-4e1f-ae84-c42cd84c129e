part of 'task_cubit.dart';

abstract class TaskState extends Equatable {
  const TaskState();

  @override
  List<Object?> get props => [];
}

class TaskInitial extends TaskState {}

class TaskLoading extends TaskState {}

class TasksLoaded extends TaskState {
  final List<TaskEntity> tasks;

  const TasksLoaded(this.tasks);

  @override
  List<Object> get props => [tasks];
}

class TaskCreated extends TaskState {
  final TaskEntity task;

  const TaskCreated(this.task);

  @override
  List<Object> get props => [task];
}

class TaskUpdated extends TaskState {
  final TaskEntity task;

  const TaskUpdated(this.task);

  @override
  List<Object> get props => [task];
}

class TaskStatsLoaded extends TaskState {
  final Map<String, dynamic> stats;

  const TaskStatsLoaded(this.stats);

  @override
  List<Object> get props => [stats];
}

class TaskError extends TaskState {
  final String message;

  const TaskError(this.message);

  @override
  List<Object> get props => [message];
}
