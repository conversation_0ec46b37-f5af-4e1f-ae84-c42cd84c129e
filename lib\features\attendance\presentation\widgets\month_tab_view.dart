import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../domain/entities/attendance_history.dart';
import '../../domain/entities/monthly_summary.dart';
import '../../domain/entities/today_summary.dart';
import 'info_card_widget.dart';
import 'monthly_summary_content_widget.dart';
import 'full_history_list_widget.dart';

class MonthTabView extends StatelessWidget {
  final MonthlySummary monthlyData;
  final TodaySummary todaySummary;
  final List<AttendanceHistoryItem> recentHistory;

  const MonthTabView({
    super.key,
    required this.monthlyData,
    required this.todaySummary,
    required this.recentHistory,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.widthPercentage(4.0),
        vertical: responsive.heightPercentage(3.0),
      ),
      child: Column(
        children: [
          InfoCardWidget(
            title: l10n.monthlySummary,
            icon: Icons.calendar_month_outlined,
            child: MonthlySummaryContentWidget(monthlyData: monthlyData),
          ),
          SizedBox(height: responsive.heightPercentage(3.0)),
          InfoCardWidget(
            title: l10n.fullMonthHistory,
            icon: Icons.history_edu_outlined,
            child: FullHistoryListWidget(
              todaySummary: todaySummary,
              recentHistory: recentHistory,
            ),
          ),
        ],
      ),
    );
  }
}
