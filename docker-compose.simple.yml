version: '3.8'

services:
 
  # Node.js Backend Application
  backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: golder_hr_backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    env_file:
      - .env.docker
    volumes:
      - uploads_data:/app/uploads
      - ./logs:/app/logs
    networks:
      - golder_hr_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
volumes:
  uploads_data:
    driver: local

networks:
  golder_hr_network:
    driver: bridge
