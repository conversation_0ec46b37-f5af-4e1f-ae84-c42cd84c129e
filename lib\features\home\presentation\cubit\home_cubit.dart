import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../l10n/app_localizations.dart';
import '../../../notification/data/model/notification_model.dart';
import '../../../notification/domain/usecases/get_notifications.dart';
import 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  final GetNotificationsUseCase _getNotificationsUseCase;

  HomeCubit({required GetNotificationsUseCase getNotificationsUseCase})
      : _getNotificationsUseCase = getNotificationsUseCase,
        super(HomeInitial());

  void _processAndEmitState(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      emit(HomeNotificationEmpty());
      return;
    }
    emit(HomeLoaded(notifications));
  }

  Future<void> fetchNotificationsHome({
    int page = 1,
    int limit = 3,
    String? type,
    bool? isRead,
    String? priority,
    required AppLocalizations l10n,
  }) async {
    try {
      emit(HomeLoading());
      final result = await _getNotificationsUseCase(
        page: page,
        limit: limit,
        type: type,
        isRead: isRead,
        priority: priority,
      );

      result.fold(
            (failure) => emit(HomeError(failure.message)),
            (notifications) =>
            _processAndEmitState(notifications.cast<NotificationModel>()),
      );
    } catch (e) {
      emit(HomeError(l10n.notificationLoadError));
    }
  }
}