import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/manual_attendance_entity.dart';

abstract class ManualAttendanceRepository {
  Future<Either<Failure, List<ManualAttendanceEntity>>> getManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  });

  Future<Either<Failure, ManualAttendanceEntity>> reviewManualAttendance({
    required String id,
    required ManualAttendanceStatus status,
    String? adminNote,
  });

  Future<Either<Failure, List<ManualAttendanceEntity>>> getMyManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
  });
}
