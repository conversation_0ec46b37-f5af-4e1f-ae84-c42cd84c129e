import 'package:equatable/equatable.dart';

enum TaskStatus { todo, in_progress, review, done, cancelled }
enum TaskPriority { low, medium, high, urgent }

class TeamTaskEntity extends Equatable {
  final String id;
  final String teamId;
  final String title;
  final String? description;
  final dynamic assignedTo; // Can be String (ID) or TeamMemberEntity
  final dynamic createdBy; // Can be String (ID) or TeamMemberEntity
  final TaskStatus status;
  final TaskPriority priority;
  final DateTime? dueDate;
  final List<TaskAttachmentEntity>? attachments;
  final List<TaskCommentEntity>? comments;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TeamTaskEntity({
    required this.id,
    required this.teamId,
    required this.title,
    this.description,
    required this.assignedTo,
    required this.createdBy,
    required this.status,
    required this.priority,
    this.dueDate,
    this.attachments,
    this.comments,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        teamId,
        title,
        description,
        assignedTo,
        createdBy,
        status,
        priority,
        dueDate,
        attachments,
        comments,
        createdAt,
        updatedAt,
      ];

  bool get isOverdue {
    if (dueDate == null || status == TaskStatus.done) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isDueSoon {
    if (dueDate == null || status == TaskStatus.done) return false;
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inDays;
    return difference <= 1 && difference >= 0;
  }

  String get statusDisplayName {
    switch (status) {
      case TaskStatus.todo:
        return 'To Do';
      case TaskStatus.in_progress:
        return 'In Progress';
      case TaskStatus.review:
        return 'Review';
      case TaskStatus.done:
        return 'Done';
      case TaskStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.high:
        return 'High';
      case TaskPriority.urgent:
        return 'Urgent';
    }
  }
}

class TaskAttachmentEntity extends Equatable {
  final String fileName;
  final String fileUrl;
  final String fileType;
  final DateTime uploadedAt;

  const TaskAttachmentEntity({
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.uploadedAt,
  });

  @override
  List<Object?> get props => [fileName, fileUrl, fileType, uploadedAt];
}

class TaskCommentEntity extends Equatable {
  final String userId;
  final String message;
  final DateTime createdAt;

  const TaskCommentEntity({
    required this.userId,
    required this.message,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [userId, message, createdAt];
}

class TeamMemberEntity extends Equatable {
  final String id;
  final String fullname;
  final String? avatar;
  final String? email;
  final String? department;
  final String? position;
  final String? role;
  final DateTime? joinedAt;
  final bool isActive;

  const TeamMemberEntity({
    required this.id,
    required this.fullname,
    this.avatar,
    this.email,
    this.department,
    this.position,
    this.role,
    this.joinedAt,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        id,
        fullname,
        avatar,
        email,
        department,
        position,
        role,
        joinedAt,
        isActive,
      ];
}
