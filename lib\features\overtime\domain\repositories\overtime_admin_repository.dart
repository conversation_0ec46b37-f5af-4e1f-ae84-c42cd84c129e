import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/overtime_request_entity.dart';

abstract class OvertimeAdminRepository {
  Future<Either<Failure, List<OvertimeRequestEntity>>> getAllOvertimeRequests({
    int page = 1,
    int limit = 10,
    String? status,
  });

  Future<Either<Failure, OvertimeRequestEntity>> approveOvertimeRequest(
    String requestId,
  );

  Future<Either<Failure, OvertimeRequestEntity>> rejectOvertimeRequest(
    String requestId,
    String rejectionReason,
  );
}
