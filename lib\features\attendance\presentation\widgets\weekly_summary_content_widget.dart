import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../domain/entities/weekly_summary.dart';
import 'stat_row_widget.dart';

class WeeklySummaryContentWidget extends StatelessWidget {
  final WeeklySummary weeklyData;

  const WeeklySummaryContentWidget({super.key, required this.weeklyData});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    const accentColor = Color(0xFF6772FF);

    return Column(
      children: [
        StatRowWidget(
          title: l10n.workDays,
          value: weeklyData.workDays,
          icon: Icons.work_outline,
          color: accentColor,
        ),
        StatRowWidget(
          title: l10n.totalHours,
          value: weeklyData.totalHours,
          icon: Icons.timer_outlined,
          color: Colors.green,
        ),
        StatRowWidget(
          title: l10n.overtime,
          value: weeklyData.overtime,
          icon: Icons.add_alarm_outlined,
          color: Colors.orange,
        ),
        StatRowWidget(
          title: l10n.lateArrivals,
          value: weeklyData.lateArrivals.toString(),
          icon: Icons.running_with_errors_outlined,
          color: Colors.redAccent,
        ),
      ],
    );
  }
}
