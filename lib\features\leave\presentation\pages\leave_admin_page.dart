import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/text_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';

import '../../../../injection_container.dart' as di;
import '../../../attendance/presentation/widgets/loading_widget.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../../../shared/widgets/empty_state_widget.dart';
import '../cubit/leave_admin_cubit.dart';
import '../widgets/leave_admin_filter_widget.dart';
import '../widgets/leave_admin_list_widget.dart';

class LeaveAdminPage extends StatelessWidget {
  const LeaveAdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<LeaveAdminCubit>()..loadAllRequests(),
      child: const LeaveAdminView(),
    );
  }
}

class LeaveAdminView extends StatefulWidget {
  const LeaveAdminView({super.key});

  @override
  State<LeaveAdminView> createState() => _LeaveAdminViewState();
}

class _LeaveAdminViewState extends State<LeaveAdminView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Logic infinite scroll không đổi
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent * 0.8) {
      context.read<LeaveAdminCubit>().loadMore();
    }
  }

  Future<void> _handleRefresh() async {
    context.read<LeaveAdminCubit>().refresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor:AppColors.background,
      appBar: AppBar(
        title: context.l10n.leaveManagement.responsiveText(context: context,
        style: context.lightTheme.textTheme.headlineSmall!.copyWith(
            fontWeight: FontWeight.bold
        ) ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0.5,
        shadowColor: Colors.grey[100],
        surfaceTintColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh_rounded),
            onPressed: _handleRefresh,
            tooltip: 'Refresh',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: BlocConsumer<LeaveAdminCubit, LeaveAdminState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            showTopSnackBar(
              context,
              title: 'Error',
              message: state.errorMessage!,
              isError: true,
            );
            context.read<LeaveAdminCubit>().clearMessages();
          }

          if (state.successMessage != null) {
            showTopSnackBar(
              context,
              title: 'Success',
              message: state.successMessage!,
              isError: false,
            );
            context.read<LeaveAdminCubit>().clearMessages();
          }
        },
        builder: (context, state) {
          if (state.isLoading && state.requests.isEmpty) {
            return const LoadingWidget();
          }

          return RefreshIndicator(
            onRefresh: _handleRefresh,
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(bottom: BorderSide(color: Colors.grey[200]!)),
                  ),
                  child: const LeaveAdminFilterWidget(),
                ),


                Expanded(
                  child: state.requests.isEmpty
                  // Sử dụng widget EmptyStateWidget mới, có thể tái sử dụng
                      ? EmptyStateWidget(
                    icon: Icons.assignment_turned_in_outlined,
                    title: context.l10n.noLeaveRequestsFound,
                    message: context.l10n.tryLeaveRequest,
                    onRefresh: _handleRefresh,
                  )
                      : LeaveAdminListWidget(
                    requests: state.requests,
                    scrollController: _scrollController,
                    isLoadingMore: state.isLoadingMore,
                    isProcessing: state.isProcessing,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}