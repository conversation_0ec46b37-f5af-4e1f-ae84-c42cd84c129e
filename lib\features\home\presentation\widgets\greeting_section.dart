import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/features/cubit/greeting_cubit.dart';
import 'package:golderhr/features/home/<USER>/widgets/welcome_home.dart';

import '../../../cubit/user_cubit.dart';

class GreetingSection extends StatelessWidget {

  const GreetingSection({super.key});

  @override
  Widget build(BuildContext context) {
    // final UserEntity? user = context.watch<UserCubit>().state;
    final userCubitInstance = context.watch<UserCubit>();
    final TimeOfDayGreeting timeOfDayState = context
        .watch<GreetingCubit>()
        .state;
    final user = userCubitInstance.state;

    final userName = user?.fullname ?? 'Guest';
    final userRole = user?.role ?? 'Guest';

    final l10n = context.l10n;
    String greetingText;
    switch (timeOfDayState) {
      case TimeOfDayGreeting.morning:
        greetingText = l10n.homeGoodMorning;
        break;
      case TimeOfDayGreeting.afternoon:
        greetingText = l10n.homeGoodAfternoon;
        break;
      case TimeOfDayGreeting.evening:
        greetingText = l10n.homeGoodEvening;
        break;
    }

    return WelcomeHome(
      title: greetingText,
      subtitle: userName,
      subtitle2: userRole,
      icon: Icons.wb_sunny_outlined,
    );
  }
}
