import 'package:flutter_bloc/flutter_bloc.dart';

//
enum TimeOfDayGreeting {
  morning, // Buổi sáng
  afternoon, // Buổi chiều
  evening, // Buổi tối
}

class GreetingCubit extends Cubit<TimeOfDayGreeting> {
  GreetingCubit() : super(_getInitialGreeting());

  static TimeOfDayGreeting _getInitialGreeting() {
    final int hour = DateTime.now().hour;

    if (hour >= 6 && hour < 12) {
      return TimeOfDayGreeting.morning;
    } else if (hour >= 12 && hour < 18) {
      return TimeOfDayGreeting.afternoon;
    } else {
      return TimeOfDayGreeting.evening;
    }
  }
}
