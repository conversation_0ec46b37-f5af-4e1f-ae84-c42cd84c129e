import 'dart:io';

import 'package:dio/dio.dart';
import 'package:golderhr/core/network/dio_client.dart'; // Giả sử đây là file của bạn
import 'package:path/path.dart' as p; // <--- THÊM DÒNG NÀY VÀO

import '../../../../core/error/exceptions.dart';
import '../models/user_for_dropdown_model.dart';

abstract class UploadFaceRemoteDataSource {
  Future<List<UserForDropdownModel>> getUsersForDropdown();

  Future<String> uploadEmployeeFace({
    required String userId,
    required File imageFile,
  });
}

class UploadFaceRemoteDataSourceImpl implements UploadFaceRemoteDataSource {
  final DioClient dioClient;

  UploadFaceRemoteDataSourceImpl({required this.dioClient});

  @override
  Future<List<UserForDropdownModel>> getUsersForDropdown() async {
    try {
      final response = await dioClient.get('/api/attendance/users-dropdown');
      final List<dynamic> data = response.data['data'];
      return data.map((json) => UserForDropdownModel.fromJson(json)).toList();
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'An unknown server error occurred',
      );
    } catch (e) {
      throw ServerException("An unexpected error occurred: $e");
    }
  }

  @override
  Future<String> uploadEmployeeFace({
    required String userId,
    required File imageFile,
  }) async {
    try {
      // Lấy tên file từ đường dẫn một cách an toàn
      final fileName = p.basename(imageFile.path);
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imageFile.path,
          filename: fileName,
        ),
      });

      final response = await dioClient.post(
        '/api/attendance/upload-face/$userId',
        data: formData,
      );

      return response.data['data']['imageUrl'] as String;
    } on DioException catch (e) {
      throw ServerException(
        e.response?.data['message'] ?? 'An unknown server error occurred',
      );
    } catch (e) {
      throw ServerException("An unexpected error occurred: $e");
    }
  }
}
