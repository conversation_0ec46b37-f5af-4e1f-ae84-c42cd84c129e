import 'package:equatable/equatable.dart';
import 'team_task_entity.dart';

enum ChatMessageType { text, file, image, system, task_mention }

class TeamChatEntity extends Equatable {
  final String id;
  final String teamId;
  final dynamic senderId; // Can be String (ID) or TeamMemberEntity
  final String message;
  final ChatMessageType type;
  final List<ChatAttachmentEntity>? attachments;
  final List<String>? mentions;
  final String? replyTo;
  final bool isPinned;
  final bool isEdited;
  final DateTime? editedAt;
  final List<ChatReactionEntity>? reactions;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TeamChatEntity({
    required this.id,
    required this.teamId,
    required this.senderId,
    required this.message,
    required this.type,
    this.attachments,
    this.mentions,
    this.replyTo,
    this.isPinned = false,
    this.isEdited = false,
    this.editedAt,
    this.reactions,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        teamId,
        senderId,
        message,
        type,
        attachments,
        mentions,
        replyTo,
        isPinned,
        isEdited,
        editedAt,
        reactions,
        createdAt,
        updatedAt,
      ];

  bool get hasAttachments => attachments != null && attachments!.isNotEmpty;
  bool get hasMentions => mentions != null && mentions!.isNotEmpty;
  bool get hasReactions => reactions != null && reactions!.isNotEmpty;
  bool get isSystemMessage => type == ChatMessageType.system;
  bool get isTaskMention => type == ChatMessageType.task_mention;

  String get senderName {
    if (senderId is TeamMemberEntity) {
      return (senderId as TeamMemberEntity).fullname;
    }
    return 'Unknown User';
  }

  String? get senderAvatar {
    if (senderId is TeamMemberEntity) {
      return (senderId as TeamMemberEntity).avatar;
    }
    return null;
  }

  String get senderId_string {
    if (senderId is TeamMemberEntity) {
      return (senderId as TeamMemberEntity).id;
    }
    return senderId.toString();
  }
}

class ChatAttachmentEntity extends Equatable {
  final String fileName;
  final String fileUrl;
  final String fileType;
  final int fileSize;

  const ChatAttachmentEntity({
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.fileSize,
  });

  @override
  List<Object?> get props => [fileName, fileUrl, fileType, fileSize];

  bool get isImage => fileType.startsWith('image/');
  bool get isDocument => !isImage;
  
  String get fileSizeFormatted {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
  }
}

class ChatReactionEntity extends Equatable {
  final String userId;
  final String emoji;
  final DateTime createdAt;

  const ChatReactionEntity({
    required this.userId,
    required this.emoji,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [userId, emoji, createdAt];
}
