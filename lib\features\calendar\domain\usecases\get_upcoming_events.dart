import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';
import '../repositories/calendar_repository.dart';

class GetUpcomingEvents implements UseCase<List<CalendarEvent>, GetUpcomingEventsParams> {
  final CalendarRepository repository;

  GetUpcomingEvents(this.repository);

  @override
  Future<Either<Failure, List<CalendarEvent>>> call(GetUpcomingEventsParams params) async {
    return await repository.getUpcomingEvents(limit: params.limit);
  }
}

class GetUpcomingEventsParams extends Equatable {
  final int? limit;

  const GetUpcomingEventsParams({this.limit});

  @override
  List<Object?> get props => [limit];
}
