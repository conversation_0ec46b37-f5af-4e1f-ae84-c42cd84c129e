// Tên tệp: IDepartmentSalary.ts

import { Schema, Document } from "mongoose";

// Interface cho Department Salary
export interface IDepartmentSalary extends Document {
  employeeId: Schema.Types.ObjectId;
  departmentId: Schema.Types.ObjectId;
  hourlyRate: number; // <PERSON><PERSON><PERSON> lương theo giờ
  isDefault: boolean; // Bộ phận mặc định của nhân viên
  isActive: boolean; // C<PERSON> đang hoạt động không
  effectiveFrom: Date; // <PERSON><PERSON> hiệu lực từ ngày
  effectiveTo?: Date; // <PERSON><PERSON> hiệu lực đến ngày (optional)
  createdAt?: Date;
  updatedAt?: Date;
}
