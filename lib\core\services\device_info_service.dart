import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class DeviceInfoService {
  static final DeviceInfoService _instance = DeviceInfoService._internal();
  factory DeviceInfoService() => _instance;
  DeviceInfoService._internal();

  Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      Map<String, dynamic> info = {
        'appVersion': packageInfo.version,
        'buildNumber': packageInfo.buildNumber,
        'packageName': packageInfo.packageName,
      };

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info.addAll({
          'deviceId': androidInfo.id, // Unique device ID
          'platform': 'Android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'manufacturer': androidInfo.manufacturer,
          'product': androidInfo.product,
          'hardware': androidInfo.hardware,
          'fingerprint': androidInfo.fingerprint,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info.addAll({
          'deviceId': iosInfo.identifierForVendor ?? 'unknown', // Unique device ID
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'localizedModel': iosInfo.localizedModel,
          'utsname': {
            'machine': iosInfo.utsname.machine,
            'nodename': iosInfo.utsname.nodename,
            'release': iosInfo.utsname.release,
            'sysname': iosInfo.utsname.sysname,
            'version': iosInfo.utsname.version,
          },
        });
      } else {
        // Fallback for other platforms
        info.addAll({
          'deviceId': 'unknown',
          'platform': Platform.operatingSystem,
          'model': 'unknown',
          'brand': 'unknown',
          'version': Platform.operatingSystemVersion,
        });
      }

      return info;
    } catch (e) {
      // Return minimal info if device info fails
      return {
        'deviceId': 'error_getting_device_id',
        'platform': Platform.operatingSystem,
        'model': 'unknown',
        'brand': 'unknown',
        'version': 'unknown',
        'error': e.toString(),
      };
    }
  }

  /// Get a simplified device info for attendance tracking
  Future<Map<String, dynamic>> getAttendanceDeviceInfo() async {
    final fullInfo = await getDeviceInfo();
    
    return {
      'deviceId': fullInfo['deviceId'],
      'platform': fullInfo['platform'],
      'model': fullInfo['model'],
      'brand': fullInfo['brand'],
      'version': fullInfo['version'],
      'appVersion': fullInfo['appVersion'],
    };
  }

  /// Get device ID only
  Future<String> getDeviceId() async {
    final info = await getDeviceInfo();
    return info['deviceId'] ?? 'unknown';
  }
}
