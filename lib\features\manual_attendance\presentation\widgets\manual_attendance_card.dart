import 'package:flutter/material.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:golderhr/shared/theme/app_colors.dart';
import 'package:iconsax/iconsax.dart';
import 'package:geocoding/geocoding.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import 'review_manual_attendance_dialog.dart';

class ManualAttendanceCard extends StatelessWidget {
  final ManualAttendanceEntity request;
  final Function(ManualAttendanceStatus status, String? note) onReview;

  const ManualAttendanceCard({
    super.key,
    required this.request,
    required this.onReview,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status and type
            Row(
              children: [
                _buildTypeIcon(),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        request.fullName,
                        style: TextStyle(
                          fontSize: responsive.fontSize(16),
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                      if (request.userInfo != null) ...[
                        Text(
                          request.userInfo!.email,
                          style: TextStyle(
                            fontSize: responsive.fontSize(12),
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                _buildStatusChip(responsive),
              ],
            ),

            const SizedBox(height: 12),

            // Attendance info
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Iconsax.clock,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Thời gian: ${_formatDateTime(request.timestamp)}',
                        style: TextStyle(
                          fontSize: responsive.fontSize(14),
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Iconsax.message_text,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Lý do: ${request.reason}',
                          style: TextStyle(
                            fontSize: responsive.fontSize(14),
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Device info
            _buildDeviceInfo(responsive),

            if (request.failureImage != null) ...[
              const SizedBox(height: 12),
              _buildImageSection(responsive),
            ],

            if (request.adminNote != null && request.adminNote!.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildAdminNote(responsive),
            ],

            if (request.status == ManualAttendanceStatus.pending) ...[
              const SizedBox(height: 16),
              _buildActionButtons(context),
            ],

            const SizedBox(height: 8),
            _buildTimestamp(responsive),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeIcon() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: request.isCheckIn ? Colors.green.shade100 : Colors.orange.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        request.isCheckIn ? Iconsax.login_1 : Iconsax.logout_1,
        color: request.isCheckIn ? Colors.green.shade700 : Colors.orange.shade700,
        size: 20,
      ),
    );
  }

  Widget _buildStatusChip(Responsive responsive) {
    Color color;
    switch (request.status) {
      case ManualAttendanceStatus.pending:
        color = Colors.orange;
        break;
      case ManualAttendanceStatus.approved:
        color = Colors.green;
        break;
      case ManualAttendanceStatus.rejected:
        color = Colors.red;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        request.status.displayName,
        style: TextStyle(
          fontSize: responsive.fontSize(12),
          color:AppColors.textPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildDeviceInfo(Responsive responsive) {
    final deviceInfo = request.deviceInfo;
    final deviceId = deviceInfo['deviceId'] ?? 'N/A';
    final platform = deviceInfo['platform'] ?? 'N/A';
    final model = deviceInfo['model'] ?? 'N/A';
    final location = deviceInfo['location'];

    return Column(
      children: [
        // Device Info Section
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Iconsax.mobile,
                    size: 16,
                    color: Colors.blue.shade600,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Thông tin thiết bị',
                    style: TextStyle(
                      fontSize: responsive.fontSize(14),
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'ID: $deviceId',
                style: TextStyle(
                  fontSize: responsive.fontSize(12),
                  color: Colors.blue.shade700,
                  fontFamily: 'monospace',
                ),
              ),
              Text(
                'Thiết bị: $platform $model',
                style: TextStyle(
                  fontSize: responsive.fontSize(12),
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
        ),

        // Location Section
        if (location != null) ...[
          const SizedBox(height: 12),
          _buildLocationInfo(location, responsive),
        ],
      ],
    );
  }

  Widget _buildLocationInfo(Map<String, dynamic> location, Responsive responsive) {
    // Debug: Print location structure
    print('Location data: $location');

    // Try different possible address fields
    String? address = location['address'] as String?;
    if (address == null || address.isEmpty) {
      // Try nested address in coordinates
      final coordinates = location['coordinates'] as Map<String, dynamic>?;
      if (coordinates != null) {
        address = coordinates['address'] as String?;
      }
    }

    final accuracy = location['accuracy']?.toDouble() ?? 0.0;
    final latitude = location['latitude']?.toDouble();
    final longitude = location['longitude']?.toDouble();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Iconsax.location,
                size: 16,
                color: Colors.green.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                'Vị trí chấm công',
                style: TextStyle(
                  fontSize: responsive.fontSize(14),
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Show address if available, otherwise use geocoding
          if (address != null && address.isNotEmpty) ...[
            Text(
              address,
              style: TextStyle(
                fontSize: responsive.fontSize(12),
                color: Colors.green.shade700,
                height: 1.3,
              ),
            ),
          ] else if (latitude != null && longitude != null) ...[
            FutureBuilder<String>(
              future: _getAddressFromCoordinates(latitude, longitude),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Text(
                    'Đang lấy địa chỉ...',
                    style: TextStyle(
                      fontSize: responsive.fontSize(12),
                      color: Colors.green.shade600,
                      fontStyle: FontStyle.italic,
                    ),
                  );
                }

                return Text(
                  snapshot.data ?? 'Không xác định được địa chỉ',
                  style: TextStyle(
                    fontSize: responsive.fontSize(12),
                    color: Colors.green.shade700,
                    height: 1.3,
                  ),
                );
              },
            ),
          ] else ...[
            Text(
              'Không có thông tin vị trí',
              style: TextStyle(
                fontSize: responsive.fontSize(12),
                color: Colors.red.shade600,
              ),
            ),
          ],

          const SizedBox(height: 4),
          Text(
            'Độ chính xác: ${accuracy.toStringAsFixed(0)}m',
            style: TextStyle(
              fontSize: responsive.fontSize(11),
              color: Colors.green.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Future<String> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        final address = [
          place.street,
          place.subLocality,
          place.locality,
          place.administrativeArea,
          place.country,
        ].where((part) => part != null && part.isNotEmpty).join(', ');

        return address.isNotEmpty ? address : 'Không xác định được địa chỉ';
      }

      return 'Không xác định được địa chỉ';
    } catch (e) {
      return 'Lỗi khi lấy địa chỉ';
    }
  }



  Widget _buildImageSection(Responsive responsive) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hình ảnh minh chứng:',
          style: TextStyle(
            fontSize: responsive.fontSize(14),
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 8),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            request.failureImage!,
            height: 120,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 120,
                color: Colors.grey.shade200,
                child: const Center(
                  child: Icon(Icons.error, color: Colors.grey),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAdminNote(Responsive responsive) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Iconsax.note,
                size: 16,
                color: Colors.amber.shade600,
              ),
              const SizedBox(width: 8),
              Text(
                'Ghi chú của admin:',
                style: TextStyle(
                  fontSize: responsive.fontSize(14),
                  fontWeight: FontWeight.bold,
                  color: Colors.amber.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            request.adminNote!,
            style: TextStyle(
              fontSize: responsive.fontSize(14),
              color: Colors.amber.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showReviewDialog(context, ManualAttendanceStatus.approved),
            icon: const Icon(Iconsax.tick_circle),
            label: const Text('Duyệt'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _showReviewDialog(context, ManualAttendanceStatus.rejected),
            icon: const Icon(Iconsax.close_circle),
            label: const Text('Từ chối'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimestamp(Responsive responsive) {
    return Text(
      'Gửi lúc: ${_formatDateTime(request.createdAt)}',
      style: TextStyle(
        fontSize: responsive.fontSize(12),
        color: Colors.grey.shade500,
        fontStyle: FontStyle.italic,
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    // Convert UTC to Vietnam timezone (UTC+7)
    final vietnamTime = dateTime.add(const Duration(hours: 7));
    return '${vietnamTime.day}/${vietnamTime.month}/${vietnamTime.year} ${vietnamTime.hour.toString().padLeft(2, '0')}:${vietnamTime.minute.toString().padLeft(2, '0')}';
  }

  void _showReviewDialog(BuildContext context, ManualAttendanceStatus status) {
    showDialog(
      context: context,
      builder: (context) => ReviewManualAttendanceDialog(
        request: request,
        initialStatus: status,
        onReview: onReview,
      ),
    );
  }
}
