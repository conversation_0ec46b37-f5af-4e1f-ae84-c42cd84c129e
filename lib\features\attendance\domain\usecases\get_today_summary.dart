import 'package:dartz/dartz.dart';
import 'package:golderhr/features/attendance/domain/repositories/attendance_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/today_summary.dart';

class GetTodaySummary implements UseCase<TodaySummary, NoParams> {
  final AttendanceRepositoryV1 repository;

  GetTodaySummary(this.repository);

  @override
  Future<Either<Failure, TodaySummary>> call(NoParams params) async {
    return await repository.getTodaySummary();
  }
}
