import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../cubit/overtime_cubit.dart';
import '../cubit/overtime_state.dart';

class ApproverSelectionWidget extends StatelessWidget {
  final ApproverEntity? selectedApprover;
  final Function(ApproverEntity?) onApproverSelected;

  const ApproverSelectionWidget({
    super.key,
    this.selectedApprover,
    required this.onApproverSelected,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);
    final l10n = context.l10n;

    return BlocBuilder<OvertimeCubit, OvertimeState>(
      builder: (context, state) {
        return Container(
          padding: responsive.padding(all: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryBlue.withValues(alpha: 0.08),
                blurRadius: 20,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
            border: Border.all(
              color: AppColors.primaryBlue.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.person_pin_circle_outlined,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                  ),
                  SizedBox(width: responsive.widthPercentage(2)),
                  Expanded(
                    child: Text(
                      l10n.selectApprover,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (state.isLoadingApprovers)
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                ],
              ),
              SizedBox(height: responsive.heightPercentage(2)),
              if (state.approvers.isEmpty && !state.isLoadingApprovers)
                _buildEmptyState(responsive, theme, l10n)
              else if (state.approvers.isNotEmpty)
                _buildApproverDropdown(responsive, theme, state.approvers, l10n)
              else
                _buildLoadingState(responsive),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState(
    Responsive responsive,
    ThemeData theme,
    dynamic l10n,
  ) {
    return Container(
      padding: responsive.padding(all: 20),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.black.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.person_off_outlined,
            size: 48,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: responsive.heightPercentage(1)),
          Text(
            l10n.noApproversAvailable,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(Responsive responsive) {
    return SizedBox(
      height: responsive.heightPercentage(15),
      child: const Center(
        child: CircularProgressIndicator(color: AppColors.primaryBlue),
      ),
    );
  }

  Widget _buildApproverDropdown(
    Responsive responsive,
    ThemeData theme,
    List<ApproverEntity> approvers,
    dynamic l10n,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color: selectedApprover != null
              ? AppColors.primaryBlue.withValues(alpha: 0.5)
              : AppColors.textSecondary.withValues(alpha: 0.3),
          width: selectedApprover != null ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: selectedApprover != null
            ? [
                BoxShadow(
                  color: AppColors.primaryBlue.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<ApproverEntity>(
          value: selectedApprover,
          hint: Container(
            height: 56, // Same height as dropdown items
            padding: responsive.padding(horizontal: 16, vertical: 4),
            child: Row(
              children: [
                Icon(
                  Icons.person_search_rounded,
                  color: AppColors.textSecondary,
                  size: 20,
                ),
                SizedBox(width: responsive.widthPercentage(2)),
                Expanded(
                  child: Text(
                    'Choose an approver', // TODO: Add to l10n
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          icon: Padding(
            padding: responsive.padding(horizontal: 16),
            child: Icon(
              Icons.keyboard_arrow_down_rounded,
              color: AppColors.primaryBlue,
            ),
          ),
          isExpanded: true,
          dropdownColor: Colors.white,
          elevation: 8,
          borderRadius: BorderRadius.circular(12),
          items: approvers.map((approver) {
            return DropdownMenuItem<ApproverEntity>(
              value: approver,
              child: Container(
                height: 56, // Fixed height to prevent overflow
                padding: responsive.padding(horizontal: 16, vertical: 4),
                child: Row(
                  children: [
                    _buildCompactAvatar(responsive, approver),
                    SizedBox(width: responsive.widthPercentage(2.5)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            approver.fullname,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                              fontSize: responsive.fontSize(11),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 1),
                          Text(
                            approver.roleName,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColors.textSecondary,
                              fontSize: responsive.fontSize(8.5),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    if (selectedApprover?.id == approver.id)
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.check, color: Colors.white, size: 12),
                      ),
                  ],
                ),
              ),
            );
          }).toList(),
          onChanged: (ApproverEntity? approver) {
            onApproverSelected(approver);
          },
          selectedItemBuilder: (BuildContext context) {
            return approvers.map<Widget>((ApproverEntity approver) {
              return Container(
                height: 56, // Same fixed height as dropdown items
                padding: responsive.padding(horizontal: 16, vertical: 4),
                child: Row(
                  children: [
                    _buildCompactAvatar(responsive, approver),
                    SizedBox(width: responsive.widthPercentage(2.5)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            approver.fullname,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.textPrimary,
                              fontSize: responsive.fontSize(11),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 1),
                          Text(
                            approver.roleName,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppColors.primaryBlue,
                              fontWeight: FontWeight.w500,
                              fontSize: responsive.fontSize(8.5),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList();
          },
        ),
      ),
    );
  }

  Widget _buildCompactAvatar(Responsive responsive, ApproverEntity approver) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: AppColors.primaryBlue.withValues(alpha: 0.1),
      ),
      child: Center(
        child: Text(
          approver.fullname.isNotEmpty
              ? approver.fullname[0].toUpperCase()
              : '?',
          style: TextStyle(
            color: AppColors.primaryBlue,
            fontSize: responsive.fontSize(14),
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
