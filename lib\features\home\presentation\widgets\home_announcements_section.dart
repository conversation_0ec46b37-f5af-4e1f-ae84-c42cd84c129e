import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/core/routes/app_routes.dart';
import 'package:golderhr/features/home/<USER>/cubit/home_cubit.dart';
import 'package:golderhr/features/home/<USER>/cubit/home_state.dart';

import 'compact_notification_card.dart';

class HomeAnnouncementsSection extends StatelessWidget {
  const HomeAnnouncementsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: context.responsive.padding(vertical: 8.0, horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.0),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: context.responsive.padding(left: 8.0, top: 8.0),
                child: Text(
                  context.l10n.homeNotificationAndUpdate,
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              TextButton(
                onPressed: () {
                  context.push(AppRoutes.notifications);
                },
                child: Text(
                  context.l10n.notificationAll,
                  style: context.lightTheme.textTheme.titleMedium!.copyWith(
                    color: context.lightTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              if (state is HomeLoading) {
                return Center(
                  child: Padding(
                    padding: context.responsive.padding(all: 24.0),
                    child: const CircularProgressIndicator(),
                  ),
                );
              } else if (state is HomeNotificationEmpty) {
                return Center(
                  child: Padding(
                    padding: context.responsive.padding(all: 24.0),
                    child: Text(context.l10n.notificationNoNewUpdates),
                  ),
                );
              } else if (state is HomeError) {
                return Center(
                  child: Padding(
                    padding: context.responsive.padding(all: 24.0),
                    child: Text(
                      state.message,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.red),
                    ),
                  ),
                );
              } else if (state is HomeLoaded) {
                final latestAnnouncements = state.notifications
                    .take(3)
                    .toList();
                if (latestAnnouncements.isEmpty) {
                  return Center(
                    child: Padding(
                      padding: context.responsive.padding(all: 24.0),
                      child: Text(context.l10n.notificationNoNewUpdates),
                    ),
                  );
                }
                return Column(
                  children: [
                    for (int i = 0; i < latestAnnouncements.length; i++) ...[
                      CompactNotificationCard(
                        notification: latestAnnouncements[i],
                      ),
                      if (i < latestAnnouncements.length - 1)
                        Divider(
                          height: 1,
                          indent: 16,
                          endIndent: 16,
                          color: Colors.grey.shade200,
                        ),
                    ],
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }
}
