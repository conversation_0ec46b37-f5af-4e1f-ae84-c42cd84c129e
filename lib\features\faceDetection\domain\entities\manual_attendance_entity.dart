import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:geolocator/geolocator.dart';

class ManualAttendanceEntity extends Equatable {
  final String fullName;
  final String reason;
  final File? failureImage;
  final Map<String, dynamic> deviceInfo;
  final bool isCheckIn;
  final Position? location;
  final String? address;
  final DateTime timestamp;

  const ManualAttendanceEntity({
    required this.fullName,
    required this.reason,
    this.failureImage,
    required this.deviceInfo,
    required this.isCheckIn,
    this.location,
    this.address,
    required this.timestamp,
  });

  @override
  List<Object?> get props => [
        fullName,
        reason,
        failureImage,
        deviceInfo,
        isCheckIn,
        location,
        address,
        timestamp,
      ];
}
