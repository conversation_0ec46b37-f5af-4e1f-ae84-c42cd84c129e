import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/manual_attendance_entity.dart';
import '../repositories/manual_attendance_repository.dart';

class ReviewManualAttendanceUseCase implements UseCase<ManualAttendanceEntity, ReviewManualAttendanceParams> {
  final ManualAttendanceRepository repository;

  ReviewManualAttendanceUseCase(this.repository);

  @override
  Future<Either<Failure, ManualAttendanceEntity>> call(ReviewManualAttendanceParams params) async {
    return await repository.reviewManualAttendance(
      id: params.id,
      status: params.status,
      adminNote: params.adminNote,
    );
  }
}

class ReviewManualAttendanceParams {
  final String id;
  final ManualAttendanceStatus status;
  final String? adminNote;

  ReviewManualAttendanceParams({
    required this.id,
    required this.status,
    this.adminNote,
  });
}
