import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

class RoleEmptyStateWidget extends StatelessWidget {
  final VoidCallback onCreateRole;

  const RoleEmptyStateWidget({
    super.key,
    required this.onCreateRole,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Iconsax.security_user,
            size: responsive.scaleRadius(64),
            color: Colors.grey[400],
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: responsive.scaleHeight(16)),
          Text(
            'No roles found',
            style: AppTextStyle.bold(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Create your first role to get started',
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(24)),
          ElevatedButton.icon(
            onPressed: onCreateRole,
            icon: const Icon(Iconsax.add),
            label: const Text('Add Role'),
          ),
        ],
      ),
    );
  }
}
