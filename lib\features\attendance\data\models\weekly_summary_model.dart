// lib/features/attendance/data/models/weekly_summary_model.dart
import 'package:golderhr/features/attendance/domain/entities/weekly_summary.dart';

class WeeklySummaryModel extends WeeklySummary {
  const WeeklySummaryModel({
    required super.workDays,
    required super.totalHours,
    required super.overtime,
    required super.lateArrivals,
    required super.performance,
  });

  factory WeeklySummaryModel.fromJson(Map<String, dynamic> json) {
    return WeeklySummaryModel(
      workDays: json['workDays'] ?? '0 / 0',
      totalHours: json['totalHours'] ?? '--',
      overtime: json['overtime'] ?? '--',
      lateArrivals: json['lateArrivals'] ?? 0,
      performance: (json['performance'] ?? 0.0)
          .toDouble(), // đảm bảo kiểu double
    );
  }
}
