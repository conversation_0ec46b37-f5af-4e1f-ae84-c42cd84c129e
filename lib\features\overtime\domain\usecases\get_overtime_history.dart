import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_request_entity.dart';
import '../repositories/overtime_repository.dart';

class GetOvertimeHistory
    implements UseCase<List<OvertimeRequestEntity>, OvertimeHistoryParams> {
  final OvertimeRepository repository;

  GetOvertimeHistory(this.repository);

  @override
  Future<Either<Failure, List<OvertimeRequestEntity>>> call(
    OvertimeHistoryParams params,
  ) async {
    return await repository.getOvertimeHistory(
      page: params.page,
      limit: params.limit,
      status: params.status,
    );
  }
}

class OvertimeHistoryParams extends Equatable {
  final int page;
  final int limit;
  final OvertimeStatus? status;

  const OvertimeHistoryParams({this.page = 1, this.limit = 10, this.status});

  @override
  List<Object?> get props => [page, limit, status];
}
