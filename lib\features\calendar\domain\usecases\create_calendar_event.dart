import 'package:dartz/dartz.dart';


import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/calendar_event.dart';

import '../params/create_event_params.dart';
import '../repositories/calendar_repository.dart';

class CreateCalendarEvent implements UseCase<CalendarEvent, CreateEventParams> {
  final CalendarRepository repository;

  CreateCalendarEvent(this.repository);

  @override
  Future<Either<Failure, CalendarEvent>> call(CreateEventParams params) async {
    return await repository.createCalendarEvent(params);
  }
}