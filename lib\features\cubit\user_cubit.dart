// file: /lib/features/auth/presentation/cubits/user_cubit.dart

import 'package:flutter_bloc/flutter_bloc.dart';

// Giả sử bạn đã tạo use case này
import 'package:golderhr/core/usecases/usecase.dart';
import 'package:golderhr/features/auth/domain/entities/user_entity.dart';

import '../auth/domain/usecases/get_cached_user_usecase.dart';

class UserCubit extends Cubit<UserEntity?> {
  final GetCachedUserUseCase _getCachedUserUseCase;

  UserCubit({required GetCachedUserUseCase getCachedUserUseCase})
    : _getCachedUserUseCase = getCachedUserUseCase,
      super(null) {
    loadUserFromCache();
  }

  Future<void> loadUserFromCache() async {
    final result = await _getCachedUserUseCase(NoParams());
    result.fold((failure) => emit(null), (user) => emit(user));
  }

  void updateUser(UserEntity user) {
    emit(user);
  }

  void clearUser() {
    emit(null);
  }
}
