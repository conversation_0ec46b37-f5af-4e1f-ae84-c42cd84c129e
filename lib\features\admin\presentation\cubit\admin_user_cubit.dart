import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../domain/usecases/get_all_users_usecase.dart';
import '../../domain/usecases/create_user_usecase.dart';
import '../../domain/usecases/admin_user_usecases.dart';
import '../../../../core/usecases/usecase.dart';

part 'admin_user_state.dart';

class AdminUserCubit extends Cubit<AdminUserState> {
  final GetAllUsersUseCase getAllUsersUseCase;
  final CreateUserUseCase createUserUseCase;
  final GetUserByIdUseCase getUserByIdUseCase;
  final UpdateUserUseCase updateUserUseCase;
  final SoftDeleteUserUseCase softDeleteUserUseCase;
  final RestoreUserUseCase restoreUserUseCase;
  final ToggleUserStatusUseCase toggleUserStatusUseCase;
  final ResetUserPasswordUseCase resetUserPasswordUseCase;
  final GetUserStatisticsUseCase getUserStatisticsUseCase;
  final BulkDeleteUsersUseCase bulkDeleteUsersUseCase;
  final BulkRestoreUsersUseCase bulkRestoreUsersUseCase;

  AdminUserCubit({
    required this.getAllUsersUseCase,
    required this.createUserUseCase,
    required this.getUserByIdUseCase,
    required this.updateUserUseCase,
    required this.softDeleteUserUseCase,
    required this.restoreUserUseCase,
    required this.toggleUserStatusUseCase,
    required this.resetUserPasswordUseCase,
    required this.getUserStatisticsUseCase,
    required this.bulkDeleteUsersUseCase,
    required this.bulkRestoreUsersUseCase,
  }) : super(const AdminUserState());

  // Load all users with filters
  Future<void> loadUsers({int page = 1, bool loadMore = false}) async {
    print(
      '🔍 [DEBUG] AdminUserCubit: loadUsers called with page: $page, filters: ${state.filterOptions.search}',
    );

    emit(state.copyWith(isLoading: true, error: null));

    final params = GetAllUsersParams.fromFilterOptions(
      state.filterOptions,
      page: page,
      limit: 10,
    );

    print(
      '🔍 [DEBUG] AdminUserCubit: API params - page: $page, search: ${params.search}, role: ${params.role}',
    );

    final result = await getAllUsersUseCase(params);

    result.fold(
      (failure) {
        emit(state.copyWith(isLoading: false, error: failure.message));
      },
      (userListResult) {
        emit(
          state.copyWith(
            isLoading: false,
            users: userListResult.users,
            pagination: userListResult.pagination,
            error: null,
          ),
        );
      },
    );
  }

  // Apply filters
  Future<void> applyFilters(UserFilterOptions filterOptions) async {
    emit(state.copyWith(filterOptions: filterOptions));
    await loadUsers();
  }

  // Search users
  Future<void> searchUsers(String query) async {
    print('🔍 [DEBUG] AdminUserCubit: searchUsers called with query: "$query"');
    final searchQuery = query.trim().isEmpty ? null : query.trim();
    final newFilters = state.filterOptions.copyWith(
      search: searchQuery,
      clearSearch: searchQuery == null,
    );
    print('🔍 [DEBUG] AdminUserCubit: newFilters: ${newFilters.search}');
    await applyFilters(newFilters);
  }

  // Filter by role
  Future<void> filterByRole(String? role) async {
    final newFilters = state.filterOptions.copyWith(
      role: role,
      clearRole: role == null,
    );
    await applyFilters(newFilters);
  }

  // Filter by status
  Future<void> filterByStatus(UserStatus status) async {
    final newFilters = state.filterOptions.copyWith(
      status: status,
      includeDeleted: status == UserStatus.deleted || status == UserStatus.all,
    );
    await applyFilters(newFilters);
  }

  // Sort users
  Future<void> sortUsers(UserSortField sortBy, SortOrder sortOrder) async {
    final newFilters = state.filterOptions.copyWith(
      sortBy: sortBy,
      sortOrder: sortOrder,
    );
    await applyFilters(newFilters);
  }

  // Create user
  Future<void> createUser(CreateUserParams params) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await createUserUseCase(params);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (user) {
        emit(
          state.copyWith(
            isProcessing: false,
            users: [user, ...state.users],
            error: null,
          ),
        );
      },
    );
  }

  // Update user
  Future<void> updateUser(UpdateUserParams params) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await updateUserUseCase(params);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (updatedUser) {
        final updatedUsers = state.users.map((user) {
          return user.id == updatedUser.id ? updatedUser : user;
        }).toList();

        emit(
          state.copyWith(isProcessing: false, users: updatedUsers, error: null),
        );
      },
    );
  }

  // Soft delete user
  Future<void> softDeleteUser(String userId) async {
    emit(state.copyWith(isProcessing: true, error: null, successMessage: null));

    final result = await softDeleteUserUseCase(userId);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (deletedUser) {
        final updatedUsers = state.users.map((user) {
          return user.id == deletedUser.id ? deletedUser : user;
        }).toList();

        emit(
          state.copyWith(
            isProcessing: false,
            users: updatedUsers,
            error: null,
            successMessage: 'User deleted successfully',
          ),
        );
      },
    );
  }

  // Restore user
  Future<void> restoreUser(String userId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await restoreUserUseCase(userId);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (restoredUser) {
        final updatedUsers = state.users.map((user) {
          return user.id == restoredUser.id ? restoredUser : user;
        }).toList();

        emit(
          state.copyWith(isProcessing: false, users: updatedUsers, error: null),
        );
      },
    );
  }

  // Toggle user status
  Future<void> toggleUserStatus(String userId) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await toggleUserStatusUseCase(userId);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (updatedUser) {
        final updatedUsers = state.users.map((user) {
          return user.id == updatedUser.id ? updatedUser : user;
        }).toList();

        emit(
          state.copyWith(isProcessing: false, users: updatedUsers, error: null),
        );
      },
    );
  }

  // Reset user password
  Future<void> resetUserPassword(String userId, String newPassword) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final params = ResetPasswordParams(
      userId: userId,
      newPassword: newPassword,
    );
    final result = await resetUserPasswordUseCase(params);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (_) {
        emit(state.copyWith(isProcessing: false, error: null));
      },
    );
  }

  // Load statistics
  Future<void> loadStatistics() async {
    emit(state.copyWith(isLoadingStats: true));

    final result = await getUserStatisticsUseCase(NoParams());

    result.fold(
      (failure) {
        emit(state.copyWith(isLoadingStats: false, error: failure.message));
      },
      (statistics) {
        emit(
          state.copyWith(
            isLoadingStats: false,
            statistics: statistics,
            error: null,
          ),
        );
      },
    );
  }

  // Bulk delete users
  Future<void> bulkDeleteUsers(List<String> userIds) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await bulkDeleteUsersUseCase(userIds);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (result) {
        // Refresh the user list
        loadUsers();
      },
    );
  }

  // Bulk restore users
  Future<void> bulkRestoreUsers(List<String> userIds) async {
    emit(state.copyWith(isProcessing: true, error: null));

    final result = await bulkRestoreUsersUseCase(userIds);

    result.fold(
      (failure) {
        emit(state.copyWith(isProcessing: false, error: failure.message));
      },
      (result) {
        // Refresh the user list
        loadUsers();
      },
    );
  }

  // Select/deselect users for bulk operations
  void toggleUserSelection(String userId) {
    final selectedUsers = List<String>.from(state.selectedUserIds);

    if (selectedUsers.contains(userId)) {
      selectedUsers.remove(userId);
    } else {
      selectedUsers.add(userId);
    }

    emit(state.copyWith(selectedUserIds: selectedUsers));
  }

  void selectAllUsers() {
    final allUserIds = state.users.map((user) => user.id).toList();
    emit(state.copyWith(selectedUserIds: allUserIds));
  }

  void clearSelection() {
    emit(state.copyWith(selectedUserIds: []));
  }

  // Clear error
  void clearError() {
    emit(state.copyWith(error: null));
  }

  // Clear success message
  void clearSuccess() {
    emit(state.copyWith(successMessage: null));
  }
}
