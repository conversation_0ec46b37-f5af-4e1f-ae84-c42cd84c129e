import 'package:golderhr/features/auth/data/models/user_model.dart';

class RegisterResponseModel {
  final bool success;
  final UserModel user;

  RegisterResponseModel({required this.success, required this.user});

  factory RegisterResponseModel.fromJson(Map<String, dynamic> json) {
    return RegisterResponseModel(
      success: json['success'] as bool,
      user: UserModel.fromJson(json['data']['user'] as Map<String, dynamic>),
    );
  }
}
