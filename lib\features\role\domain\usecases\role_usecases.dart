import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/role_entity.dart';
import '../repositories/role_repository.dart';
import '../../data/models/role_model.dart';

// Get all roles use case
class GetAllRoles implements UseCase<RoleListResult, GetAllRolesParams> {
  final RoleRepository repository;

  GetAllRoles(this.repository);

  @override
  Future<Either<Failure, RoleListResult>> call(GetAllRolesParams params) async {
    return await repository.getAllRoles(
      page: params.page,
      limit: params.limit,
      search: params.search,
    );
  }
}

class GetAllRolesParams {
  final int page;
  final int limit;
  final String? search;

  const GetAllRolesParams({this.page = 1, this.limit = 10, this.search});
}

// Get role by ID use case
class GetRoleById implements UseCase<RoleEntity, String> {
  final RoleRepository repository;

  GetRoleById(this.repository);

  @override
  Future<Either<Failure, RoleEntity>> call(String roleId) async {
    return await repository.getRoleById(roleId);
  }
}

// Create role use case
class CreateRole implements UseCase<RoleEntity, CreateRoleParams> {
  final RoleRepository repository;

  CreateRole(this.repository);

  @override
  Future<Either<Failure, RoleEntity>> call(CreateRoleParams params) async {
    // Check if role name already exists
    final nameExistsResult = await repository.checkRoleNameExists(params.name);

    return nameExistsResult.fold((failure) => Left(failure), (exists) {
      if (exists) {
        return Left(ValidationFailure('Role name already exists'));
      }
      return repository.createRole(name: params.name);
    });
  }
}

// Update role use case
class UpdateRole implements UseCase<RoleEntity, UpdateRoleParams> {
  final RoleRepository repository;

  UpdateRole(this.repository);

  @override
  Future<Either<Failure, RoleEntity>> call(UpdateRoleParams params) async {
    // Check if new name already exists (excluding current role)
    if (params.name != null) {
      final nameExistsResult = await repository.checkRoleNameExists(
        params.name!,
        excludeId: params.roleId,
      );

      final nameExists = nameExistsResult.fold(
        (failure) => false, // If check fails, proceed with update
        (exists) => exists,
      );

      if (nameExists) {
        return Left(ValidationFailure('Role name already exists'));
      }
    }

    return await repository.updateRole(params.roleId, name: params.name);
  }
}

// Delete role use case
class DeleteRole implements UseCase<void, String> {
  final RoleRepository repository;

  DeleteRole(this.repository);

  @override
  Future<Either<Failure, void>> call(String roleId) async {
    return await repository.deleteRole(roleId);
  }
}

// Get roles for dropdown use case
class GetRolesForDropdown implements UseCase<List<RoleEntity>, NoParams> {
  final RoleRepository repository;

  GetRolesForDropdown(this.repository);

  @override
  Future<Either<Failure, List<RoleEntity>>> call(NoParams params) async {
    return await repository.getRolesForDropdown();
  }
}

// Validation failure class
class ValidationFailure extends Failure {
  ValidationFailure(String message) : super(message);
}
