import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/team_entity.dart';
import '../../domain/entities/team_chat_entity.dart';
import '../../domain/repositories/team_repository.dart';
import '../datasources/team_remote_data_source.dart';

class TeamRepositoryImpl implements TeamRepository {
  final TeamRemoteDataSource remoteDataSource;

  TeamRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<TeamEntity>>> getUserTeams() async {
    try {
      final teams = await remoteDataSource.getUserTeams();
      return Right(teams);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TeamEntity>> getTeamDetails(String teamId) async {
    try {
      final team = await remoteDataSource.getTeamDetails(teamId);
      return Right(team);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TeamEntity>> createTeam(Map<String, dynamic> teamData) async {
    try {
      final team = await remoteDataSource.createTeam(teamData);
      return Right(team);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> addTeamMember(String teamId, String userId, String role) async {
    try {
      await remoteDataSource.addTeamMember(teamId, userId, role);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getTeamStats(String teamId) async {
    try {
      final stats = await remoteDataSource.getTeamStats(teamId);
      return Right(stats);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getTeamChatHistory(String teamId, {int page = 1, int limit = 50}) async {
    try {
      final chatData = await remoteDataSource.getTeamChatHistory(teamId, page: page, limit: limit);
      return Right(chatData);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> pinMessage(String teamId, String messageId) async {
    try {
      await remoteDataSource.pinMessage(teamId, messageId);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, void>> createMeeting(String teamId, Map<String, dynamic> meetingData) async {
    try {
      await remoteDataSource.createMeeting(teamId, meetingData);
      return const Right(null);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('Unexpected error occurred'));
    }
  }
}
