import 'package:flutter/material.dart';
import '../responsive/responsive.dart';

extension ResponsiveExtension on BuildContext {
  /// T<PERSON>y cập n<PERSON>h đến Responsive instance
  Responsive get responsive => Responsive.of(this);

  /// Shortcut: Font size responsive
  double rf(double size) => responsive.fontSize(size);

  /// Shortcut: Width theo tỷ lệ thiết kế
  double rw(double size) => responsive.scaleWidth(size);

  /// Shortcut: Height theo tỷ lệ thiết kế
  double rh(double size) => responsive.scaleHeight(size);

  /// Shortcut: Radius hoặc icon size theo tỷ lệ nhỏ nhất
  double rr(double size) => responsive.scaleRadius(size);

  /// Shortcut: Padding an toàn
  EdgeInsets get safePadding => responsive.safeAreaPadding;

  /// Shortcut: Kiểm tra layout
  bool get isMobile => responsive.isMobile;
  bool get isTablet => responsive.isTablet;
  bool get isLandscape => responsive.isLandscape;
}
