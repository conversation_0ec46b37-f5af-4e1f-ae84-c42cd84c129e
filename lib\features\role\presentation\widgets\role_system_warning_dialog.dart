import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../../domain/entities/role_entity.dart';

class RoleSystemWarningDialog extends StatelessWidget {
  final RoleEntity role;

  const RoleSystemWarningDialog({
    super.key,
    required this.role,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
      ),
      elevation: 10,
      child: Container(
        width: responsive.adaptiveValue<double>(
          mobile: 320,
          tablet: 400,
          mobileLandscape: 360,
          tabletLandscape: 440,
        ),
        padding: responsive.padding(all: 24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(responsive.defaultRadius * 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              spreadRadius: 0,
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Warning Icon
            Container(
              width: responsive.scaleWidth(64),
              height: responsive.scaleHeight(64),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.orange.withValues(alpha: 0.15),
                    Colors.orange.withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(
                  responsive.defaultRadius * 2,
                ),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.2),
                  width: 2,
                ),
              ),
              child: Icon(
                Iconsax.shield_security,
                color: Colors.orange[600],
                size: responsive.scaleRadius(32),
              ),
            ),

            SizedBox(height: responsive.scaleHeight(20)),

            // Title
            Text(
              'System Role Protected',
              style: AppTextStyle.bold(
                context,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 22,
                  mobileLandscape: 21,
                  tabletLandscape: 24,
                ),
                color: Colors.grey[800],
              ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: responsive.scaleHeight(12)),

            // Content
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: AppTextStyle.regular(
                  context,
                  size: responsive.adaptiveValue<double>(
                    mobile: 14,
                    tablet: 15,
                    mobileLandscape: 14.5,
                    tabletLandscape: 16,
                  ),
                  color: Colors.grey[600],
                ),
                children: [
                  const TextSpan(text: 'The role '),
                  TextSpan(
                    text: '"${role.name.toUpperCase()}"',
                    style: AppTextStyle.bold(
                      context,
                      size: responsive.adaptiveValue<double>(
                        mobile: 14,
                        tablet: 15,
                        mobileLandscape: 14.5,
                        tabletLandscape: 16,
                      ),
                      color: Colors.orange[600],
                    ),
                  ),
                  const TextSpan(
                    text:
                        ' is a system role and cannot be deleted.\n\nSystem roles are essential for the application to function properly.',
                  ),
                ],
              ),
            ),

            SizedBox(height: responsive.scaleHeight(28)),

            // OK Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[600],
                  foregroundColor: Colors.white,
                  padding: responsive.padding(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      responsive.defaultRadius,
                    ),
                  ),
                  elevation: 2,
                  shadowColor: Colors.orange.withValues(alpha: 0.3),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Iconsax.shield_tick,
                      size: responsive.scaleRadius(16),
                      color: Colors.white,
                    ),
                    SizedBox(width: responsive.scaleWidth(8)),
                    Text(
                      'I Understand',
                      style: AppTextStyle.medium(
                        context,
                        size: 15,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
