// lib/features/faceDetection/data/models/attendance_status_model.dart
import '../../domain/entities/attendance_status_entity.dart';

class AttendanceStatusModel extends AttendanceStatusEntity {
  const AttendanceStatusModel({
    required super.canCheckIn,
    required super.canCheckOut,
    required super.checkInsCount,
    required super.checkOutsCount,
    super.lastCheckInTime,
    super.lastCheckOutTime,
    required super.nextAction,
    required super.hasCheckedIn,
    required super.hasCheckedOut,
    super.checkInTime,
    super.checkOutTime,
  });

  factory AttendanceStatusModel.fromJson(Map<String, dynamic> json) {
    NextActionType parseNextAction(String? action) {
      if (action == 'CHECK_IN') {
        return NextActionType.CHECK_IN;
      } else if (action == 'CHECK_OUT') {
        return NextActionType.CHECK_OUT;
      }
      return NextActionType.UNKNOWN;
    }

    DateTime? parseOptionalDateTime(dynamic value) {
      if (value is String) {
        return DateTime.tryParse(value);
      }
      return null;
    }

    return AttendanceStatusModel(
      // Các trường mới
      canCheckIn: json['canCheckIn'] as bool? ?? false,
      canCheckOut: json['canCheckOut'] as bool? ?? false,
      checkInsCount: json['checkInsCount'] as int? ?? 0,
      checkOutsCount: json['checkOutsCount'] as int? ?? 0,
      lastCheckInTime: parseOptionalDateTime(json['lastCheckInTime']),
      lastCheckOutTime: parseOptionalDateTime(json['lastCheckOutTime']),
      nextAction: parseNextAction(json['nextAction'] as String?),

      // Các trường hiện có, nhưng parse an toàn hơn
      hasCheckedIn: json['hasCheckedIn'] as bool? ?? false,
      hasCheckedOut: json['hasCheckedOut'] as bool? ?? false,
      checkInTime: parseOptionalDateTime(json['checkInTime']),
      checkOutTime: parseOptionalDateTime(json['checkOutTime']),
    );
  }

  Map<String, dynamic> toJson() {
    String? formatNextAction(NextActionType action) {
      switch (action) {
        case NextActionType.CHECK_IN:
          return 'CHECK_IN';
        case NextActionType.CHECK_OUT:
          return 'CHECK_OUT';
        case NextActionType.UNKNOWN:
          return null;
      }
    }

    return {
      'canCheckIn': canCheckIn,
      'canCheckOut': canCheckOut,
      'checkInsCount': checkInsCount,
      'checkOutsCount': checkOutsCount,
      'lastCheckInTime': lastCheckInTime?.toIso8601String(),
      'lastCheckOutTime': lastCheckOutTime?.toIso8601String(),
      'nextAction': formatNextAction(nextAction),
      'hasCheckedIn': hasCheckedIn,
      'hasCheckedOut': hasCheckedOut,
      'checkInTime': checkInTime?.toIso8601String(),
      'checkOutTime': checkOutTime?.toIso8601String(),
    };
  }
}
