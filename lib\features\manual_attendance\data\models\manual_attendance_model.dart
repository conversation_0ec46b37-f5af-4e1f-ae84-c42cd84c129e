import '../../domain/entities/manual_attendance_entity.dart';

class ManualAttendanceModel extends ManualAttendanceEntity {
  const ManualAttendanceModel({
    required super.id,
    required super.userId,
    required super.fullName,
    required super.reason,
    super.failureImage,
    required super.deviceInfo,
    required super.isCheckIn,
    required super.timestamp,
    required super.status,
    super.adminNote,
    super.reviewedBy,
    super.reviewedAt,
    required super.createdAt,
    required super.updatedAt,
    super.userInfo,
    super.reviewerInfo,
  });

  factory ManualAttendanceModel.fromJson(Map<String, dynamic> json) {
    return ManualAttendanceModel(
      id: json['_id'] ?? '',
      userId: json['userId'] is String ? json['userId'] : json['userId']?['_id'] ?? '',
      fullName: json['fullName'] ?? '',
      reason: json['reason'] ?? '',
      failureImage: json['failureImage'],
      deviceInfo: json['deviceInfo'] ?? {},
      isCheckIn: json['isCheckIn'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
      status: ManualAttendanceStatusExtension.fromString(json['status'] ?? 'pending'),
      adminNote: json['adminNote'],
      reviewedBy: json['reviewedBy'] is String ? json['reviewedBy'] : json['reviewedBy']?['_id'],
      reviewedAt: json['reviewedAt'] != null ? DateTime.parse(json['reviewedAt']) : null,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      userInfo: json['userId'] is Map<String, dynamic> 
          ? UserInfoModel.fromJson(json['userId'])
          : null,
      reviewerInfo: json['reviewedBy'] is Map<String, dynamic>
          ? UserInfoModel.fromJson(json['reviewedBy'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      'fullName': fullName,
      'reason': reason,
      'failureImage': failureImage,
      'deviceInfo': deviceInfo,
      'isCheckIn': isCheckIn,
      'timestamp': timestamp.toIso8601String(),
      'status': status.value,
      'adminNote': adminNote,
      'reviewedBy': reviewedBy,
      'reviewedAt': reviewedAt?.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class UserInfoModel extends UserInfo {
  const UserInfoModel({
    required super.id,
    required super.fullName,
    required super.email,
    super.department,
  });

  factory UserInfoModel.fromJson(Map<String, dynamic> json) {
    return UserInfoModel(
      id: json['_id'] ?? '',
      fullName: json['fullName'] ?? '',
      email: json['email'] ?? '',
      department: json['department'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'fullName': fullName,
      'email': email,
      'department': department,
    };
  }
}

class ManualAttendancePaginationModel extends ManualAttendancePagination {
  const ManualAttendancePaginationModel({
    required super.currentPage,
    required super.totalPages,
    required super.totalItems,
    required super.itemsPerPage,
  });

  factory ManualAttendancePaginationModel.fromJson(Map<String, dynamic> json) {
    return ManualAttendancePaginationModel(
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      totalItems: json['totalItems'] ?? 0,
      itemsPerPage: json['itemsPerPage'] ?? 10,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentPage': currentPage,
      'totalPages': totalPages,
      'totalItems': totalItems,
      'itemsPerPage': itemsPerPage,
    };
  }
}
