class LeaveBalanceByType {
  final int used;
  final int remaining;
  final int total;

  const LeaveBalanceByType({
    required this.used,
    required this.remaining,
    required this.total,
  });

  factory LeaveBalanceByType.fromJson(Map<String, dynamic> json) {
    return LeaveBalanceByType(
      used: json['used'] ?? 0,
      remaining: json['remaining'] ?? 0,
      total: json['total'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'used': used, 'remaining': remaining, 'total': total};
  }
}

class LeaveSummaryModel {
  final int thisMonthDays;
  final int thisYearDays;
  final int pendingRequests;
  final int approvedRequests;
  final int rejectedRequests;
  final int remainingDays;
  final int totalAllowedDays;
  final Map<String, LeaveBalanceByType>? leaveBalanceByType;

  const LeaveSummaryModel({
    required this.thisMonthDays,
    required this.thisYearDays,
    required this.pendingRequests,
    required this.approvedRequests,
    required this.rejectedRequests,
    required this.remainingDays,
    required this.totalAllowedDays,
    this.leaveBalanceByType,
  });

  factory LeaveSummaryModel.fromJson(Map<String, dynamic> json) {
    Map<String, LeaveBalanceByType>? balanceByType;
    if (json['leaveBalanceByType'] != null) {
      final balanceData = json['leaveBalanceByType'] as Map<String, dynamic>;
      balanceByType = balanceData.map(
        (key, value) => MapEntry(
          key,
          LeaveBalanceByType.fromJson(value as Map<String, dynamic>),
        ),
      );
    }

    return LeaveSummaryModel(
      thisMonthDays: json['thisMonthDays'] ?? 0,
      thisYearDays: json['thisYearDays'] ?? 0,
      pendingRequests: json['pendingRequests'] ?? 0,
      approvedRequests: json['approvedRequests'] ?? 0,
      rejectedRequests: json['rejectedRequests'] ?? 0,
      remainingDays: json['remainingDays'] ?? 0,
      totalAllowedDays: json['totalAllowedDays'] ?? 12,
      leaveBalanceByType: balanceByType,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'thisMonthDays': thisMonthDays,
      'thisYearDays': thisYearDays,
      'pendingRequests': pendingRequests,
      'approvedRequests': approvedRequests,
      'rejectedRequests': rejectedRequests,
      'remainingDays': remainingDays,
      'totalAllowedDays': totalAllowedDays,
      'leaveBalanceByType': leaveBalanceByType?.map(
        (key, value) => MapEntry(key, value.toJson()),
      ),
    };
  }

  // Convert to LeaveBalance entity for UI
  toLeaveBalance() {
    return {
      'remainingDays': remainingDays,
      'totalDays': totalAllowedDays,
      'usedDays': thisYearDays,
      'pendingDays': pendingRequests,
      'approvedDays': approvedRequests,
      'rejectedDays': rejectedRequests,
    };
  }
}
