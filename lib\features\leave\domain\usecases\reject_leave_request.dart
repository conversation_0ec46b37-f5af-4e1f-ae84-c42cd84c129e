import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/leave_request.dart';
import '../repositories/leave_admin_repository.dart';

class RejectLeaveRequest
    implements UseCase<LeaveRequest, RejectLeaveRequestParams> {
  final LeaveAdminRepository repository;

  RejectLeaveRequest(this.repository);

  @override
  Future<Either<Failure, LeaveRequest>> call(
    RejectLeaveRequestParams params,
  ) async {
    return await repository.rejectLeaveRequest(
      params.requestId,
      params.rejectionReason,
    );
  }
}

class RejectLeaveRequestParams extends Equatable {
  final String requestId;
  final String rejectionReason;

  const RejectLeaveRequestParams({
    required this.requestId,
    required this.rejectionReason,
  });

  @override
  List<Object?> get props => [requestId, rejectionReason];
}
