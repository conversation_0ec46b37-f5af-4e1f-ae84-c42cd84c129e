import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'dart:async';
import 'package:intl/intl.dart';

import '../../../../core/responsive/responsive.dart';

class HeaderCardWidget extends StatefulWidget {
  final bool isCheckedIn;

  const HeaderCardWidget({super.key, required this.isCheckedIn});

  @override
  State<HeaderCardWidget> createState() => _HeaderCardWidgetState();
}

class _HeaderCardWidgetState extends State<HeaderCardWidget> {
  late Timer _timer;
  late String _currentTime;
  late String _currentDate;

  @override
  void initState() {
    super.initState();
    _updateTime();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateTime();
    });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateTime() {
    final now = DateTime.now();
    setState(() {
      _currentTime = DateFormat('HH:mm:ss').format(now);
      _currentDate = DateFormat('EEEE, dd MMMM yyyy').format(now);
    });
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);
    final l10n = context.l10n;
    const primaryColor = Color(0xFF4A56E2);
    const accentColor = Color(0xFF6772FF);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(responsive.widthPercentage(6)),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [primaryColor, accentColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: primaryColor.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            _currentTime,
            style: theme.textTheme.displayMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w700,
              fontSize: responsive.fontSize(40),
            ),
          ),
          SizedBox(height: responsive.heightPercentage(0.5)),
          Text(
            _currentDate,
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w400,
              fontSize: responsive.fontSize(18),
            ),
          ),
          SizedBox(height: responsive.heightPercentage(2.5)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: responsive.widthPercentage(4),
              vertical: responsive.heightPercentage(1),
            ),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.isCheckedIn ? Icons.check_circle : Icons.exit_to_app,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  widget.isCheckedIn ? l10n.checkedIn : l10n.notCheckedIn,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
