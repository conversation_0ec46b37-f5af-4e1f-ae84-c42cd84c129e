import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:geolocator/geolocator.dart';
import 'package:golderhr/core/error/failures.dart';
import 'package:golderhr/core/usecases/usecase.dart';
import '../entities/manual_attendance_entity.dart';
import '../repositories/attendance_repository.dart';

class SubmitManualAttendanceUseCase implements UseCase<void, ManualAttendanceParams> {
  final AttendanceRepository repository;

  SubmitManualAttendanceUseCase(this.repository);

  @override
  Future<Either<Failure, void>> call(ManualAttendanceParams params) async {
    return await repository.submitManualAttendance(
      ManualAttendanceEntity(
        fullName: params.fullName,
        reason: params.reason,
        failureImage: params.failureImage,
        deviceInfo: params.deviceInfo,
        isCheckIn: params.isCheckIn,
        location: params.location,
        address: params.address,
        timestamp: DateTime.now(),
      ),
    );
  }
}

class ManualAttendanceParams {
  final String fullName;
  final String reason;
  final File? failureImage;
  final Map<String, dynamic> deviceInfo;
  final bool isCheckIn;
  final Position? location;
  final String? address;

  ManualAttendanceParams({
    required this.fullName,
    required this.reason,
    this.failureImage,
    required this.deviceInfo,
    required this.isCheckIn,
    this.location,
    this.address,
  });
}
