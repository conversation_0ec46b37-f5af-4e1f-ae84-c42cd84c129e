import '../../domain/entities/team_task_entity.dart';

class TeamTaskModel extends TeamTaskEntity {
  const TeamTaskModel({
    required super.id,
    required super.teamId,
    required super.title,
    super.description,
    required super.assignedTo,
    required super.createdBy,
    required super.status,
    required super.priority,
    super.dueDate,
    super.attachments,
    super.comments,
    super.createdAt,
    super.updatedAt,
  });

  factory TeamTaskModel.fromJson(Map<String, dynamic> json) {
    return TeamTaskModel(
      id: json['_id'] ?? json['id'],
      teamId: json['teamId'],
      title: json['title'],
      description: json['description'],
      assignedTo: json['assignedTo'] is Map 
          ? TeamMemberModel.fromJson(json['assignedTo'])
          : json['assignedTo'],
      createdBy: json['createdBy'] is Map
          ? TeamMemberModel.fromJson(json['createdBy'])
          : json['createdBy'],
      status: TaskStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TaskStatus.todo,
      ),
      priority: TaskPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => TaskPriority.medium,
      ),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      attachments: (json['attachments'] as List?)
          ?.map((e) => TaskAttachmentModel.fromJson(e))
          .toList() ?? [],
      comments: (json['comments'] as List?)
          ?.map((e) => TaskCommentModel.fromJson(e))
          .toList() ?? [],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'teamId': teamId,
      'title': title,
      'description': description,
      'assignedTo': assignedTo is TeamMemberEntity 
          ? (assignedTo as TeamMemberEntity).id
          : assignedTo,
      'createdBy': createdBy is TeamMemberEntity
          ? (createdBy as TeamMemberEntity).id
          : createdBy,
      'status': status.name,
      'priority': priority.name,
      'dueDate': dueDate?.toIso8601String(),
      'attachments': attachments?.map((e) => (e as TaskAttachmentModel).toJson()).toList(),
      'comments': comments?.map((e) => (e as TaskCommentModel).toJson()).toList(),
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }
}

class TaskAttachmentModel extends TaskAttachmentEntity {
  const TaskAttachmentModel({
    required super.fileName,
    required super.fileUrl,
    required super.fileType,
    required super.uploadedAt,
  });

  factory TaskAttachmentModel.fromJson(Map<String, dynamic> json) {
    return TaskAttachmentModel(
      fileName: json['fileName'],
      fileUrl: json['fileUrl'],
      fileType: json['fileType'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fileUrl': fileUrl,
      'fileType': fileType,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }
}

class TaskCommentModel extends TaskCommentEntity {
  const TaskCommentModel({
    required super.userId,
    required super.message,
    required super.createdAt,
  });

  factory TaskCommentModel.fromJson(Map<String, dynamic> json) {
    return TaskCommentModel(
      userId: json['userId'],
      message: json['message'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'message': message,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class TeamMemberModel extends TeamMemberEntity {
  const TeamMemberModel({
    required super.id,
    required super.fullname,
    super.avatar,
    super.email,
    super.department,
    super.position,
    super.role,
    super.joinedAt,
    super.isActive = true,
  });

  factory TeamMemberModel.fromJson(Map<String, dynamic> json) {
    return TeamMemberModel(
      id: json['_id'] ?? json['id'],
      fullname: json['fullname'],
      avatar: json['avatar'],
      email: json['email'],
      department: json['department'],
      position: json['position'],
      role: json['role'],
      joinedAt: json['joinedAt'] != null ? DateTime.parse(json['joinedAt']) : null,
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fullname': fullname,
      'avatar': avatar,
      'email': email,
      'department': department,
      'position': position,
      'role': role,
      'joinedAt': joinedAt?.toIso8601String(),
      'isActive': isActive,
    };
  }
}
