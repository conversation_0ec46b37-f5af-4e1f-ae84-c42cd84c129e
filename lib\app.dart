import 'package:device_preview/device_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/routes/app_pages.dart';
import 'package:golderhr/core/services/navigation_service.dart';
import 'features/auth/presentation/cubit/auth_cubit.dart';
import 'features/cubit/language_cubit.dart';
import 'features/cubit/user_cubit.dart';
import 'features/department/presentation/cubit/department_cubit.dart';
import 'features/notification/presentation/cubit/notification_cubit.dart';
import 'injection_container.dart';
import 'l10n/app_localizations.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => sl<AuthCubit>()),
        BlocProvider(create: (_) => sl<LanguageCubit>()),
        BlocProvider(create: (_) => sl<UserCubit>()),
        BlocProvider(create: (_) => sl<DepartmentCubit>()),
        BlocProvider(
          create: (_) => sl<NotificationCubit>()..fetchNotifications(),
        ),
      ],
      child: BlocBuilder<LanguageCubit, String>(
        builder: (context, languageCode) {
          return MaterialApp.router(
            debugShowCheckedModeBanner: false,
            locale: Locale(languageCode),
            builder: DevicePreview.appBuilder,
            theme: context.lightTheme,
            title: 'Golden HR',
            routerConfig: appRouter,
            // Add navigator key for global navigation
            key: NavigationService.navigatorKey,
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [Locale('en'), Locale('vi')],
          );
        },
      ),
    );
  }
}
