// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import '../../../../shared/theme/app_colors.dart';
// import '../../../../shared/theme/app_theme_helper.dart';
// import '../../domain/entities/team_entity.dart';
// import '../cubit/team_chat_cubit.dart';

// class TeamChatContent extends StatefulWidget {
//   final TeamEntity team;

//   const TeamChatContent({super.key, required this.team});

//   @override
//   State<TeamChatContent> createState() => _TeamChatContentState();
// }

// class _TeamChatContentState extends State<TeamChatContent> {
//   final TextEditingController _messageController = TextEditingController();
//   final ScrollController _scrollController = ScrollController();

//   @override
//   void initState() {
//     super.initState();
//     // Load chat history when chat is opened
//     context.read<TeamChatCubit>().loadChatHistory(widget.team.id);
//   }

//   @override
//   void dispose() {
//     _messageController.dispose();
//     _scrollController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       color: AppColors.background,
//       child: Column(
//         children: [
//           // Chat header
//           _buildChatHeader(),
//           // Messages area
//           Expanded(
//             child: BlocBuilder<TeamChatCubit, TeamChatState>(
//               builder: (context, state) {
//                 if (state is TeamChatLoading) {
//                   return const Center(child: CircularProgressIndicator());
//                 }

//                 if (state is TeamChatError) {
//                   return Center(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Icon(
//                           Icons.error_outline,
//                           size: 48,
//                           color: AppColors.error,
//                         ),
//                         const SizedBox(height: 16),
//                         Text(
//                           'Failed to load chat',
//                           style: AppTextStyles.bodyLarge.copyWith(
//                             color: AppColors.error,
//                           ),
//                         ),
//                         const SizedBox(height: 8),
//                         ElevatedButton(
//                           onPressed: () {
//                             context.read<TeamChatCubit>().loadChatHistory(widget.team.id);
//                           },
//                           child: const Text('Retry'),
//                         ),
//                       ],
//                     ),
//                   );
//                 }

//                 if (state is TeamChatLoaded) {
//                   return _buildMessagesList(state.messages);
//                 }

//                 return _buildEmptyChat();
//               },
//             ),
//           ),
//           // Message input
//           // _buildMessageInput(),
//         ],
//       ),
//     );
//   }

//   Widget _buildChatHeader() {
//     return Container(
//       padding: const EdgeInsets.all(16),
//       decoration: BoxDecoration(
//         color: Colors.white,
//         boxShadow: [
//           BoxShadow(
//             color: Colors.black.withValues(alpha: 0.05),
//             blurRadius: 4,
//             offset: const Offset(0, 2),
//           ),
//         ],
//       ),
//       child: Row(
//         children: [
//           CircleAvatar(
//             radius: 20,
//             backgroundColor: AppColors.primary,
//             backgroundImage: widget.team.avatar != null
//                 ? NetworkImage(widget.team.avatar!)
//                 : null,
//             child: widget.team.avatar == null
//                 ? Text(
//                     widget.team.name.substring(0, 1).toUpperCase(),
//                     style: AppTextStyles.bodyLarge.copyWith(
//                       color: Colors.white,
//                       fontWeight: FontWeight.bold,
//                     ),
//                   )
//                 : null,
//           ),
//           const SizedBox(width: 12),
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text(
//                   '${widget.team.name} Chat',
//                   style: AppTextStyles.bodyLarge.copyWith(
//                     fontWeight: FontWeight.bold,
//                   ),
//                 ),
//                 Text(
//                   '${widget.team.memberCount ?? 0} members',
//                   style: AppTextStyles.bodySmall.copyWith(
//                     color: AppColors.textSecondary,
//                   ),
//                 ),
//               ],
//             ),
//           ),
//           IconButton(
//             onPressed: () => _showChatOptions(),
//             icon: Icon(
//               Icons.more_vert,
//               color: AppColors.textSecondary,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
