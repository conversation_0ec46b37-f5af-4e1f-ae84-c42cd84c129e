import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../domain/entities/leave_request.dart';
import '../../validators/leave_validator.dart';
import '../cubit/leave_cubit.dart';
import '../cubit/leave_state.dart';
import 'leave_type_selector.dart';
import 'date_range_picker.dart';
import 'reason_input_field.dart';

class NewLeaveTab extends StatefulWidget {
  const NewLeaveTab({super.key});

  @override
  State<NewLeaveTab> createState() => _NewLeaveTabState();
}

class _NewLeaveTabState extends State<NewLeaveTab> {
  final _formKey = GlobalKey<FormState>();
  final _reasonController = TextEditingController();

  LeaveType _selectedType = LeaveType.annual;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return BlocListener<LeaveCubit, LeaveState>(
      listener: (context, state) {
        if (state is LeaveRequestError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.message,
            isError: true,
          );
        }

        if (state is LeaveRequestSubmitted) {
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: state.message,
            isError: false,
          );
        }
      },
      child: Padding(
        padding: responsive.padding(all: 16),
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: responsive.heightPercentage(1)),

                // Header Card
                Container(
                  width: double.infinity,
                  padding: responsive.padding(all: 10),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryBlue.withValues(alpha: 0.1),
                        AppColors.primaryGreen.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: AppColors.primaryBlue.withValues(alpha: 0.2),
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.event_note_outlined,
                        size: context.responsive.fontSize(40),
                        color: AppColors.primaryBlue,
                      ),
                      SizedBox(height: responsive.heightPercentage(1)),
                      Text(
                        context.l10n.submitLeaveRequest,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontSize: responsive.fontSize(20),
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: responsive.heightPercentage(0.5)),
                      Text(
                        context.l10n.fillLeaveDetailsToSubmit,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontSize: responsive.fontSize(14),
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                SizedBox(height: responsive.heightPercentage(3)),

                // Leave Type Section
                LeaveTypeSelector(
                  selectedType: _selectedType,
                  onTypeChanged: (type) {
                    setState(() {
                      _selectedType = type;
                    });
                  },
                ),

                SizedBox(height: responsive.heightPercentage(3)),

                // Date Range Section
                DateRangePicker(
                  startDate: _startDate,
                  endDate: _endDate,
                  onStartDateChanged: (date) {
                    setState(() {
                      _startDate = date;
                      if (_endDate != null && _endDate!.isBefore(date)) {
                        _endDate = null;
                      }
                    });
                  },
                  onEndDateChanged: (date) {
                    setState(() {
                      _endDate = date;
                    });
                  },
                ),

                SizedBox(height: responsive.heightPercentage(3)),

                // Reason Section
                ReasonInputField(
                  controller: _reasonController,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return context.l10n.reasonRequired;
                    }
                    if (value.trim().length < 10) {
                      return context.l10n.reasonMinLength;
                    }
                    if (value.trim().length > 500) {
                      return context.l10n.reasonMaxLength;
                    }
                    return null;
                  },
                ),

                SizedBox(height: responsive.heightPercentage(4)),

                // Submit Button
                BlocBuilder<LeaveCubit, LeaveState>(
                  builder: (context, state) {
                    final isSubmitting = state is LeaveRequestSubmitting;

                    return SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: isSubmitting ? null : _submitRequest,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: Colors.white,
                          padding: responsive.padding(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: isSubmitting
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: responsive.widthPercentage(2),
                                  ),
                                  Text(
                                    context.l10n.submitting,
                                    style: theme.textTheme.labelLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              )
                            : Text(
                                context.l10n.submitRequest,
                                style: theme.textTheme.labelLarge?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                  fontSize: responsive.fontSize(16),
                                ),
                              ),
                      ),
                    );
                  },
                ),

                SizedBox(height: responsive.heightPercentage(2)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _submitRequest() {
    if (_formKey.currentState!.validate() &&
        _startDate != null &&
        _endDate != null) {
      
      // Additional validation using LeaveValidator
      final validationResult = LeaveValidator.validateLeaveRequest(
        type: _selectedType,
        startDate: _startDate!,
        endDate: _endDate!,
        reason: _reasonController.text.trim(),
        l10n: context.l10n,
      );

      if (!validationResult.isValid) {
        showTopSnackBar(
          context,
          title: context.l10n.error,
          message: validationResult.firstError,
          isError: true,
        );
        return;
      }

      context.read<LeaveCubit>().submitLeaveRequest(
        type: _selectedType,
        startDate: _startDate!,
        endDate: _endDate!,
        reason: _reasonController.text.trim(),
        l10n: context.l10n,
      );

      // Clear form after submission
      _clearForm();
    } else {
      String errorMessage = context.l10n.pleaseFillAllFields;
      if (_startDate == null) {
        errorMessage = context.l10n.pleaseSelectStartDate;
      } else if (_endDate == null) {
        errorMessage = context.l10n.pleaseSelectEndDate;
      }

      showTopSnackBar(
        context,
        title: context.l10n.error,
        message: errorMessage,
        isError: true,
      );
    }
  }

  void _clearForm() {
    _reasonController.clear();
    setState(() {
      _selectedType = LeaveType.annual;
      _startDate = null;
      _endDate = null;
    });
  }
}
