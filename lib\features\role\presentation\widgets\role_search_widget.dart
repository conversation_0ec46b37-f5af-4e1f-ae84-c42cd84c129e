import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/role_cubit.dart';

class RoleSearchWidget extends StatefulWidget {
  final TextEditingController searchController;
  final VoidCallback? onSearchChanged;

  const RoleSearchWidget({
    super.key,
    required this.searchController,
    this.onSearchChanged,
  });

  @override
  State<RoleSearchWidget> createState() => _RoleSearchWidgetState();
}

class _RoleSearchWidgetState extends State<RoleSearchWidget> {
  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;

    return Container(
      padding: responsive.padding(all: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: widget.searchController,
        decoration: InputDecoration(
          hintText: 'Search roles by name...',
          hintStyle: AppTextStyle.regular(
            context,
            size: 14,
            color: Colors.grey[500],
          ),
          prefixIcon: Icon(
            Iconsax.search_normal,
            color: Colors.grey[600],
            size: responsive.scaleRadius(20),
          ),
          suffixIcon: widget.searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    widget.searchController.clear();
                    context.read<RoleCubit>().refreshRoles();
                    if (widget.onSearchChanged != null) {
                      widget.onSearchChanged!();
                    }
                  },
                  icon: Icon(
                    Iconsax.close_circle,
                    color: Colors.grey[600],
                    size: responsive.scaleRadius(18),
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              responsive.defaultRadius,
            ),
            borderSide: BorderSide(
              color: Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              responsive.defaultRadius,
            ),
            borderSide: BorderSide(
              color: Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(
              responsive.defaultRadius,
            ),
            borderSide: BorderSide(
              color: theme.primaryColor,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Colors.grey.withValues(alpha: 0.05),
          contentPadding: responsive.padding(
            horizontal: 16,
            vertical: 14,
          ),
        ),
        style: AppTextStyle.regular(context, size: 14),
        onChanged: (value) {
          if (widget.onSearchChanged != null) {
            widget.onSearchChanged!();
          }
          // Debounce search
          Future.delayed(const Duration(milliseconds: 500), () {
            if (mounted && widget.searchController.text == value) {
              context.read<RoleCubit>().refreshRoles(
                search: value.trim().isEmpty ? null : value.trim(),
              );
            }
          });
        },
      ),
    );
  }
}
