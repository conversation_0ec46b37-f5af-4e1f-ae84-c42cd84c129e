import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';

class AuthRedirectRow extends StatelessWidget {
  final String questionText;
  final String actionText;
  final VoidCallback onPressed;

  const AuthRedirectRow({
    super.key,
    required this.questionText,
    required this.actionText,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(questionText, style: context.lightTheme.textTheme.bodyLarge),
        TextButton(
          onPressed: onPressed,
          child: Text(
            actionText,
            style: context.lightTheme.textTheme.bodyLarge,
          ),
        ),
      ],
    );
  }
}
