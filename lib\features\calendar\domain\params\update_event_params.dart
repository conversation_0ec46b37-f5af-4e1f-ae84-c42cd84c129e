import 'package:equatable/equatable.dart';

import '../entities/calendar_event.dart';


class UpdateEventParams extends Equatable {

  final String? eventId;
  final String? title;
  final String? description;
  final DateTime? startTime;
  final DateTime? endTime;
  final bool? isAllDay;
  final CalendarEventType? type;
  final String? color;
  final String? location;
  final List<String>? attendees;
  final bool? isRecurring;
  final String? recurrenceRule;

  const UpdateEventParams({
    this.eventId,
    this.title,
    this.description,
    this.startTime,
    this.endTime,
    this.isAllDay,
    this.type,
    this.color,
    this.location,
    this.attendees,
    this.isRecurring,
    this.recurrenceRule,
  });


  Map<String, dynamic> toJson() {
    // Chúng ta chỉ thêm các trường vào Map NẾU chúng không bị null
    return {
      if (eventId != null) 'eventId': eventId,
      if (title != null) 'title': title,
      if (description != null) 'description': description,
      if (startTime != null) 'startTime': startTime!.toIso8601String(),
      if (endTime != null) 'endTime': endTime!.toIso8601String(),
      if (isAllDay != null) 'isAllDay': isAllDay,
      if (type != null) 'type': type!.name,
      if (color != null) 'color': color,
      if (location != null) 'location': location,
      if (attendees != null) 'attendees': attendees,
      if (isRecurring != null) 'isRecurring': isRecurring,
      if (recurrenceRule != null) 'recurrenceRule': recurrenceRule,
    };
  }

  @override
  List<Object?> get props => [
    eventId,
    title,
    description,
    startTime,
    endTime,
    isAllDay,
    type,
    color,
    location,
    attendees,
    isRecurring,
    recurrenceRule,
  ];
}