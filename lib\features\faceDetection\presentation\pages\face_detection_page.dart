import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../../../../injection_container.dart';
import '../../domain/usecase/check_in_usecase.dart';
import '../../domain/usecase/check_out_usecase.dart';
import '../../domain/usecase/get_today_attendance_usecase.dart';
import '../cubit/face_checkin_cubit.dart';
import '../widgets/action_buttons.dart';
import '../widgets/app_bar.dart';
import '../widgets/image_preview_card.dart';
import '../widgets/location_card.dart';
import '../widgets/status_card.dart';
import '../widgets/welcome_header.dart';
// ... các import khác ...

enum AttendanceMode { checkIn, checkOut }

class FaceDetectionPage extends StatelessWidget {
  final AttendanceMode mode;

  const FaceDetectionPage({super.key, required this.mode});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final responsive = context.responsive;

    return BlocProvider(
      create: (context) => FaceDetectionCubit(
        checkInUseCase: sl<CheckInUseCase>(),
        checkOutUseCase: sl<CheckOutUseCase>(),
        getTodayAttendanceUseCase: sl<GetTodayAttendanceUseCase>(),
        l10n: l10n, // Truyền l10n từ context
      ),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: const FaceDetectionAppBar(),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: responsive.padding(all: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const WelcomeHeader(),
                SizedBox(height: responsive.scaleHeight(30)),
                const LocationCard(),
                SizedBox(height: responsive.scaleHeight(20)),
                const ImagePreviewCard(),
                SizedBox(height: responsive.scaleHeight(30)),
                ActionButtons(),
                SizedBox(height: responsive.scaleHeight(20)),
                const StatusCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
