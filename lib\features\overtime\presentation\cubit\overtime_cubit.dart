import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/usecases/usecase.dart';
import '../../domain/entities/overtime_request_entity.dart';
import '../../domain/usecases/get_approvers.dart';
import '../../domain/usecases/get_overtime_history.dart';
import '../../domain/usecases/get_overtime_summary.dart';
import '../../domain/usecases/submit_overtime_request.dart';
import 'overtime_state.dart';

class OvertimeCubit extends Cubit<OvertimeState> {
  final GetOvertimeSummary getOvertimeSummary;
  final GetOvertimeHistory getOvertimeHistory;
  final GetApprovers getApprovers;
  final SubmitOvertimeRequest submitOvertimeRequest;

  OvertimeCubit({
    required this.getOvertimeSummary,
    required this.getOvertimeHistory,
    required this.getApprovers,
    required this.submitOvertimeRequest,
  }) : super(const OvertimeState());

  Future<void> loadInitialData() async {
    emit(state.copyWith(status: OvertimeStateStatus.loading));

    try {
      // Load summary and initial history in parallel
      final summaryResult = await getOvertimeSummary(NoParams());
      final historyResult = await getOvertimeHistory(
        const OvertimeHistoryParams(),
      );

      summaryResult.fold(
        (failure) => emit(
          state.copyWith(
            status: OvertimeStateStatus.error,
            errorMessage: failure.message,
          ),
        ),
        (summary) {
          historyResult.fold(
            (failure) => emit(
              state.copyWith(
                status: OvertimeStateStatus.error,
                errorMessage: failure.message,
              ),
            ),
            (history) => emit(
              state.copyWith(
                status: OvertimeStateStatus.success,
                summary: summary,
                history: history,
                hasMoreData: history.length >= 10,
              ),
            ),
          );
        },
      );
    } catch (e) {
      emit(
        state.copyWith(
          status: OvertimeStateStatus.error,
          errorMessage: 'An unexpected error occurred',
        ),
      );
    }
  }

  Future<void> refreshData() async {
    emit(
      state.copyWith(
        currentPage: 1,
        hasMoreData: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
      ),
    );
    await loadInitialData();
  }

  Future<void> loadMoreHistory() async {
    if (state.isLoadingHistory || !state.hasMoreData) return;

    emit(state.copyWith(isLoadingHistory: true));

    final result = await getOvertimeHistory(
      OvertimeHistoryParams(
        page: state.currentPage + 1,
        status: state.selectedFilter,
      ),
    );

    result.fold(
      (failure) => emit(
        state.copyWith(isLoadingHistory: false, errorMessage: failure.message),
      ),
      (newHistory) {
        final updatedHistory = List<OvertimeRequestEntity>.from(state.history)
          ..addAll(newHistory);

        emit(
          state.copyWith(
            isLoadingHistory: false,
            history: updatedHistory,
            currentPage: state.currentPage + 1,
            hasMoreData: newHistory.length >= 10,
          ),
        );
      },
    );
  }

  Future<void> filterHistory(OvertimeStatus? status) async {
    emit(
      state.copyWith(
        selectedFilter: status,
        isLoadingHistory: true,
        currentPage: 1,
        hasMoreData: true,
      ),
    );

    final result = await getOvertimeHistory(
      OvertimeHistoryParams(page: 1, status: status),
    );

    result.fold(
      (failure) => emit(
        state.copyWith(isLoadingHistory: false, errorMessage: failure.message),
      ),
      (history) => emit(
        state.copyWith(
          isLoadingHistory: false,
          history: history,
          hasMoreData: history.length >= 10,
        ),
      ),
    );
  }

  Future<void> submitRequest({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
  }) async {
    emit(
      state.copyWith(
        isSubmitting: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
      ),
    );

    final result = await submitOvertimeRequest(
      SubmitOvertimeParams(
        date: date,
        startTime: startTime,
        endTime: endTime,
        reason: reason,
        type: type,
      ),
    );

    result.fold(
      (failure) => emit(
        state.copyWith(isSubmitting: false, errorMessage: failure.message),
      ),
      (request) {
        // Add new request to the beginning of history
        final updatedHistory = [request, ...state.history];

        emit(
          state.copyWith(
            isSubmitting: false,
            history: updatedHistory,
            successMessage: 'Overtime request submitted successfully',
          ),
        );

        // Refresh summary to get updated data
        _refreshSummary();
      },
    );
  }

  Future<void> _refreshSummary() async {
    final result = await getOvertimeSummary(NoParams());
    result.fold(
      (failure) {}, // Ignore error for summary refresh
      (summary) => emit(state.copyWith(summary: summary)),
    );
  }

  Future<void> loadApprovers() async {
    print(
      '🔍 [CUBIT] loadApprovers called, current approvers count: ${state.approvers.length}',
    );
    if (state.approvers.isNotEmpty) {
      print('🔍 [CUBIT] Approvers already loaded, skipping...');
      return; // Already loaded
    }

    print('🔍 [CUBIT] Setting isLoadingApprovers to true');
    emit(state.copyWith(isLoadingApprovers: true));

    print('🔍 [CUBIT] Calling getApprovers use case...');
    final result = await getApprovers(NoParams());

    result.fold(
      (failure) {
        print('❌ [CUBIT] Failed to load approvers: ${failure.message}');
        emit(
          state.copyWith(
            isLoadingApprovers: false,
            errorMessage: 'Failed to load approvers: ${failure.message}',
          ),
        );
      },
      (approvers) {
        print('✅ [CUBIT] Successfully loaded ${approvers.length} approvers');
        emit(state.copyWith(isLoadingApprovers: false, approvers: approvers));
      },
    );
  }

  void selectApprover(ApproverEntity? approver) {
    emit(state.copyWith(selectedApprover: approver));
  }

  void clearSelectedApprover() {
    emit(state.copyWith(clearSelectedApprover: true));
  }

  Future<void> submitRequestWithApprover({
    required DateTime date,
    required DateTime startTime,
    required DateTime endTime,
    required String reason,
    required OvertimeType type,
    ApproverEntity? approver,
  }) async {
    emit(
      state.copyWith(
        isSubmitting: true,
        clearErrorMessage: true,
        clearSuccessMessage: true,
      ),
    );

    final result = await submitOvertimeRequest(
      SubmitOvertimeParams(
        date: date,
        startTime: startTime,
        endTime: endTime,
        reason: reason,
        type: type,
        approverId: approver?.id,
      ),
    );

    result.fold(
      (failure) => emit(
        state.copyWith(isSubmitting: false, errorMessage: failure.message),
      ),
      (request) {
        // Add new request to the beginning of history
        final updatedHistory = [request, ...state.history];

        emit(
          state.copyWith(
            isSubmitting: false,
            history: updatedHistory,
            successMessage: 'Overtime request submitted successfully',
            clearSelectedApprover: true,
          ),
        );

        // Refresh summary to get updated data
        _refreshSummary();
      },
    );
  }

  void clearMessages() {
    emit(state.copyWith(clearErrorMessage: true, clearSuccessMessage: true));
  }
}
