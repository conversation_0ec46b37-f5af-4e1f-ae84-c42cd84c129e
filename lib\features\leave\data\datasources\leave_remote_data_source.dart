import 'package:dio/dio.dart';
import 'package:golderhr/core/logger/app_logger.dart';

import '../../../../core/error/exceptions.dart';
import '../../../../core/network/dio_client.dart';
import '../models/approver_model.dart';
import '../models/leave_policy_model.dart';
import '../models/leave_request_model.dart';
import '../models/leave_summary_model.dart';

abstract class LeaveRemoteDataSource {
  Future<LeaveSummaryModel> getLeaveSummary();
  Future<List<LeaveRequestModel>> getLeaveHistory({
    int page = 1,
    int limit = 10,
    String? status,
  });
  Future<LeaveRequestModel> submitLeaveRequest(LeaveRequestModel request);
  Future<LeaveRequestModel> updateLeaveRequest(String id, LeaveRequestModel request);
  Future<void> cancelLeaveRequest(String id);
  Future<LeaveRequestModel> getLeaveRequestById(String id);
  Future<List<ApproverModel>> getApprovers();
  Future<List<LeavePolicyModel>> getLeavePolicies();
}

class LeaveRemoteDataSourceImpl implements LeaveRemoteDataSource {
  final DioClient dioClient;

  LeaveRemoteDataSourceImpl({required this.dioClient});

  // Helper method for API calls
  Future<T> _getFromApi<T>({
    required String endpoint,
    required T Function(dynamic json) fromJson,
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final Response response = await dioClient.get(
        endpoint,
        queryParameters: queryParameters,
      );
      final responseData = response.data['data'];
      return fromJson(responseData);
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  Future<T> _postToApi<T>({
    required String endpoint,
    required T Function(dynamic json) fromJson,
    Map<String, dynamic>? data,
  }) async {
    try {
      final Response response = await dioClient.post(
        endpoint,
        data: data,
      );
      final responseData = response.data['data'];
      return fromJson(responseData);
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  Future<T> _putToApi<T>({
    required String endpoint,
    required T Function(dynamic json) fromJson,
    Map<String, dynamic>? data,
  }) async {
    try {
      final Response response = await dioClient.put(
        endpoint,
        data: data,
      );
      final responseData = response.data['data'];
      return fromJson(responseData);
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  Future<void> _deleteFromApi({
    required String endpoint,
  }) async {
    try {
      await dioClient.delete(endpoint);
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  @override
  Future<LeaveSummaryModel> getLeaveSummary() {
    return _getFromApi(
      endpoint: '/api/leave/summary',
      fromJson: (json) => LeaveSummaryModel.fromJson(json),
    );
  }

  @override
  Future<List<LeaveRequestModel>> getLeaveHistory({
    int page = 1,
    int limit = 10,
    String? status,
  }) async {
    final queryParams = <String, dynamic>{
      'page': page,
      'limit': limit,
    };
    
    if (status != null && status.isNotEmpty) {
      queryParams['status'] = status;
    }

    try {
      final Response response = await dioClient.get(
        '/api/leave/history',
        queryParameters: queryParams,
      );
      
      final List<dynamic> data = response.data['data'];
      return data.map((json) => LeaveRequestModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  @override
  Future<LeaveRequestModel> submitLeaveRequest(LeaveRequestModel request) {
    AppLogger.debug("this is test $request");
    return _postToApi(
      endpoint: '/api/leave/submit',
      data: request.toCreateRequestJson(),
      fromJson: (json) => LeaveRequestModel.fromJson(json),
    );
  }

  @override
  Future<LeaveRequestModel> updateLeaveRequest(String id, LeaveRequestModel request) {
    return _putToApi(
      endpoint: '/api/leave/$id',
      data: request.toCreateRequestJson(),
      fromJson: (json) => LeaveRequestModel.fromJson(json),
    );
  }

  @override
  Future<void> cancelLeaveRequest(String id) {
    return _deleteFromApi(
      endpoint: '/api/leave/$id',
    );
  }

  @override
  Future<LeaveRequestModel> getLeaveRequestById(String id) {
    return _getFromApi(
      endpoint: '/api/leave/$id',
      fromJson: (json) => LeaveRequestModel.fromJson(json),
    );
  }

  @override
  Future<List<ApproverModel>> getApprovers() async {
    try {
      final Response response = await dioClient.get('/api/leave/approvers');
      final List<dynamic> data = response.data['data'];
      return data.map((json) => ApproverModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }

  @override
  Future<List<LeavePolicyModel>> getLeavePolicies() async {
    try {
      final Response response = await dioClient.get('/api/leave/policies');
      final List<dynamic> data = response.data['data'];
      return data.map((json) => LeavePolicyModel.fromJson(json)).toList();
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data['message'] ?? 'An unknown server error occurred.';
      throw ServerException(errorMessage);
    } catch (e) {
      throw NetworkException('A network error occurred: ${e.toString()}');
    }
  }
}
