import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:iconsax/iconsax.dart';

class LocationWidget extends StatefulWidget {
  final Position? initialLocation;
  final Function(Position?)? onLocationChanged;
  final bool showAccuracy;
  final bool autoGetLocation;
  final EdgeInsets? padding;
  final TextStyle? addressStyle;
  final TextStyle? accuracyStyle;
  final Color? iconColor;

  const LocationWidget({
    super.key,
    this.initialLocation,
    this.onLocationChanged,
    this.showAccuracy = true,
    this.autoGetLocation = true,
    this.padding,
    this.addressStyle,
    this.accuracyStyle,
    this.iconColor,
  });

  @override
  State<LocationWidget> createState() => _LocationWidgetState();
}

class _LocationWidgetState extends State<LocationWidget> {
  Position? _currentLocation;
  String _currentAddress = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _currentLocation = widget.initialLocation;
      _getAddressFromLocation(widget.initialLocation!);
    } else if (widget.autoGetLocation) {
      _getCurrentLocationAndAddress();
    }
  }

  Future<void> _getCurrentLocationAndAddress() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _currentAddress = 'Đang lấy vị trí...';
    });

    try {
      // Kiểm tra dịch vụ vị trí có bật không
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _currentAddress = 'Dịch vụ vị trí đang tắt. Vui lòng bật GPS và thử lại.';
          _isLoading = false;
        });
        return;
      }

      // Kiểm tra permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() {
            _currentAddress = 'Quyền truy cập vị trí bị từ chối';
            _isLoading = false;
          });
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() {
          _currentAddress = 'Quyền truy cập vị trí bị từ chối vĩnh viễn';
          _isLoading = false;
        });
        return;
      }

      // Lấy vị trí hiện tại với timeout dài hơn
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
          timeLimit: Duration(seconds: 60),
        ),
      );

      // Cập nhật location và lấy địa chỉ
      _currentLocation = position;
      await _getAddressFromLocation(position);
      
      // Gọi callback nếu có
      if (widget.onLocationChanged != null) {
        widget.onLocationChanged!(_currentLocation);
      }
    } catch (e) {
      AppLogger.error('Error getting current location: $e');
      String errorMessage = 'Lỗi khi lấy vị trí hiện tại';
      
      // Xử lý chi tiết hơn các loại lỗi
      if (e.toString().contains('PERMISSION_DENIED')) {
        errorMessage = 'Quyền truy cập vị trí bị từ chối';
      } else if (e.toString().contains('PERMISSION_DENIED_NEVER_ASK')) {
        errorMessage = 'Quyền truy cập vị trí bị từ chối vĩnh viễn';
      } else if (e.toString().contains('LOCATION_DISABLED')) {
        errorMessage = 'Dịch vụ vị trí đang tắt';
      } else if (e.toString().contains('io_error')) {
        errorMessage = 'Lỗi kết nối GPS. Vui lòng kiểm tra GPS và thử lại';
      } else if (e.toString().contains('TIMEOUT')) {
        errorMessage = 'Hết thời gian chờ lấy vị trí. Vui lòng thử lại';
      }
      
      setState(() {
        _currentAddress = errorMessage;
        _isLoading = false;
      });
    }
  }

  Future<void> _getAddressFromLocation(Position location) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(
        location.latitude,
        location.longitude,
      );

      if (placemarks.isNotEmpty) {
        final place = placemarks.first;
        final address = [
          place.street,
          place.subLocality,
          place.locality,
          place.administrativeArea,
          place.country,
        ].where((part) => part != null && part.isNotEmpty).join(', ');

        setState(() {
          _currentAddress = address.isNotEmpty ? address : 'Không xác định được địa chỉ';
          _isLoading = false;
        });
      } else {
        setState(() {
          _currentAddress = 'Không xác định được địa chỉ';
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('Error getting address: $e');
      String errorMessage = 'Lỗi khi lấy địa chỉ';
      
      // Xử lý chi tiết hơn các loại lỗi geocoding
      if (e.toString().contains('io_error')) {
        errorMessage = 'Lỗi kết nối mạng khi lấy địa chỉ';
      } else if (e.toString().contains('NETWORK_ERROR')) {
        errorMessage = 'Không có kết nối mạng để lấy địa chỉ';
      } else if (e.toString().contains('TIMEOUT')) {
        errorMessage = 'Hết thời gian chờ lấy địa chỉ';
      }
      
      setState(() {
        _currentAddress = errorMessage;
        _isLoading = false;
      });
    }
  }

  void refreshLocation() {
    _getCurrentLocationAndAddress();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive(context);
    
    return Container(
      padding: widget.padding ?? const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Iconsax.location,
                size: responsive.fontSize(16),
                color: widget.iconColor ?? Colors.blue.shade600,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Vị trí hiện tại',
                  style: TextStyle(
                    fontSize: responsive.fontSize(14),
                    fontWeight: FontWeight.w500,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
              if (!_isLoading)
                IconButton(
                  onPressed: refreshLocation,
                  icon: Icon(
                    Iconsax.refresh,
                    size: responsive.fontSize(16),
                    color: Colors.blue.shade600,
                  ),
                  tooltip: 'Làm mới vị trí',
                ),
              if (_isLoading)
                SizedBox(
                  width: responsive.fontSize(16),
                  height: responsive.fontSize(16),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
                  ),
                ),
            ],
          ),
          if (_currentLocation != null) ...[
            Text(
              _currentAddress.isNotEmpty ? _currentAddress : 'Đang lấy địa chỉ...',
              style: widget.addressStyle ?? TextStyle(
                fontSize: responsive.fontSize(13),
                color: Colors.blue.shade700,
                height: 1.4,
              ),
            ),

          ] else ...[
            Text(
              _currentAddress.isNotEmpty ? _currentAddress : 'Đang lấy vị trí...',
              style: widget.addressStyle ?? TextStyle(
                fontSize: responsive.fontSize(13),
                color: _currentAddress.contains('Lỗi') || _currentAddress.contains('từ chối')
                    ? Colors.red.shade600
                    : Colors.blue.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
