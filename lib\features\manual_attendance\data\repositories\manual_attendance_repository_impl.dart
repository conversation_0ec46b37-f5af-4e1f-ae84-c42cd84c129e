import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/manual_attendance_entity.dart';
import '../../domain/repositories/manual_attendance_repository.dart';
import '../datasources/manual_attendance_remote_data_source.dart';

class ManualAttendanceRepositoryImpl implements ManualAttendanceRepository {
  final ManualAttendanceRemoteDataSource remoteDataSource;

  ManualAttendanceRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<ManualAttendanceEntity>>> getManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final requests = await remoteDataSource.getManualAttendanceRequests(
        page: page,
        limit: limit,
        status: status,
        userId: userId,
        startDate: startDate,
        endDate: endDate,
      );
      return Right(requests);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, ManualAttendanceEntity>> reviewManualAttendance({
    required String id,
    required ManualAttendanceStatus status,
    String? adminNote,
  }) async {
    try {
      final result = await remoteDataSource.reviewManualAttendance(
        id: id,
        status: status,
        adminNote: adminNote,
      );
      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('An unexpected error occurred: $e'));
    }
  }

  @override
  Future<Either<Failure, List<ManualAttendanceEntity>>> getMyManualAttendanceRequests({
    int page = 1,
    int limit = 10,
    ManualAttendanceStatus? status,
  }) async {
    try {
      final requests = await remoteDataSource.getMyManualAttendanceRequests(
        page: page,
        limit: limit,
        status: status,
      );
      return Right(requests);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('An unexpected error occurred: $e'));
    }
  }
}
