// lib/features/attendance/domain/entities/attendance_history.dart
import 'package:equatable/equatable.dart';

class AttendanceHistoryItem extends Equatable {
  final String id;
  final String date; // vd: "June 24"
  final String checkIn; // vd: "09:00 AM"
  final String checkOut; // vd: "05:00 PM"
  final String totalHours; // vd: "8h 0m"

  const AttendanceHistoryItem({
    required this.id,
    required this.date,
    required this.checkIn,
    required this.checkOut,
    required this.totalHours,
  });

  @override
  List<Object?> get props => [id, date, checkIn, checkOut, totalHours];
}

// Entity cho kết quả phân trang
class PaginatedAttendanceHistory extends Equatable {
  final List<AttendanceHistoryItem> history;
  final int currentPage;
  final int totalPages;

  const PaginatedAttendanceHistory({
    required this.history,
    required this.currentPage,
    required this.totalPages,
  });

  @override
  List<Object?> get props => [history, currentPage, totalPages];
}
