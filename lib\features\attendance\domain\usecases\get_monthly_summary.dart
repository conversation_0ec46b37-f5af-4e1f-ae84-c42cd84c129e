// lib/features/attendance/domain/usecases/get_monthly_summary.dart
import 'package:dartz/dartz.dart';
import 'package:golderhr/features/attendance/domain/entities/monthly_summary.dart';
import 'package:golderhr/features/attendance/domain/repositories/attendance_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetMonthlySummary implements UseCase<MonthlySummary, NoParams> {
  final AttendanceRepositoryV1 repository;

  GetMonthlySummary(this.repository);

  @override
  Future<Either<Failure, MonthlySummary>> call(NoParams params) async {
    return await repository.getMonthlySummary();
  }
}
