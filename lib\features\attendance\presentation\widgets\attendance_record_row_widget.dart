import 'package:flutter/material.dart';

import '../../../../core/responsive/responsive.dart';

class AttendanceRecordRowWidget extends StatelessWidget {
  final String date;
  final String checkIn;
  final String checkOut;
  final String total;
  final bool isToday;

  const AttendanceRecordRowWidget({
    super.key,
    required this.date,
    required this.checkIn,
    required this.checkOut,
    required this.total,
    this.isToday = false,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    const primaryColor = Color(0xFF4A56E2);
    final dateStyle = TextStyle(
      fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
      color: isToday ? primaryColor : Colors.black87,
      fontSize: 16,
    );
    final valueStyle = TextStyle(color: Colors.grey[800], fontSize: 15);

    return Padding(
      padding: EdgeInsets.only(
        left: responsive.widthPercentage(1),
        right: responsive.widthPercentage(1),
        top: isToday ? 2.0 : 8.0,
        bottom: 8.0,
      ),
      child: Row(
        children: [
          Expanded(flex: 3, child: Text(date, style: dateStyle)),
          Expanded(
            flex: 2,
            child: Text(
              checkIn,
              style: valueStyle,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              checkOut,
              style: valueStyle,
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(total, style: valueStyle, textAlign: TextAlign.right),
          ),
        ],
      ),
    );
  }
}
