import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../cubit/language_cubit.dart';

class LanguageSelector extends StatelessWidget {
  const LanguageSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return BlocBuilder<LanguageCubit, String>(
      builder: (context, currentLanguage) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildLanguageButton(
              'VI',
              '🇻🇳',
              'vi',
              responsive,
              currentLanguage,
              context,
            ),
            SizedBox(width: responsive.scaleWidth(16)),
            _buildLanguageButton(
              'EN',
              '🇺🇸',
              'en',
              responsive,
              currentLanguage,
              context,
            ),
          ],
        );
      },
    );
  }

  Widget _buildLanguageButton(
    String text,
    String flag,
    String languageCode,
    Responsive responsive,
    String currentLanguage,
    BuildContext context,
  ) {
    final isActive = currentLanguage == languageCode;

    return GestureDetector(
      onTap: () {
        context.read<LanguageCubit>().changeLanguage(languageCode);

        // Nếu bạn dùng localization package như easy_localization hoặc flutter_localizations:
        // context.setLocale(Locale(languageCode));
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(
          horizontal: responsive.scaleWidth(12),
          vertical: responsive.scaleHeight(6),
        ),
        decoration: BoxDecoration(
          color: isActive
              ? Colors.white.withAlpha((0.2 * 255).round())
              : Colors.white.withAlpha((0.1 * 255).round()),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isActive
                ? Colors.white.withAlpha((0.4 * 255).round())
                : Colors.white.withAlpha((0.2 * 255).round()),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              flag,
              style: context.lightTheme.textTheme.bodyLarge!.copyWith(
                color: AppColors.white,
              ),
            ),
            SizedBox(width: responsive.scaleWidth(6)),
            Text(
              text,
              style: context.lightTheme.textTheme.bodyLarge!.copyWith(
                color: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
