import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/usecases/usecase.dart';
import '../../data/models/department_model.dart';
import '../../domain/entities/department_entity.dart';
import '../../domain/usecases/department_usecases.dart';
import 'department_state.dart';

// Department Cubit
class DepartmentCubit extends Cubit<DepartmentState> {
  final GetAllDepartments getAllDepartments;
  final GetDepartmentById getDepartmentById;
  final CreateDepartment createDepartment;
  final UpdateDepartment updateDepartment;
  final DeleteDepartment deleteDepartment;
  final RestoreDepartment restoreDepartment;
  final ToggleDepartmentStatus toggleDepartmentStatus;
  final GetDepartmentsForDropdown getDepartmentsForDropdown;
  final GetDepartmentHierarchy getDepartmentHierarchy;

  DepartmentCubit({
    required this.getAllDepartments,
    required this.getDepartmentById,
    required this.createDepartment,
    required this.updateDepartment,
    required this.deleteDepartment,
    required this.restoreDepartment,
    required this.toggleDepartmentStatus,
    required this.getDepartmentsForDropdown,
    required this.getDepartmentHierarchy,
  }) : super(const DepartmentState());

  // Get all departments with pagination
  Future<void> loadDepartments({
    int page = 1,
    int limit = 10,
    String? search,
    bool includeDeleted = false,
    bool refresh = false,
  }) async {
    // Logic này vẫn giữ nguyên, nó đã đúng
    if (refresh) {
      emit(
        state.copyWith(
          isLoading: true,
          error: null,
          currentPage: 1,
          departments: [],
          hasReachedMax: false,
          includeDeleted: includeDeleted,
        ),
      );
    } else if (state.isLoading || state.hasReachedMax && !refresh) {
      return; // Không load thêm nếu đang load hoặc đã hết dữ liệu
    } else {
      emit(state.copyWith(isLoading: true, error: null));
    }


    final result = await getAllDepartments(
      GetAllDepartmentsParams(
        page: page,
        limit: limit,
        search: search,
        includeDeleted: includeDeleted,
      ),
    );

    result.fold(
          (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
          (departmentListResult) {
        final newDepartments = page == 1
            ? departmentListResult.departments.cast<DepartmentEntity>()
            : [
          ...state.departments,
          ...departmentListResult.departments.cast<DepartmentEntity>(),
        ];

        emit(
          state.copyWith(
            isLoading: false,
            departments: newDepartments,
            departmentListResult: departmentListResult,
            currentPage: page,
            hasReachedMax: departmentListResult.departments.length < limit,
            error: null,
          ),
        );
      },
    );
  }

  // Load more departments for pagination
  Future<void> loadMoreDepartments({String? search}) async {
    if (state.hasReachedMax || state.isLoading) return;

    await loadDepartments(
      page: state.currentPage + 1,
      search: search,
      includeDeleted: state.includeDeleted,
    );
  }

  // Get department by ID
  Future<void> loadDepartmentById(String departmentId) async {
    emit(state.copyWith(isLoading: true, error: null));
    final result = await getDepartmentById(departmentId);
    result.fold(
          (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
          (department) => emit(
        state.copyWith(
          isLoading: false,
          selectedDepartment: department,
          error: null,
        ),
      ),
    );
  }

  // [MODIFIED] Create new department
  Future<void> createNewDepartment({
    required String name,
    String? description,
    String? code,
    String? parentId,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));
    final result = await createDepartment(
      CreateDepartmentParams(
        name: name,
        description: description,
        code: code,
        parentId: parentId,
      ),
    );

    result.fold(
          (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
          (department) {
        emit(
          state.copyWith(
            isProcessing: false,
            successMessage: 'Department created successfully',
            error: null,
          ),
        );
        // Tải lại toàn bộ danh sách để đảm bảo đồng bộ
        refreshDepartments();
      },
    );
  }

  // [MODIFIED] Update department
  Future<void> updateExistingDepartment({
    required String departmentId,
    String? name,
    String? description,
    String? code,
    bool? isActive,
    bool? isDisabled,
    String? parentId,
  }) async {
    emit(state.copyWith(isProcessing: true, error: null));
    final result = await updateDepartment(
      UpdateDepartmentParams(
        departmentId: departmentId,
        name: name,
        description: description,
        code: code,
        isActive: isActive,
        isDisabled: isDisabled,
        parentId: parentId,
      ),
    );

    result.fold(
          (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
          (updatedDepartment) {
        emit(
          state.copyWith(
            isProcessing: false,
            successMessage: 'Department updated successfully',
            error: null,
          ),
        );
        // Tải lại toàn bộ danh sách để đảm bảo đồng bộ
        refreshDepartments();
      },
    );
  }

  // [MODIFIED] Delete department
  Future<void> deleteExistingDepartment(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));
    final result = await deleteDepartment(departmentId);

    result.fold(
          (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
          (_) {
        emit(
          state.copyWith(
            isProcessing: false,
            successMessage: 'Department deleted successfully',
            error: null,
          ),
        );
        // Tải lại toàn bộ danh sách để đảm bảo đồng bộ
        refreshDepartments();
      },
    );
  }

  // [MODIFIED] Restore department
  Future<void> restoreExistingDepartment(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));
    final result = await restoreDepartment(departmentId);

    result.fold(
          (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
          (restoredDepartment) {
        emit(
          state.copyWith(
            isProcessing: false,
            successMessage: 'Department restored successfully',
            error: null,
          ),
        );
        // Tải lại toàn bộ danh sách để đảm bảo đồng bộ
        refreshDepartments();
      },
    );
  }

  // [MODIFIED] Toggle department status
  Future<void> toggleExistingDepartmentStatus(String departmentId) async {
    emit(state.copyWith(isProcessing: true, error: null));
    final result = await toggleDepartmentStatus(departmentId);

    result.fold(
          (failure) =>
          emit(state.copyWith(isProcessing: false, error: failure.message)),
          (updatedDepartment) {
        emit(
          state.copyWith(
            isProcessing: false,
            successMessage: 'Department status updated successfully',
            error: null,
          ),
        );
        // Tải lại toàn bộ danh sách để đảm bảo đồng bộ
        refreshDepartments();
      },
    );
  }

  // Load departments for dropdown
  Future<void> loadDepartmentsForDropdown() async {
    final result = await getDepartmentsForDropdown(NoParams());
    result.fold(
          (failure) => emit(state.copyWith(error: failure.message)),
          (departments) =>
          emit(state.copyWith(dropdownDepartments: departments, error: null)),
    );
  }

  // Load department hierarchy
  Future<void> loadDepartmentHierarchy() async {
    emit(state.copyWith(isLoading: true, error: null));
    final result = await getDepartmentHierarchy(NoParams());
    result.fold(
          (failure) =>
          emit(state.copyWith(isLoading: false, error: failure.message)),
          (departments) => emit(
        state.copyWith(
          isLoading: false,
          hierarchyDepartments: departments,
          error: null,
        ),
      ),
    );
  }

  // Toggle include deleted filter
  void toggleIncludeDeleted() {
    final newIncludeDeleted = !state.includeDeleted;
    refreshDepartments(includeDeleted: newIncludeDeleted);
  }

  // Clear messages
  void clearMessages() {
    emit(state.copyWith(error: null, successMessage: null));
  }

  // Clear selected department
  void clearSelectedDepartment() {
    emit(state.copyWith(selectedDepartment: null));
  }

  // Refresh departments
  Future<void> refreshDepartments({
    String? search,
    bool? includeDeleted,
  }) async {
    await loadDepartments(
      page: 1,
      search: search,
      includeDeleted: includeDeleted ?? state.includeDeleted,
      refresh: true,
    );
  }
}