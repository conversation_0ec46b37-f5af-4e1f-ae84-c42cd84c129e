import 'package:flutter/material.dart';
import 'package:golderhr/core/responsive/responsive.dart';
import 'package:golderhr/shared/widgets/button_custom.dart';
import 'package:golderhr/shared/widgets/text_field_custom.dart';
import 'package:iconsax/iconsax.dart';
import '../../domain/entities/manual_attendance_entity.dart';

class ReviewManualAttendanceDialog extends StatefulWidget {
  final ManualAttendanceEntity request;
  final ManualAttendanceStatus initialStatus;
  final Function(ManualAttendanceStatus status, String? note) onReview;

  const ReviewManualAttendanceDialog({
    super.key,
    required this.request,
    required this.initialStatus,
    required this.onReview,
  });

  @override
  State<ReviewManualAttendanceDialog> createState() => _ReviewManualAttendanceDialogState();
}

class _ReviewManualAttendanceDialogState extends State<ReviewManualAttendanceDialog> {
  late ManualAttendanceStatus _selectedStatus;
  final _noteController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.initialStatus;
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: responsive.scaleWidth(400),
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _selectedStatus == ManualAttendanceStatus.approved 
                    ? Colors.green.shade50 
                    : Colors.red.shade50,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _selectedStatus == ManualAttendanceStatus.approved 
                        ? Iconsax.tick_circle 
                        : Iconsax.close_circle,
                    color: _selectedStatus == ManualAttendanceStatus.approved 
                        ? Colors.green 
                        : Colors.red,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _selectedStatus == ManualAttendanceStatus.approved 
                          ? 'Duyệt yêu cầu' 
                          : 'Từ chối yêu cầu',
                      style: TextStyle(
                        fontSize: responsive.fontSize(18),
                        fontWeight: FontWeight.bold,
                        color: _selectedStatus == ManualAttendanceStatus.approved 
                            ? Colors.green.shade800 
                            : Colors.red.shade800,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Request summary
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Thông tin yêu cầu',
                            style: TextStyle(
                              fontSize: responsive.fontSize(16),
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 12),
                          _buildInfoRow('Nhân viên:', widget.request.fullName),
                          _buildInfoRow(
                            'Loại:', 
                            widget.request.isCheckIn ? 'Chấm công vào' : 'Chấm công ra'
                          ),
                          _buildInfoRow(
                            'Thời gian:', 
                            _formatDateTime(widget.request.timestamp)
                          ),
                          _buildInfoRow('Lý do:', widget.request.reason),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Status selection
                    Text(
                      'Quyết định',
                      style: TextStyle(
                        fontSize: responsive.fontSize(16),
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatusOption(
                            ManualAttendanceStatus.approved,
                            'Duyệt',
                            Iconsax.tick_circle,
                            Colors.green,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatusOption(
                            ManualAttendanceStatus.rejected,
                            'Từ chối',
                            Iconsax.close_circle,
                            Colors.red,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // Admin note
                    Text(
                      'Ghi chú (tùy chọn)',
                      style: TextStyle(
                        fontSize: responsive.fontSize(16),
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFieldCustom(
                      controller: _noteController,
                      hintText: _selectedStatus == ManualAttendanceStatus.approved
                          ? 'Ghi chú về việc duyệt yêu cầu...'
                          : 'Lý do từ chối yêu cầu...',
                      maxLines: 3,
                      prefixIcon: Iconsax.note,
                    ),

                    const SizedBox(height: 24),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                            child: const Text('Hủy'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ButtonCustom(
                            text: _selectedStatus == ManualAttendanceStatus.approved 
                                ? 'Duyệt' 
                                : 'Từ chối',
                            onPressed: _isLoading ? null : _handleReview,
                            isLoading: _isLoading,
                            backgroundColor: _selectedStatus == ManualAttendanceStatus.approved 
                                ? Colors.green 
                                : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    final responsive = Responsive.of(context);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: responsive.fontSize(14),
                color: Colors.grey.shade600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: responsive.fontSize(14),
                color: Colors.grey.shade800,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusOption(
    ManualAttendanceStatus status,
    String label,
    IconData icon,
    Color color,
  ) {
    final responsive = Responsive.of(context);
    final isSelected = _selectedStatus == status;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedStatus = status;
        });
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color : Colors.grey.shade500,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: responsive.fontSize(14),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? color : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    // Convert UTC to Vietnam timezone (UTC+7)
    final vietnamTime = dateTime.add(const Duration(hours: 7));
    return '${vietnamTime.day}/${vietnamTime.month}/${vietnamTime.year} ${vietnamTime.hour.toString().padLeft(2, '0')}:${vietnamTime.minute.toString().padLeft(2, '0')}';
  }

  void _handleReview() {
    setState(() {
      _isLoading = true;
    });

    final note = _noteController.text.trim();
    widget.onReview(_selectedStatus, note.isEmpty ? null : note);

    Navigator.of(context).pop();
  }
}
