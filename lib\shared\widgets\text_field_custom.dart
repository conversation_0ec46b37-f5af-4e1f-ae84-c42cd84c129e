import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

class TextFieldCustom extends StatelessWidget {
  final String? hintText;
  final TextEditingController? controller;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final bool obscureText;
  final String? Function(String?)? validator;
  final VoidCallback? onSuffixIconPressed;
  final FocusNode? focusNode;
  final Color? iconColor;
  final double? iconSize;
  final double? fontSize;
  final int? maxLines;

  const TextFieldCustom({
    super.key,
    this.hintText,
    this.controller,
    this.prefixIcon,
    this.suffixIcon,
    this.obscureText = false,
    this.validator,
    this.onSuffixIconPressed,
    this.focusNode,
    this.iconColor,
    this.iconSize,
    this.fontSize,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final double iconBase = iconSize ?? 20;
    final double iconScaled = context.rf(iconBase);
    final Color finalIconColor =
        iconColor ??
        theme.inputDecorationTheme.iconColor ??
        (theme.brightness == Brightness.dark ? Colors.white70 : Colors.black54);

    final inputTextStyle = theme.textTheme.titleMedium?.copyWith(
      fontSize: context.rf(16),
    );
    final hintTextStyle = theme.textTheme.bodyMedium?.copyWith(
      fontSize: context.rf(14),
      color: theme.hintColor,
    );

    OutlineInputBorder border(Color color, double width) => OutlineInputBorder(
      borderRadius: BorderRadius.circular(context.rr(10)),
      borderSide: BorderSide(color: color, width: width),
    );

    return TextFormField(
      controller: controller,
      validator: validator,
      obscureText: obscureText,
      focusNode: focusNode,
      maxLines: obscureText ? 1 : (maxLines ?? 1),
      style: inputTextStyle,
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: hintTextStyle,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(
          vertical: context.rh(14),
          horizontal: context.rw(12),
        ),
        prefixIcon: prefixIcon != null
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: context.rw(12)),
                child: Icon(
                  prefixIcon,
                  size: iconScaled,
                  color: finalIconColor,
                ),
              )
            : null,
        suffixIcon: suffixIcon != null
            ? IconButton(
                padding: EdgeInsets.symmetric(horizontal: context.rw(12)),
                icon: Icon(suffixIcon, size: iconScaled, color: finalIconColor),
                onPressed: onSuffixIconPressed,
                splashRadius: context.rr(20),
                constraints: BoxConstraints(
                  minHeight: iconScaled + context.rh(8),
                  minWidth: iconScaled + context.rw(8),
                ),
              )
            : null,
        errorStyle: TextStyle(fontSize: context.rf(12)),
        border: border(Colors.grey.shade400, 0.8),
        enabledBorder: border(Colors.grey.shade400, 0.8),
        focusedBorder: border(theme.primaryColor, 1.2),
        errorBorder: border(theme.colorScheme.error, 1.0),
        focusedErrorBorder: border(theme.colorScheme.error, 1.2),
      ),
    );
  }
}
