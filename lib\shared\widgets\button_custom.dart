import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import '../theme/app_colors.dart';

class ButtonCustom extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double? borderRadius;
  final EdgeInsetsGeometry? padding;

  const ButtonCustom({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.width, // Cho phép widget cha override chiều rộng
    this.height, // Cho phép widget cha override chiều cao
    this.borderRadius,
    this.padding,
  });

  @override
  State<ButtonCustom> createState() => _ButtonCustomState();
}

class _ButtonCustomState extends State<ButtonCustom>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.96).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.onPressed != null && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    final bool isDisabled = widget.isLoading || widget.onPressed == null;
    final Color effectiveBackgroundColor =
        widget.backgroundColor ?? AppColors.primary;
    final Color effectiveTextColor = widget.textColor ?? Colors.white;

    // Sử dụng extension để tính toán kích thước mặc định, dễ dàng override
    final double buttonHeight = widget.height ?? context.rh(52);
    final double buttonBorderRadius = widget.borderRadius ?? context.rr(12);

    return ScaleTransition(
      scale: _scaleAnimation,
      child: GestureDetector(
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        onTap: isDisabled ? null : widget.onPressed,
        child: Container(
          width:
              widget.width ??
              double.infinity, // Mặc định chiếm hết chiều rộng parent
          height: buttonHeight,
          decoration: BoxDecoration(
            color: isDisabled ? Colors.grey.shade400 : effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(buttonBorderRadius),
            boxShadow: isDisabled
                ? null
                : [
                    BoxShadow(
                      color: effectiveBackgroundColor.withValues(alpha: 0.3),
                      blurRadius: context.rr(12),
                      offset: Offset(0, context.rr(6)),
                    ),
                  ],
          ),
          child: Material(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(buttonBorderRadius),
            child: InkWell(
              borderRadius: BorderRadius.circular(buttonBorderRadius),
              onTap: isDisabled
                  ? null
                  : widget.onPressed, // Đảm bảo InkWell cũng bị disable
              child: Padding(
                padding:
                    widget.padding ??
                    EdgeInsets.symmetric(horizontal: context.rw(16)),
                child: _buildButtonContent(context, effectiveTextColor),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context, Color textColor) {
    if (widget.isLoading) {
      return Center(
        child: SizedBox(
          width: context.rr(24), // Dùng rr cho icon/indicator size
          height: context.rr(24),
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(textColor),
            strokeWidth: 2.5,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widget.icon != null) ...[
          Icon(
            widget.icon,
            size: context.rr(22), // Kích thước icon responsive
            color: textColor,
          ),
          SizedBox(width: context.rw(8)), // Khoảng cách responsive
        ],
        // Dùng Flexible để đảm bảo text không bị tràn nếu quá dài
        Flexible(
          child: Text(
            widget.text,
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: context.rf(16), // Font size responsive
              fontWeight: FontWeight.w600,
              color: textColor,
            ),
          ),
        ),
      ],
    );
  }
}
