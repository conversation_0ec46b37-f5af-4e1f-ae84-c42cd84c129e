import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';

import '../../../../core/responsive/responsive.dart';
import '../cubit/attendance_page/attendance_state.dart';
import 'header_card_widget.dart';
import 'info_card_widget.dart';
import 'today_summary_content_widget.dart';

class TodayTabView extends StatelessWidget {
  final AttendanceLoaded state;

  const TodayTabView({super.key, required this.state});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: responsive.widthPercentage(4),
        vertical: responsive.heightPercentage(2),
      ),
      child: Column(
        children: [
          HeaderCardWidget(isCheckedIn: state.todaySummary.isCheckedIn),
          SizedBox(height: responsive.heightPercentage(5)),
          InfoCardWidget(
            title: l10n.todaysSummary,
            icon: Icons.pie_chart_outline_rounded,
            child: TodaySummaryContentWidget(todaySummary: state.todaySummary),
          ),
          SizedBox(height: responsive.heightPercentage(3)),
        ],
      ),
    );
  }
}
