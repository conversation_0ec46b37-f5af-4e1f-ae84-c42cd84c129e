import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/extensions/l10n_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';
import '../cubit/calendar_cubit.dart';
import '../utils/calendar_dialogs.dart';

class EventDetailsDialog extends StatelessWidget {
  final CalendarEvent event;

  const EventDetailsDialog({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          children: [
            _buildAppBar(context),
            Divider(height: 1, color: Colors.grey.shade300),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(20),
                children: [
                  _buildSectionHeader(context, _getEventTypeDisplayName(event.type), icon: _getEventTypeIcon(event.type)),
                  const SizedBox(height: 12),
                  _buildTitle(context),
                  const SizedBox(height: 20),
                  _buildInfoRow(context, icon: Icons.access_time, title: 'Time', content: _formatEventTime()),
                  if (event.location?.isNotEmpty == true)
                    _buildInfoRow(context, icon: Icons.location_on_outlined, title: 'Location', content: event.location!),
                  if (event.description?.isNotEmpty == true)
                    _buildInfoRow(context, icon: Icons.description_outlined, title: 'Description', content: event.description!),
                  _buildInfoRow(context, icon: Icons.person_outline, title: 'Created by', content: event.createdBy.fullname),
                  _buildInfoRow(context, icon: Icons.calendar_today_outlined, title: 'Created on', content: _formatDate(event.createdAt)),
                  if (event.isRecurring)
                    _buildInfoRow(context, icon: Icons.repeat, title: 'Recurring', content: event.recurrenceRule ?? 'Yes'),
                  if (event.attendees.isNotEmpty) _buildAttendees(context),
                ],
              ),
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            _buildActionButtons(context, l10n),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              'Event Detail',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          )
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      event.title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, {required IconData icon}) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primaryBlue),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppColors.primaryBlue,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, {required IconData icon, required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildAttendees(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.people_outline, size: 20, color: Colors.grey),
              const SizedBox(width: 8),
              Text(
                'Attendees (${event.attendees.length})',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Colors.grey,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ...event.attendees.map((attendee) => Padding(
            padding: const EdgeInsets.only(left: 28, bottom: 6),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 12,
                  backgroundColor: AppColors.primaryBlue.withOpacity(0.1),
                  child: Text(
                    attendee.fullname.isNotEmpty ? attendee.fullname[0].toUpperCase() : 'U',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(attendee.fullname),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, dynamic l10n) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                final calendarCubit = context.read<CalendarCubit>();
                Navigator.of(context).pop();
                _showEditEventDialog(context, calendarCubit);
              },
              icon: const Icon(Icons.edit_outlined),
              label: const Text('Edit'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primaryBlue,
                side: BorderSide(color: AppColors.primaryBlue),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () async {
                Navigator.of(context).pop();
                final confirm = await _showDeleteConfirmDialog(context);
                if (confirm && context.mounted) {
                  await context.read<CalendarCubit>().deleteEvent(event.id);
                }
              },
              icon: const Icon(Icons.delete_outline),
              label: const Text('Delete'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showEditEventDialog(BuildContext context, CalendarCubit calendarCubit) {
    CalendarDialogs.showAddEventDialogWithCubit(
      context,
      calendarCubit: calendarCubit,
      eventToEdit: event,
      selectedDate: DateTime(
        event.startTime.year,
        event.startTime.month,
        event.startTime.day,
      ),
    );
  }

  Future<bool> _showDeleteConfirmDialog(BuildContext context) async {
    final l10n = context.l10n;
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete "${event.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.cancel),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(l10n.delete, style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    ) ?? false;
  }

  String _formatEventTime() {
    if (event.isAllDay) return 'All day';
    final startTime = '${event.startTime.hour.toString().padLeft(2, '0')}:${event.startTime.minute.toString().padLeft(2, '0')}';
    final endTime = '${event.endTime.hour.toString().padLeft(2, '0')}:${event.endTime.minute.toString().padLeft(2, '0')}';
    if (_isSameDay(event.startTime, event.endTime)) {
      return '$startTime - $endTime';
    } else {
      return '${_formatDate(event.startTime)} $startTime - ${_formatDate(event.endTime)} $endTime';
    }
  }

  String _formatDate(DateTime date) {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun','Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return '${date.day} ${monthNames[date.month - 1]} ${date.year}';
  }

  bool _isSameDay(DateTime d1, DateTime d2) =>
      d1.year == d2.year && d1.month == d2.month && d1.day == d2.day;

  Color _parseColor(String hex) {
    try {
      if (hex.startsWith('#')) {
        return Color(int.parse(hex.substring(1), radix: 16) + 0xFF000000);
      } else if (hex.startsWith('0x')) {
        return Color(int.parse(hex));
      }
      return AppColors.primaryBlue;
    } catch (_) {
      return AppColors.primaryBlue;
    }
  }

  IconData _getEventTypeIcon(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting: return Icons.people_outline;
      case CalendarEventType.leave: return Icons.beach_access_outlined;
      case CalendarEventType.holiday: return Icons.celebration_outlined;
      case CalendarEventType.training: return Icons.school_outlined;
      case CalendarEventType.event: return Icons.event_outlined;
      case CalendarEventType.other: return Icons.more_horiz_outlined;
      case CalendarEventType.unknown: return Icons.help_outline;
    }
  }

  String _getEventTypeDisplayName(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting: return 'Meeting';
      case CalendarEventType.leave: return 'Leave';
      case CalendarEventType.holiday: return 'Holiday';
      case CalendarEventType.training: return 'Training';
      case CalendarEventType.event: return 'Event';
      case CalendarEventType.other: return 'Other';
      case CalendarEventType.unknown: return 'Unknown';
    }
  }
}
