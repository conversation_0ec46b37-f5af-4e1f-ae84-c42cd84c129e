import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:golderhr/features/attendance/domain/entities/monthly_details.dart';
import 'package:golderhr/features/attendance/domain/repositories/attendance_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetMonthlyDetails
    implements UseCase<MonthlyDetails, MonthlyDetailsParams> {
  final AttendanceRepositoryV1 repository;

  GetMonthlyDetails(this.repository);

  @override
  Future<Either<Failure, MonthlyDetails>> call(
    MonthlyDetailsParams params,
  ) async {
    return await repository.getMonthlyDetails(
      year: params.year,
      month: params.month,
    );
  }
}

class MonthlyDetailsParams extends Equatable {
  final int year;
  final int month;

  const MonthlyDetailsParams({required this.year, required this.month});

  @override
  List<Object?> get props => [year, month];
}
