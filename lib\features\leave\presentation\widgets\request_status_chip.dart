import 'package:flutter/material.dart';

enum RequestStatus { pending, approved, rejected, cancelled }

class RequestStatusChip extends StatelessWidget {
  final RequestStatus status;

  const RequestStatusChip({super.key, required this.status});

  @override
  Widget build(BuildContext context) {
    return Chip(
      label: Text(
        _getStatusText(status),
        style: TextStyle(

          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
      avatar: Icon(
        _getStatusIcon(status),
        size: 16,
    
      ),
      backgroundColor: _getStatusColor(status).withOpacity(0.2),
      labelPadding: const EdgeInsets.only(left: 2, right: 8),
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(color: Colors.transparent),
      ),
    );
  }

  String _getStatusText(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return 'Pending';
      case RequestStatus.approved:
        return 'Approved';
      case RequestStatus.rejected:
        return 'Rejected';
      case RequestStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Colors.orange.shade300;
      case RequestStatus.approved:
        return Colors.green.shade300;
      case RequestStatus.rejected:
        return Colors.red.shade300;
      case RequestStatus.cancelled:
        return Colors.grey.shade400;
    }
  }

  IconData _getStatusIcon(RequestStatus status) {
    switch (status) {
      case RequestStatus.pending:
        return Icons.schedule_rounded;
      case RequestStatus.approved:
        return Icons.check_circle_rounded;
      case RequestStatus.rejected:
        return Icons.cancel_rounded;
      case RequestStatus.cancelled:
        return Icons.block_rounded;
    }
  }
}