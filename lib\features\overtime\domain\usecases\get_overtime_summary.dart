import 'package:dartz/dartz.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/overtime_summary_entity.dart';
import '../repositories/overtime_repository.dart';

class GetOvertimeSummary implements UseCase<OvertimeSummaryEntity, NoParams> {
  final OvertimeRepository repository;

  GetOvertimeSummary(this.repository);

  @override
  Future<Either<Failure, OvertimeSummaryEntity>> call(NoParams params) async {
    return await repository.getOvertimeSummary();
  }
}
