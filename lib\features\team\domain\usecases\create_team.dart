import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/team_entity.dart';
import '../repositories/team_repository.dart';

class CreateTeam implements UseCase<TeamEntity, CreateTeamParams> {
  final TeamRepository repository;

  CreateTeam(this.repository);

  @override
  Future<Either<Failure, TeamEntity>> call(CreateTeamParams params) async {
    return await repository.createTeam(params.toJson());
  }
}

class CreateTeamParams extends Equatable {
  final String name;
  final String? description;
  final String? departmentId;
  final List<String>? memberIds;

  const CreateTeamParams({
    required this.name,
    this.description,
    this.departmentId,
    this.memberIds,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'departmentId': departmentId,
      'memberIds': memberIds,
    };
  }

  @override
  List<Object?> get props => [name, description, departmentId, memberIds];
}
