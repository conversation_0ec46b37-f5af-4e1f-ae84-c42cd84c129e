import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/theme/app_theme_helper.dart';
import '../../domain/entities/team_entity.dart';
import '../cubit/team_cubit.dart';

class TeamOverviewContent extends StatefulWidget {
  final TeamEntity team;

  const TeamOverviewContent({super.key, required this.team});

  @override
  State<TeamOverviewContent> createState() => _TeamOverviewContentState();
}

class _TeamOverviewContentState extends State<TeamOverviewContent> {
  @override
  void initState() {
    super.initState();
    // Load team stats when overview is opened
    context.read<TeamCubit>().loadTeamStats(widget.team.id);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppColors.background,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 20),

            // Quick Stats Row
            _buildQuickStatsRow(),
            const SizedBox(height: 20),

            // Main Dashboard Grid
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left Column - Performance Metrics
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildAttendanceOverview(),
                      const SizedBox(height: 16),
                      _buildLeaveOverview(),
                      const SizedBox(height: 16),
                      _buildProductivityMetrics(),
                    ],
                  ),
                ),
                const SizedBox(width: 20),
                // Right Column - Team Info & Activities
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      _buildTeamInfoCard(),
                      const SizedBox(height: 16),
                      _buildRecentActivities(),
                      const SizedBox(height: 16),
                      _buildUpcomingEvents(),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          'Team Performance Dashboard',
          style: AppTextStyles.headlineMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Row(
          children: [
            _buildHeaderButton(
              icon: Icons.analytics_outlined,
              label: 'Reports',
              onPressed: () => _showReportsDialog(),
            ),
            const SizedBox(width: 12),
            if (widget.team.canManage)
              _buildHeaderButton(
                icon: Icons.add_task,
                label: 'New Task',
                onPressed: () => _showCreateTaskDialog(),
                isPrimary: true,
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isPrimary = false,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary ? AppColors.primary : Colors.white,
        foregroundColor: isPrimary ? Colors.white : AppColors.textPrimary,
        elevation: isPrimary ? 2 : 1,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        side: isPrimary ? null : BorderSide(color: AppColors.border),
      ),
    );
  }

  Widget _buildQuickStatsRow() {
    return Row(
      children: [
        Expanded(child: _buildQuickStatCard('Present Today', '8/10', Icons.check_circle, AppColors.success)),
        const SizedBox(width: 16),
        Expanded(child: _buildQuickStatCard('On Leave', '2/10', Icons.event_busy, AppColors.warning)),
        const SizedBox(width: 16),
        Expanded(child: _buildQuickStatCard('Avg. Attendance', '92%', Icons.trending_up, AppColors.info)),
        const SizedBox(width: 16),
        Expanded(child: _buildQuickStatCard('Team Score', '8.5/10', Icons.star, AppColors.primary)),
      ],
    );
  }

  Widget _buildQuickStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: AppTextStyles.bodyLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: color,
                    ),
                  ),
                  Text(
                    title,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamInfoCard() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Team Information',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: AppColors.primary,
                  backgroundImage: widget.team.avatar != null
                      ? NetworkImage(widget.team.avatar!)
                      : null,
                  child: widget.team.avatar == null
                      ? Text(
                          widget.team.name.substring(0, 1).toUpperCase(),
                          style: AppTextStyles.bodyLarge.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.team.name,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (widget.team.description != null)
                        Text(
                          widget.team.description!,
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildInfoItem(Icons.people, '${widget.team.memberCount ?? 0} Members'),
                const SizedBox(width: 16),
                _buildInfoItem(Icons.admin_panel_settings, widget.team.userRole?.toUpperCase() ?? 'MEMBER'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 14, color: AppColors.textSecondary),
        const SizedBox(width: 4),
        Text(
          text,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildAttendanceOverview() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.access_time, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Attendance Overview',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showAttendanceDetails(),
                  child: Text('View Details', style: TextStyle(color: AppColors.primary)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Present', '8', AppColors.success),
                ),
                Expanded(
                  child: _buildMetricItem('Late', '1', AppColors.warning),
                ),
                Expanded(
                  child: _buildMetricItem('Absent', '1', AppColors.error),
                ),
                Expanded(
                  child: _buildMetricItem('Rate', '92%', AppColors.info),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: 0.92,
              backgroundColor: AppColors.border,
              valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLeaveOverview() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.event_busy, color: AppColors.warning, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Leave Management',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showLeaveDetails(),
                  child: Text('View Details', style: TextStyle(color: AppColors.primary)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Pending', '3', AppColors.warning),
                ),
                Expanded(
                  child: _buildMetricItem('Approved', '2', AppColors.success),
                ),
                Expanded(
                  child: _buildMetricItem('This Month', '5', AppColors.info),
                ),
                Expanded(
                  child: _buildMetricItem('Coverage', '90%', AppColors.primary),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildUpcomingLeaves(),
          ],
        ),
      ),
    );
  }

  Widget _buildProductivityMetrics() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppColors.success, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Productivity Metrics',
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => _showProductivityDetails(),
                  child: Text('View Details', style: TextStyle(color: AppColors.primary)),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem('Tasks', '24', AppColors.primary),
                ),
                Expanded(
                  child: _buildMetricItem('Completed', '18', AppColors.success),
                ),
                Expanded(
                  child: _buildMetricItem('In Progress', '4', AppColors.warning),
                ),
                Expanded(
                  child: _buildMetricItem('Overdue', '2', AppColors.error),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Completion Rate',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: 0.75,
                        backgroundColor: AppColors.border,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.success),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '75%',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.bodyLarge.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildUpcomingLeaves() {
    return Column(
      children: [
        _buildLeaveItem('John Doe', 'Annual Leave', 'Dec 25-27', AppColors.info),
        _buildLeaveItem('Jane Smith', 'Sick Leave', 'Dec 24', AppColors.warning),
      ],
    );
  }

  Widget _buildLeaveItem(String name, String type, String dates, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 32,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$type • $dates',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activities',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildActivityItem(
              'Task completed by John Doe',
              '2 hours ago',
              Icons.check_circle,
              AppColors.success,
            ),
            _buildActivityItem(
              'Leave request submitted',
              '4 hours ago',
              Icons.event_busy,
              AppColors.warning,
            ),
            _buildActivityItem(
              'New team member added',
              '1 day ago',
              Icons.person_add,
              AppColors.info,
            ),
            const SizedBox(height: 8),
            Center(
              child: TextButton(
                onPressed: () => _showAllActivities(),
                child: Text('View All Activities'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 16, color: color),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium,
                ),
                Text(
                  time,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingEvents() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Upcoming Events',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildEventItem(
              'Team Meeting',
              'Tomorrow 10:00 AM',
              Icons.event,
              AppColors.primary,
            ),
            _buildEventItem(
              'Project Deadline',
              'Dec 30, 2024',
              Icons.flag,
              AppColors.error,
            ),
            _buildEventItem(
              'Performance Review',
              'Jan 5, 2025',
              Icons.assessment,
              AppColors.info,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventItem(String title, String time, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  time,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Dialog methods
  void _showReportsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Team Reports'),
        content: const SizedBox(
          width: 400,
          child: Text('Generate comprehensive team reports including:\n\n• Attendance Summary\n• Leave Analytics\n• Performance Metrics\n• Productivity Reports'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Navigate to reports page
            },
            child: const Text('Generate Reports'),
          ),
        ],
      ),
    );
  }

  void _showAttendanceDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Team Attendance Details'),
        content: const SizedBox(
          width: 400,
          child: Text('Detailed attendance information will be shown here.\n\nThis will integrate with the existing attendance module.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showLeaveDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Team Leave Management'),
        content: const SizedBox(
          width: 400,
          child: Text('Team leave overview and management.\n\nThis will integrate with the existing leave module.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showProductivityDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Team Productivity Metrics'),
        content: const SizedBox(
          width: 400,
          child: Text('Detailed productivity analytics and task management.\n\nThis will show task completion rates, project progress, and team performance indicators.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAllActivities() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('All Team Activities'),
        content: const SizedBox(
          width: 500,
          height: 400,
          child: Text('Complete activity feed will be shown here.\n\nThis will include all team activities, notifications, and updates.'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showCreateTaskDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Task'),
        content: const SizedBox(
          width: 400,
          child: Text('Task creation form will be implemented here.\n\nThis will include:\n• Task title and description\n• Assignee selection\n• Due date\n• Priority level\n• Project assignment'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement task creation
            },
            child: const Text('Create Task'),
          ),
        ],
      ),
    );
  }
}
