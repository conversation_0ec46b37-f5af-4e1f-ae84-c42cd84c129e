import 'package:equatable/equatable.dart';

enum TaskStatus { todo, in_progress, review, done, cancelled }
enum TaskPriority { low, medium, high, urgent }

class TaskEntity extends Equatable {
  final String id;
  final String title;
  final String? description;
  final dynamic assignedTo; // Can be String (ID) or UserEntity
  final dynamic createdBy; // Can be String (ID) or UserEntity
  final String? teamId;
  final TaskStatus status;
  final TaskPriority priority;
  final DateTime? dueDate;
  final List<TaskAttachmentEntity>? attachments;
  final List<TaskCommentEntity>? comments;
  final List<String>? tags;
  final double? estimatedHours;
  final double? actualHours;
  final DateTime? completedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TaskEntity({
    required this.id,
    required this.title,
    this.description,
    required this.assignedTo,
    required this.createdBy,
    this.teamId,
    required this.status,
    required this.priority,
    this.dueDate,
    this.attachments,
    this.comments,
    this.tags,
    this.estimatedHours,
    this.actualHours,
    this.completedAt,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        assignedTo,
        createdBy,
        teamId,
        status,
        priority,
        dueDate,
        attachments,
        comments,
        tags,
        estimatedHours,
        actualHours,
        completedAt,
        createdAt,
        updatedAt,
      ];

  bool get isOverdue {
    if (dueDate == null || status == TaskStatus.done || status == TaskStatus.cancelled) return false;
    return DateTime.now().isAfter(dueDate!);
  }

  bool get isDueSoon {
    if (dueDate == null || status == TaskStatus.done || status == TaskStatus.cancelled) return false;
    final now = DateTime.now();
    final difference = dueDate!.difference(now).inHours;
    return difference <= 24 && difference >= 0;
  }

  bool get isCompleted => status == TaskStatus.done;
  bool get isCancelled => status == TaskStatus.cancelled;
  bool get isActive => !isCompleted && !isCancelled;

  String get statusDisplayName {
    switch (status) {
      case TaskStatus.todo:
        return 'To Do';
      case TaskStatus.in_progress:
        return 'In Progress';
      case TaskStatus.review:
        return 'Review';
      case TaskStatus.done:
        return 'Done';
      case TaskStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get priorityDisplayName {
    switch (priority) {
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.high:
        return 'High';
      case TaskPriority.urgent:
        return 'Urgent';
    }
  }

  String get assignedToName {
    if (assignedTo is UserEntity) {
      return (assignedTo as UserEntity).fullname;
    }
    return 'Unknown User';
  }

  String? get assignedToAvatar {
    if (assignedTo is UserEntity) {
      return (assignedTo as UserEntity).avatar;
    }
    return null;
  }

  String get createdByName {
    if (createdBy is UserEntity) {
      return (createdBy as UserEntity).fullname;
    }
    return 'Unknown User';
  }

  String get assignedToId {
    if (assignedTo is UserEntity) {
      return (assignedTo as UserEntity).id;
    }
    return assignedTo.toString();
  }

  String get createdById {
    if (createdBy is UserEntity) {
      return (createdBy as UserEntity).id;
    }
    return createdBy.toString();
  }
}

class TaskAttachmentEntity extends Equatable {
  final String fileName;
  final String fileUrl;
  final String fileType;
  final DateTime uploadedAt;

  const TaskAttachmentEntity({
    required this.fileName,
    required this.fileUrl,
    required this.fileType,
    required this.uploadedAt,
  });

  @override
  List<Object?> get props => [fileName, fileUrl, fileType, uploadedAt];

  bool get isImage => fileType.startsWith('image/');
  bool get isDocument => !isImage;
  
  String get fileExtension {
    return fileName.split('.').last.toUpperCase();
  }
}

class TaskCommentEntity extends Equatable {
  final String userId;
  final String message;
  final DateTime createdAt;

  const TaskCommentEntity({
    required this.userId,
    required this.message,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [userId, message, createdAt];
}

class UserEntity extends Equatable {
  final String id;
  final String fullname;
  final String? avatar;
  final String? email;
  final String? department;
  final String? position;

  const UserEntity({
    required this.id,
    required this.fullname,
    this.avatar,
    this.email,
    this.department,
    this.position,
  });

  @override
  List<Object?> get props => [
        id,
        fullname,
        avatar,
        email,
        department,
        position,
      ];
}
