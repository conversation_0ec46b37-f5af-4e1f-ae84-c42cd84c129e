import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_colors.dart';

import '../../../../core/responsive/responsive.dart';
import '../../../../injection_container.dart';
import '../cubit/attendance_page/attendance_cubit.dart';
import '../cubit/attendance_page/attendance_state.dart';
import '../widgets/today_tab_view.dart';
import '../widgets/week_tab_view.dart';
import '../widgets/month_tab_view.dart';

// ========== PHẦN 1: PAGE CHÍNH VÀ VIEW CHÍNH ==========

class AttendancePage extends StatelessWidget {
  const AttendancePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<AttendanceCubitV1>()..loadInitialData(),
      child: const _AttendanceView(),
    );
  }
}

class _AttendanceView extends StatefulWidget {
  const _AttendanceView();

  @override
  State<_AttendanceView> createState() => _AttendanceViewState();
}

class _AttendanceViewState extends State<_AttendanceView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return <Widget>[
            SliverAppBar(
              title: Text(
                l10n.attendance,
                style: context.lightTheme.textTheme.headlineLarge,
              ),
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back,
                  color: Colors.grey[800],
                  size: context.responsive.fontSize(20),
                ),
                onPressed: () => context.pop(),
              ),
              elevation: 1,
              shadowColor: Colors.grey.withAlpha(
                50,
              ), // Using withAlpha for clarity
              backgroundColor: Colors.white,
              pinned: true,
              floating: true,
              bottom: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: Colors.grey[500],
                indicatorColor: AppColors.primary,
                indicatorWeight: 3.0,
                labelStyle: context.lightTheme.textTheme.titleLarge,
                tabs: [
                  Tab(text: l10n.today),
                  Tab(text: l10n.thisWeek),
                  Tab(
                    text: l10n.thisMonth,
                  ), // Assuming l10n.month is for current month
                ],
              ),
            ),
          ];
        },
        body: BlocBuilder<AttendanceCubitV1, AttendanceState>(
          builder: (context, state) {
            if (state is AttendanceLoading || state is AttendanceInitial) {
              return const Center(
                child: CircularProgressIndicator(color: AppColors.primary),
              );
            } else if (state is AttendanceLoaded) {
              return TabBarView(
                controller: _tabController,
                children: [
                  TodayTabView(state: state),
                  WeekTabView(weeklyData: state.weeklySummary),
                  MonthTabView(
                    monthlyData: state.monthlySummary,
                    todaySummary: state.todaySummary,
                    recentHistory: state.recentHistory,
                  ),
                ],
              );
            } else if (state is AttendanceError) {
              return Center(
                child: Padding(
                  padding: responsive.padding(all: 16.0),
                  child: Text(
                    state
                        .message, // This message might already be localized from the cubit/usecase
                    textAlign: TextAlign.center,
                    style: context.lightTheme.textTheme.labelMedium!.copyWith(
                      color: Colors.red[700],
                      fontSize: 16,
                    ),
                  ),
                ),
              );
            }
            return Center(child: Text(l10n.unexpectedError));
          },
        ),
      ),
    );
  }
}
