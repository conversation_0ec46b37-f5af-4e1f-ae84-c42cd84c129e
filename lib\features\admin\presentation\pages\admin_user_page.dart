import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:golderhr/core/services/firebase_service.dart';

import 'package:iconsax/iconsax.dart';
import '../../../../injection_container.dart' as di;
import '../cubit/admin_user_cubit.dart';
import '../widgets/admin_user_filter_widget.dart';
import '../widgets/admin_user_data_grid.dart';
import '../widgets/admin_user_stats_widget.dart';
import '../widgets/admin_user_empty_state_widget.dart';

import '../widgets/edit_user_dialog.dart';
import '../widgets/create_user_dialog.dart';
import '../../../role/presentation/cubit/role_cubit.dart';
import '../../../department/presentation/cubit/department_cubit.dart';
import '../../domain/entities/admin_user_entity.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';

class AdminUserPage extends StatelessWidget {
  const AdminUserPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di.sl<AdminUserCubit>()
        ..loadUsers()
        ..loadStatistics(),
      child: const AdminUserView(),
    );
  }
}

class AdminUserView extends StatefulWidget {
  const AdminUserView({super.key});

  @override
  State<AdminUserView> createState() => _AdminUserViewState();
}

class _AdminUserViewState extends State<AdminUserView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;

    return BlocListener<AdminUserCubit, AdminUserState>(
      listener: (context, state) {
        if (state.hasError) {
          showTopSnackBar(
            context,
            title: context.l10n.error,
            message: state.error!,
            isError: true,
          );
        } else if (state.hasSuccess) {
          showTopSnackBar(
            context,
            title: context.l10n.success,
            message: state.successMessage!,
            isError: false,
          );

          context.read<AdminUserCubit>().clearSuccess();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'User Management',
            style: AppTextStyle.bold(context, size: 20),
          ),
          centerTitle: responsive.isMobile,
          elevation: 0,
          backgroundColor: theme.scaffoldBackgroundColor,
          actions: [
            // Add User Button
            IconButton(
              onPressed: () => _showCreateUserDialog(context),
              icon: Icon(
                Icons.person_add,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 24,
                  mobileLandscape: 22,
                  tabletLandscape: 26,
                ),
              ),
              tooltip: 'Add User', // l10n.addUser,
            ),
            // Test Notification Button
            IconButton(
              onPressed: () => _testNotification(),
              icon: Icon(
                Icons.notifications_active,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 24,
                  mobileLandscape: 22,
                  tabletLandscape: 26,
                ),
              ),
              tooltip: 'Test Notification',
            ),
            // Show FCM Token Button
            IconButton(
              onPressed: () => _showFCMToken(),
              icon: Icon(
                Icons.token,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 24,
                  mobileLandscape: 22,
                  tabletLandscape: 26,
                ),
              ),
              tooltip: 'Show FCM Token',
            ),
            // Reload Button
            IconButton(
              onPressed: () => context.read<AdminUserCubit>().loadUsers(),
              icon: Icon(
                Iconsax.refresh,
                size: responsive.adaptiveValue<double>(
                  mobile: 20,
                  tablet: 24,
                  mobileLandscape: 22,
                  tabletLandscape: 26,
                ),
              ),
              tooltip: 'Reload Data', // l10n.refreshUsers,
            ),
            BlocBuilder<AdminUserCubit, AdminUserState>(
              builder: (context, state) {
                if (state.hasSelection) {
                  return Row(
                    children: [
                      IconButton(
                        onPressed: () => _showBulkActionsDialog(context),
                        icon: Icon(
                          Icons.more_vert,
                          size: responsive.adaptiveValue<double>(
                            mobile: 20,
                            tablet: 24,
                            mobileLandscape: 22,
                            tabletLandscape: 26,
                          ),
                        ),
                        tooltip: 'Bulk Actions', // l10n.bulkActions,
                      ),
                      IconButton(
                        onPressed: () =>
                            context.read<AdminUserCubit>().clearSelection(),
                        icon: Icon(
                          Icons.clear,
                          size: responsive.adaptiveValue<double>(
                            mobile: 20,
                            tablet: 24,
                            mobileLandscape: 22,
                            tabletLandscape: 26,
                          ),
                        ),
                        tooltip: 'Clear Selection', // l10n.clearSelection,
                      ),
                    ],
                  );
                }
                return IconButton(
                  onPressed: () => _showFilterDialog(context),
                  icon: Icon(
                    Iconsax.filter,
                    size: responsive.adaptiveValue<double>(
                      mobile: 20,
                      tablet: 24,
                      mobileLandscape: 22,
                      tabletLandscape: 26,
                    ),
                  ),
                  tooltip: 'Filter & Sort', // l10n.filterAndSort,
                );
              },
            ),
          ],
          bottom: TabBar(
            controller: _tabController,
            labelStyle: AppTextStyle.medium(context, size: 14),
            unselectedLabelStyle: AppTextStyle.regular(context, size: 14),
            tabs: [
              Tab(
                icon: Icon(
                  Icons.people,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                text: 'Users', // l10n.adminUsers,
              ),
              Tab(
                icon: Icon(
                  Icons.analytics,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                text: 'Statistics', // l10n.adminStatistics,
              ),
            ],
          ),
        ),
        body: BlocBuilder<AdminUserCubit, AdminUserState>(
          builder: (context, state) {
            return TabBarView(
              controller: _tabController,
              children: [
                // Users Tab
                _buildUsersTab(context, state),

                // Statistics Tab
                _buildStatisticsTab(context, state),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildUsersTab(BuildContext context, AdminUserState state) {
    final responsive = context.responsive;

    if (state.isLoading && !state.hasUsers) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Filter Section
        const AdminUserFilterWidget(),

        // Selection Info
        if (state.hasSelection)
          Container(
            width: double.infinity,
            padding: responsive.responsivePadding,
            color: Colors.blue.shade50,
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.blue.shade700,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 22,
                    mobileLandscape: 21,
                    tabletLandscape: 24,
                  ),
                ),
                SizedBox(width: responsive.scaleWidth(8)),
                Text(
                  '${state.selectedUserIds.length} users selected', // l10n.usersSelected(state.selectedUserIds.length),
                  style: AppTextStyle.medium(
                    context,
                    size: 14,
                    color: Colors.blue.shade700,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () =>
                      context.read<AdminUserCubit>().selectAllUsers(),
                  child: Text(
                    state.isAllSelected
                        ? 'Deselect All'
                        : 'Select All', // state.isAllSelected ? l10n.deselectAll : l10n.selectAll,
                    style: AppTextStyle.medium(context, size: 14),
                  ),
                ),
              ],
            ),
          ),

        // Users List
        Expanded(
          child: state.hasUsers
              ? Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: responsive.responsivePadding,
                        child: AdminUserDataGrid(
                          users: state.users,
                          isLoading: state.isLoading,
                          selectedUserIds: state.selectedUserIds,
                          onUserTap: (user) =>
                              _showUserDetailsDialog(context, user),
                          onUserSelect: (userId) => context
                              .read<AdminUserCubit>()
                              .toggleUserSelection(userId),
                          onUserAction: (action, user) =>
                              _handleUserAction(context, action, user),
                        ),
                      ),
                    ),
                    // Pagination Controls
                    _buildPaginationControls(context, state),
                  ],
                )
              : _buildEmptyState(context),
        ),
      ],
    );
  }

  Widget _buildStatisticsTab(BuildContext context, AdminUserState state) {
    final responsive = context.responsive;

    if (state.isLoadingStats) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.statistics == null) {
      return Center(
        child: Text(
          'No statistics available', // l10n.noStatisticsAvailable,
          style: AppTextStyle.regular(
            context,
            size: 16,
            color: Colors.grey[600],
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: responsive.responsivePadding,
      child: AdminUserStatsWidget(statistics: state.statistics!),
    );
  }

  Widget _buildPaginationControls(BuildContext context, AdminUserState state) {
    final responsive = context.responsive;
    final theme = context.lightTheme;
    final pagination = state.pagination;

    if (pagination == null) return const SizedBox.shrink();

    return Container(
      padding: responsive.padding(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.cardColor,
        border: Border(top: BorderSide(color: Colors.grey.shade300, width: 1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Page Info
          Text(
            'Page ${pagination.currentPage} of ${pagination.totalPages} (${pagination.totalUsers} users)',
            style: AppTextStyle.medium(
              context,
              size: responsive.adaptiveValue<double>(
                mobile: 12,
                tablet: 14,
                mobileLandscape: 13,
                tabletLandscape: 15,
              ),
              color: Colors.grey.shade600,
            ),
          ),

          // Navigation Buttons
          Row(
            children: [
              // Previous Button
              IconButton(
                onPressed: pagination.hasPrev && !state.isLoading
                    ? () => context.read<AdminUserCubit>().loadUsers(
                        page: pagination.currentPage - 1,
                      )
                    : null,
                icon: Icon(
                  Icons.chevron_left,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 24,
                    mobileLandscape: 22,
                    tabletLandscape: 26,
                  ),
                ),
                tooltip: 'Previous Page',
              ),

              // Next Button
              IconButton(
                onPressed: pagination.hasNext && !state.isLoading
                    ? () => context.read<AdminUserCubit>().loadUsers(
                        page: pagination.currentPage + 1,
                      )
                    : null,
                icon: Icon(
                  Icons.chevron_right,
                  size: responsive.adaptiveValue<double>(
                    mobile: 20,
                    tablet: 24,
                    mobileLandscape: 22,
                    tabletLandscape: 26,
                  ),
                ),
                tooltip: 'Next Page',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return const AdminUserEmptyStateWidget();
  }

  void _showFilterDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: cubit),
          BlocProvider(
            create: (context) => di.sl<RoleCubit>()..loadRolesForDropdown(),
          ),
          BlocProvider(
            create: (context) => di.sl<DepartmentCubit>()..loadDepartments(),
          ),
        ],
        child: const AdminUserFilterWidget(isDialog: true),
      ),
    );
  }

  void _showBulkActionsDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();
    final selectedUsers = cubit.state.selectedUsers;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Bulk Actions (${selectedUsers.length} users)'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: const Text('Delete Selected'),
              onTap: () {
                Navigator.pop(context);
                _confirmBulkDelete(
                  context,
                  selectedUsers.map((u) => u.id).toList(),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.restore, color: Colors.green),
              title: const Text('Restore Selected'),
              onTap: () {
                Navigator.pop(context);
                cubit.bulkRestoreUsers(selectedUsers.map((u) => u.id).toList());
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _confirmBulkDelete(BuildContext context, List<String> userIds) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Bulk Delete'),
        content: Text(
          'Are you sure you want to delete ${userIds.length} users?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminUserCubit>().bulkDeleteUsers(userIds);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showCreateUserDialog(BuildContext context) {
    final cubit = context.read<AdminUserCubit>();
    showDialog(
      context: context,
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: cubit),
          BlocProvider(create: (context) => di.sl<RoleCubit>()),
          BlocProvider(create: (context) => di.sl<DepartmentCubit>()),
        ],
        child: CreateUserDialog(
          onUserCreated: () {
            // Refresh user list after creating new user
            cubit.loadUsers();
          },
        ),
      ),
    );
  }

  void _showUserDetailsDialog(BuildContext context, AdminUserEntity user) {
    final cubit = context.read<AdminUserCubit>();
    showDialog(
      context: context,
      builder: (context) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: cubit),
          BlocProvider(create: (context) => di.sl<RoleCubit>()),
          BlocProvider(create: (context) => di.sl<DepartmentCubit>()),
        ],
        child: EditUserDialog(user: user),
      ),
    );
  }

  void _handleUserAction(
    BuildContext context,
    String action,
    AdminUserEntity user,
  ) {
    final cubit = context.read<AdminUserCubit>();

    switch (action) {
      case 'edit':
        _showUserDetailsDialog(context, user);
        break;
      case 'delete':
        _confirmDeleteUser(context, user);
        break;
      case 'restore':
        cubit.restoreUser(user.id);
        break;
      case 'toggle_status':
        cubit.toggleUserStatus(user.id);
        break;
      case 'reset_password':
        _showResetPasswordDialog(context, user);
        break;
    }
  }

  void _confirmDeleteUser(BuildContext context, AdminUserEntity user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Delete'),
        content: Text('Are you sure you want to delete ${user.displayName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminUserCubit>().softDeleteUser(user.id);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showResetPasswordDialog(BuildContext context, AdminUserEntity user) {
    final passwordController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reset Password for ${user.displayName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Enter new password for ${user.email}'),
            const SizedBox(height: 16),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'New Password',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (passwordController.text.isNotEmpty) {
                Navigator.pop(context);
                context.read<AdminUserCubit>().resetUserPassword(
                  user.id,
                  passwordController.text,
                );
              }
            },
            child: const Text('Reset Password'),
          ),
        ],
      ),
    );
  }

  void _testNotification() {
    FirebaseService().showLocalNotification(
      title: 'Test Notification',
      body: 'This is a test notification from Golder HR app!',
      data: {'type': 'test', 'timestamp': DateTime.now().toIso8601String()},
    );
  }

  Future<void> _showFCMToken() async {
    try {
      final token = await FirebaseService().getCurrentToken();
      if (token != null) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('FCM Token'),
            content: SelectableText(
              token,
              style: TextStyle(fontSize: 12, fontFamily: 'monospace'),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Close'),
              ),
              TextButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: token));
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Token copied to clipboard!')),
                  );
                },
                child: Text('Copy'),
              ),
            ],
          ),
        );
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('FCM Token not available')));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error getting FCM token: $e')));
    }
  }
}
