import 'package:flutter/material.dart';
import 'package:golderhr/core/logger/app_logger.dart';
import 'package:golderhr/injection_container.dart';
import '../notification/domain/usecases/notify_admin_new_request.dart';

class NotificationDebugPage extends StatefulWidget {
  const NotificationDebugPage({super.key});

  @override
  State<NotificationDebugPage> createState() => _NotificationDebugPageState();
}

class _NotificationDebugPageState extends State<NotificationDebugPage> {
  String _result = '';
  bool _isLoading = false;

  Future<void> _testLeaveNotification() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing leave notification...';
    });

    try {
      AppLogger.info('🧪 [DEBUG] Starting leave notification test...');
      
      final notifyAdminUseCase = sl<NotifyAdminNewRequest>();
      AppLogger.info('🧪 [DEBUG] Use case retrieved successfully');
      
      final params = NotifyAdminNewRequestParams(
        requestType: 'leave',
        requestId: 'debug-leave-${DateTime.now().millisecondsSinceEpoch}',
        employeeName: 'Debug Test User',
        requestDetails: 'annual từ 1/1/2024 đến 5/1/2024',
      );
      
      AppLogger.info('🧪 [DEBUG] Calling notification with params: $params');
      
      final result = await notifyAdminUseCase(params);
      
      result.fold(
        (failure) {
          AppLogger.error('🧪 [DEBUG] Notification failed: ${failure.message}');
          setState(() {
            _result = 'FAILED: ${failure.message}';
            _isLoading = false;
          });
        },
        (_) {
          AppLogger.info('🧪 [DEBUG] Notification sent successfully!');
          setState(() {
            _result = 'SUCCESS: Leave notification sent!';
            _isLoading = false;
          });
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('🧪 [DEBUG] Exception: $e');
      AppLogger.error('🧪 [DEBUG] Stack trace: $stackTrace');
      setState(() {
        _result = 'EXCEPTION: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testOvertimeNotification() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing overtime notification...';
    });

    try {
      AppLogger.info('🧪 [DEBUG] Starting overtime notification test...');
      
      final notifyAdminUseCase = sl<NotifyAdminNewRequest>();
      AppLogger.info('🧪 [DEBUG] Use case retrieved successfully');
      
      final params = NotifyAdminNewRequestParams(
        requestType: 'overtime',
        requestId: 'debug-overtime-${DateTime.now().millisecondsSinceEpoch}',
        employeeName: 'Debug Test User',
        requestDetails: 'regular vào 1/1/2024 từ 18:00 đến 20:00',
      );
      
      AppLogger.info('🧪 [DEBUG] Calling notification with params: $params');
      
      final result = await notifyAdminUseCase(params);
      
      result.fold(
        (failure) {
          AppLogger.error('🧪 [DEBUG] Notification failed: ${failure.message}');
          setState(() {
            _result = 'FAILED: ${failure.message}';
            _isLoading = false;
          });
        },
        (_) {
          AppLogger.info('🧪 [DEBUG] Notification sent successfully!');
          setState(() {
            _result = 'SUCCESS: Overtime notification sent!';
            _isLoading = false;
          });
        },
      );
    } catch (e, stackTrace) {
      AppLogger.error('🧪 [DEBUG] Exception: $e');
      AppLogger.error('🧪 [DEBUG] Stack trace: $stackTrace');
      setState(() {
        _result = 'EXCEPTION: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Test Admin Notifications',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testLeaveNotification,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text('Test Leave Notification'),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testOvertimeNotification,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
              child: const Text('Test Overtime Notification'),
            ),
            
            const SizedBox(height: 20),
            
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            
            if (_result.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _result.startsWith('SUCCESS') 
                      ? Colors.green.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  border: Border.all(
                    color: _result.startsWith('SUCCESS') 
                        ? Colors.green 
                        : Colors.red,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _result,
                  style: TextStyle(
                    color: _result.startsWith('SUCCESS') 
                        ? Colors.green.shade700
                        : Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            
            const SizedBox(height: 20),
            
            const Text(
              'Instructions:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Tap the buttons above to test notifications\n'
              '2. Check the console/logs for detailed debug info\n'
              '3. Look for 🧪 [DEBUG] messages in AppLogger\n'
              '4. Check if API calls are being made\n'
              '5. Verify server response',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
