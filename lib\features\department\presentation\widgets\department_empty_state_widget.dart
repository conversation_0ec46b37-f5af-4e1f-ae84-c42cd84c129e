import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import 'package:golderhr/shared/theme/app_text_styles.dart';
import 'package:iconsax/iconsax.dart';

class DepartmentEmptyStateWidget extends StatelessWidget {
  final VoidCallback onCreateDepartment;

  const DepartmentEmptyStateWidget({
    super.key,
    required this.onCreateDepartment,
  });

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Iconsax.building,
            size: responsive.scaleRadius(64),
            color: Colors.grey[400],
          ),
          <PERSON>zed<PERSON><PERSON>(height: responsive.scaleHeight(16)),
          Text(
            'No departments found',
            style: AppTextStyle.bold(
              context,
              size: 18,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(8)),
          Text(
            'Create your first department to get started',
            style: AppTextStyle.regular(
              context,
              size: 14,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(height: responsive.scaleHeight(24)),
          ElevatedButton.icon(
            onPressed: onCreateDepartment,
            icon: const Icon(Iconsax.add),
            label: const Text('Add Department'),
          ),
        ],
      ),
    );
  }
}
