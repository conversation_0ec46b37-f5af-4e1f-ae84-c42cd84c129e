import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../domain/entities/team_chat_entity.dart';
import '../../domain/repositories/team_repository.dart';
import '../../../../core/services/socket_service.dart';

part 'team_chat_state.dart';

class TeamChatCubit extends Cubit<TeamChatState> {
  final TeamRepository repository;
  final SocketService socketService;
  
  StreamSubscription? _chatSubscription;
  StreamSubscription? _typingSubscription;
  
  String? _currentTeamId;
  List<TeamChatEntity> _messages = [];
  Map<String, bool> _typingUsers = {};
  Timer? _typingTimer;

  TeamChatCubit({
    required this.repository,
    required this.socketService,
  }) : super(TeamChatInitial()) {
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    // Listen to chat messages
    _chatSubscription = socketService.chatMessageStream.listen((data) {
      if (data['type'] == 'reaction') {
        _handleMessageReaction(data['data']);
      } else if (data['type'] == 'pinned') {
        _handleMessagePinned(data['data']);
      } else {
        _handleNewMessage(data);
      }
    });

    // Listen to typing indicators
    _typingSubscription = socketService.typingStream.listen((data) {
      _handleTypingIndicator(data);
    });
  }

  Future<void> loadChatHistory(String teamId, {int page = 1}) async {
    if (page == 1) {
      emit(TeamChatLoading());
      _currentTeamId = teamId;
      _messages.clear();
      
      // Join team room for real-time updates
      socketService.joinTeamRoom(teamId);
    }

    final result = await repository.getTeamChatHistory(teamId, page: page, limit: 50);
    
    result.fold(
      (failure) => emit(TeamChatError(failure.message)),
      (chatData) {
        final newMessages = chatData['messages'] as List<TeamChatEntity>;
        final hasMore = chatData['hasMore'] as bool;
        
        if (page == 1) {
          _messages = newMessages;
        } else {
          _messages.insertAll(0, newMessages);
        }
        
        emit(TeamChatLoaded(
          messages: List.from(_messages),
          hasMore: hasMore,
          typingUsers: Map.from(_typingUsers),
        ));
      },
    );
  }

  void sendMessage({
    required String message,
    String type = 'text',
    List<Map<String, dynamic>>? attachments,
    List<String>? mentions,
    String? replyTo,
  }) {
    if (_currentTeamId == null || message.trim().isEmpty) return;

    socketService.sendChatMessage(
      teamId: _currentTeamId!,
      message: message.trim(),
      type: type,
      attachments: attachments,
      mentions: mentions,
      replyTo: replyTo,
    );

    // Stop typing indicator
    _stopTyping();
  }

  void startTyping() {
    if (_currentTeamId == null) return;

    socketService.sendTypingIndicator(
      teamId: _currentTeamId!,
      isTyping: true,
    );

    // Auto-stop typing after 3 seconds
    _typingTimer?.cancel();
    _typingTimer = Timer(const Duration(seconds: 3), () {
      _stopTyping();
    });
  }

  void _stopTyping() {
    if (_currentTeamId == null) return;

    _typingTimer?.cancel();
    socketService.sendTypingIndicator(
      teamId: _currentTeamId!,
      isTyping: false,
    );
  }

  void reactToMessage(String messageId, String emoji) {
    socketService.reactToMessage(
      messageId: messageId,
      emoji: emoji,
    );
  }

  Future<void> pinMessage(String messageId) async {
    if (_currentTeamId == null) return;

    final result = await repository.pinMessage(_currentTeamId!, messageId);
    
    result.fold(
      (failure) => emit(TeamChatError(failure.message)),
      (_) {
        // Real-time update will be handled by socket
      },
    );
  }

  void _handleNewMessage(Map<String, dynamic> data) {
    try {
      final message = TeamChatEntity(
        id: data['_id'] ?? data['id'],
        teamId: data['teamId'],
        senderId: data['sender'],
        message: data['message'],
        type: ChatMessageType.values.firstWhere(
          (e) => e.name == data['type'],
          orElse: () => ChatMessageType.text,
        ),
        attachments: (data['attachments'] as List?)
            ?.map((e) => ChatAttachmentEntity(
                  fileName: e['fileName'],
                  fileUrl: e['fileUrl'],
                  fileType: e['fileType'],
                  fileSize: e['fileSize'],
                ))
            .toList(),
        mentions: List<String>.from(data['mentions'] ?? []),
        replyTo: data['replyTo'],
        isPinned: data['isPinned'] ?? false,
        reactions: (data['reactions'] as List?)
            ?.map((e) => ChatReactionEntity(
                  userId: e['userId'],
                  emoji: e['emoji'],
                  createdAt: DateTime.parse(e['createdAt']),
                ))
            .toList(),
        createdAt: DateTime.parse(data['createdAt']),
      );

      _messages.add(message);
      
      if (state is TeamChatLoaded) {
        emit(TeamChatLoaded(
          messages: List.from(_messages),
          hasMore: (state as TeamChatLoaded).hasMore,
          typingUsers: Map.from(_typingUsers),
        ));
      }
    } catch (e) {
      print('Error handling new message: $e');
    }
  }

  void _handleMessageReaction(Map<String, dynamic> data) {
    final messageId = data['messageId'];
    final reactions = data['reactions'] as List;

    final messageIndex = _messages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      final updatedMessage = _messages[messageIndex];
      // Update reactions - this would need proper implementation
      // For now, just emit the current state
      if (state is TeamChatLoaded) {
        emit(TeamChatLoaded(
          messages: List.from(_messages),
          hasMore: (state as TeamChatLoaded).hasMore,
          typingUsers: Map.from(_typingUsers),
        ));
      }
    }
  }

  void _handleMessagePinned(Map<String, dynamic> data) {
    final messageId = data['messageId'];
    final isPinned = data['isPinned'];

    final messageIndex = _messages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      // Update pinned status - this would need proper implementation
      // For now, just emit the current state
      if (state is TeamChatLoaded) {
        emit(TeamChatLoaded(
          messages: List.from(_messages),
          hasMore: (state as TeamChatLoaded).hasMore,
          typingUsers: Map.from(_typingUsers),
        ));
      }
    }
  }

  void _handleTypingIndicator(Map<String, dynamic> data) {
    final userId = data['userId'];
    final isTyping = data['isTyping'];
    final user = data['user'];

    if (isTyping) {
      _typingUsers[userId] = true;
    } else {
      _typingUsers.remove(userId);
    }

    if (state is TeamChatLoaded) {
      emit(TeamChatLoaded(
        messages: List.from(_messages),
        hasMore: (state as TeamChatLoaded).hasMore,
        typingUsers: Map.from(_typingUsers),
      ));
    }
  }

  void leaveTeam() {
    if (_currentTeamId != null) {
      socketService.leaveTeamRoom(_currentTeamId!);
      _currentTeamId = null;
    }
    _messages.clear();
    _typingUsers.clear();
    _typingTimer?.cancel();
    emit(TeamChatInitial());
  }

  @override
  Future<void> close() {
    _chatSubscription?.cancel();
    _typingSubscription?.cancel();
    _typingTimer?.cancel();
    if (_currentTeamId != null) {
      socketService.leaveTeamRoom(_currentTeamId!);
    }
    return super.close();
  }
}
