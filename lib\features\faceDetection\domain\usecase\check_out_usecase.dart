import 'package:dartz/dartz.dart';
import 'package:golderhr/features/faceDetection/domain/entities/attendance_record_entity.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/attendance_repository.dart';
import 'check_in_usecase.dart';

class CheckOutUseCase
    implements UseCase<AttendanceRecordEntity, CheckInOutParams> {
  final AttendanceRepository repository;

  CheckOutUseCase(this.repository);

  @override
  Future<Either<Failure, AttendanceRecordEntity>> call(
    CheckInOutParams params,
  ) async {
    return await repository.checkOut(
      image: params.image,
      location: params.location,
    );
  }
}
