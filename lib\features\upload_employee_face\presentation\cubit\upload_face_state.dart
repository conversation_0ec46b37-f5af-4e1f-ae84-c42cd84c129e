import 'dart:io';
import 'package:equatable/equatable.dart';
import '../../domain/entities/user_for_dropdown_entity.dart';

enum UploadStatus {
  initial,
  loadingUsers,
  success,
  uploading,
  uploadSuccess,
  failure,
}

class UploadFaceState extends Equatable {
  final UploadStatus status;
  final List<UserForDropdownEntity> users;
  final UserForDropdownEntity? selectedUser;
  final File? imageFile;
  final String? errorMessage;

  const UploadFaceState({
    this.status = UploadStatus.initial,
    this.users = const [],
    this.selectedUser,
    this.imageFile,
    this.errorMessage,
  });

  UploadFaceState copyWith({
    UploadStatus? status,
    List<UserForDropdownEntity>? users,
    UserForDropdownEntity? selectedUser,
    File? imageFile,
    String? errorMessage,
    bool clearSelectedUser = false,
    bool clearImageFile = false,
  }) {
    return UploadFaceState(
      status: status ?? this.status,
      users: users ?? this.users,

      selectedUser: clearSelectedUser
          ? null
          : selectedUser ?? this.selectedUser,

      imageFile: clearImageFile ? null : imageFile ?? this.imageFile,

      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    status,
    users,
    selectedUser,
    imageFile,
    errorMessage,
  ];
}
