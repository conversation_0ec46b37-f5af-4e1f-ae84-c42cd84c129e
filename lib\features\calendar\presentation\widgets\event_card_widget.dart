import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/extensions/responsive_extension.dart';
import '../../../../core/routes/app_routes.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../domain/entities/calendar_event.dart';

/// Widget hiển thị thông tin event dưới dạng card
class EventCardWidget extends StatelessWidget {
  final CalendarEventEntity event;
  final bool showDate;
  final VoidCallback? onTap;

  const EventCardWidget({
    super.key,
    required this.event,
    this.showDate = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showEventDetails(context),
      child: Container(
        padding: context.responsive.padding(all: 12),
        decoration: BoxDecoration(
          color: _parseColor(event.color).withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(10),
          border: Border(
            left: BorderSide(color: _parseColor(event.color), width: 4),
          ),
        ),
        child: Row(
          children: [
            _buildEventIcon(context),
            SizedBox(width: context.rw(12)),
            Expanded(child: _buildEventInfo(context)),
            _buildEventTime(context),
          ],
        ),
      ),
    );
  }

  Widget _buildEventIcon(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(context.rw(8)),
      decoration: BoxDecoration(
        color: _parseColor(event.color).withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        _getEventTypeIcon(event.type),
        color: _parseColor(event.color),
        size: context.rf(18),
      ),
    );
  }

  Widget _buildEventInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          event.title,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (event.description?.isNotEmpty == true) ...[
          SizedBox(height: context.rh(2)),
          Text(
            event.description!,
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
        if (event.location != null && event.location!.isNotEmpty) ...[
          SizedBox(height: context.rh(2)),
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                size: context.rf(12),
                color: AppColors.textSecondary,
              ),
              SizedBox(width: context.rw(4)),
              Expanded(
                child: Text(
                  event.location!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
        if (showDate) ...[
          SizedBox(height: context.rh(2)),
          Text(
            _getDateString(
              DateTime(
                event.startTime.year,
                event.startTime.month,
                event.startTime.day,
              ),
            ),
            style: Theme.of(
              context,
            ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
          ),
        ],
      ],
    );
  }

  Widget _buildEventTime(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: context.rw(8),
            vertical: context.rh(4),
          ),
          decoration: BoxDecoration(
            color: _parseColor(event.color).withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            _formatTime(event.startTime),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _parseColor(event.color),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (event.isAllDay) ...[
          SizedBox(height: context.rh(4)),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: context.rw(6),
              vertical: context.rh(2),
            ),
            decoration: BoxDecoration(
              color: AppColors.info.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'All Day',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: AppColors.info,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
        if (event.isRecurring) ...[
          SizedBox(height: context.rh(4)),
          Icon(
            Icons.repeat,
            size: context.rf(14),
            color: AppColors.textSecondary,
          ),
        ],
      ],
    );
  }

  void _showEventDetails(BuildContext context) {
    context.pushNamed(AppRoutes.eventDetail, extra: event);
  }

  String _formatTime(DateTime time) {
    final hour = time.hour;
    final minute = time.minute;
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  String _getDateString(DateTime date) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${date.day} ${monthNames[date.month - 1]}';
  }

  /// Parse color string to Color object
  Color _parseColor(String colorString) {
    try {
      // Remove # if present
      String hexColor = colorString.replaceAll('#', '');

      // Add alpha if not present
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Return default color if parsing fails
      return AppColors.primaryBlue;
    }
  }

  /// Get icon for event type
  IconData _getEventTypeIcon(CalendarEventType type) {
    switch (type) {
      case CalendarEventType.meeting:
        return Icons.people;
      case CalendarEventType.leave:
        return Icons.time_to_leave;
      case CalendarEventType.holiday:
        return Icons.celebration;
      case CalendarEventType.training:
        return Icons.school;
      case CalendarEventType.event:
        return Icons.event;
      case CalendarEventType.other:
        return Icons.more_horiz;
      case CalendarEventType.unknown:
        return Icons.help_outline;
    }
  }
}

/// Widget hiển thị event card với style compact
class CompactEventCardWidget extends StatelessWidget {
  final CalendarEventEntity event;
  final VoidCallback? onTap;

  const CompactEventCardWidget({super.key, required this.event, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: context.responsive.padding(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: _parseColor(event.color).withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: _parseColor(event.color).withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: context.rw(4),
              height: context.rw(4),
              decoration: BoxDecoration(
                color: _parseColor(event.color),
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: context.rw(8)),
            Expanded(
              child: Text(
                event.title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              _formatTime(event.startTime),
              style: Theme.of(
                context,
              ).textTheme.labelSmall?.copyWith(color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  /// Format time for display
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour.toString().padLeft(2, '0');
    final minute = dateTime.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  /// Parse color string to Color object
  Color _parseColor(String colorString) {
    try {
      // Remove # if present
      String hexColor = colorString.replaceAll('#', '');

      // Add alpha if not present
      if (hexColor.length == 6) {
        hexColor = 'FF$hexColor';
      }

      return Color(int.parse(hexColor, radix: 16));
    } catch (e) {
      // Return default color if parsing fails
      return AppColors.primaryBlue;
    }
  }
}
