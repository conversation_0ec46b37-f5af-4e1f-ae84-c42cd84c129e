{"deviceInfo":{"deviceId":"SKQ1.211006.001","platform":"Android 12"},"eventType":"FACE_VERIFICATION_FAILURE","level":"warn","locationInfo":{"address":", 17 <PERSON><PERSON>,  , <PERSON><PERSON>, Việt <PERSON>","coordinates":[106.7066911,10.7650443]},"message":"Face verification failed","service":"attendance-audit","timestamp":"2025-07-11 10:28:18","userEmail":"<EMAIL>","userId":"6865ead450d18d414460ae55","verificationDetails":{"failureReason":"Face verification failed","method":"legacy","success":false}}
{"eventType":"FACE_VERIFICATION_FAILURE","level":"warn","locationInfo":{"address":"123 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>","coordinates":[106.6297,10.8231]},"message":"Face verification failed","service":"attendance-audit","timestamp":"2025-07-11 11:22:32","userEmail":"<EMAIL>","userId":"6865e5ee50d18d414460add8","verificationDetails":{"failureReason":"Face verification failed","layer1Result":{"distance":0.567529111098959,"match":false},"layer2Result":null,"method":"2-layer","success":false}}
{"eventType":"FACE_VERIFICATION_SUCCESS","level":"info","locationInfo":{"address":"123 Đường Láng, Hà Nội","coordinates":[106.6297,10.8231]},"message":"Face verification successful","service":"attendance-audit","timestamp":"2025-07-11 11:23:39","userEmail":"<EMAIL>","userId":"6865e5ee50d18d414460add8","verificationDetails":{"layer1Result":{"distance":0.0311122411500965,"match":true,"method":"face_recognition"},"layer2Result":{"disabled":true,"method":"DeepFace_ArcFace","reason":"DeepFace temporarily disabled for testing"},"method":"layer1-only","success":true}}
{"eventType":"CHECK_IN_SUCCESS","level":"info","locationInfo":{"address":"123 Đường Láng, Hà Nội","coordinates":[106.6297,10.8231]},"message":"CHECK_IN successful","service":"attendance-audit","timestamp":"2025-07-11 11:23:39","userEmail":"<EMAIL>","userId":"6865e5ee50d18d414460add8","verificationDetails":{"layer1Result":{"distance":0.0311122411500965,"match":true,"method":"face_recognition"},"layer2Result":{"disabled":true,"method":"DeepFace_ArcFace","reason":"DeepFace temporarily disabled for testing"},"method":"layer1-only","success":true}}
