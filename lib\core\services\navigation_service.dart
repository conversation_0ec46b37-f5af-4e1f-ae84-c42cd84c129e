import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../routes/app_routes.dart';
import '../../features/notification/data/model/notification_model.dart';
import '../logger/app_logger.dart';

/// Global navigation service để điều hướng mà không cần BuildContext
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  /// Global navigator key
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Get current context
  BuildContext? get currentContext => navigatorKey.currentContext;

  /// Get current GoRouter
  GoRouter? get router {
    final context = currentContext;
    if (context != null) {
      return GoRouter.of(context);
    }
    return null;
  }

  /// Navigate to notification detail page
  Future<void> navigateToNotificationDetail({
    required String notificationId,
    NotificationModel? notification,
  }) async {
    final context = currentContext;
    final currentRouter = router;
    
    if (context == null || currentRouter == null) {
      AppLogger.error('NavigationService: No context or router available');
      return;
    }

    try {
      // Nếu có notification object, sử dụng nó
      if (notification != null) {
        currentRouter.pushNamed(
          AppRoutes.notificationDetailRelative,
          pathParameters: {'id': notificationId},
          extra: notification,
        );
      } else {
        // Nếu không có notification object, cần fetch từ API hoặc navigate đến notification list
        // Tạm thời navigate đến notification page
        currentRouter.pushNamed(AppRoutes.notifications);
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to notification detail', e);
      // Fallback: navigate to notification page
      try {
        currentRouter.pushNamed(AppRoutes.notifications);
      } catch (fallbackError) {
        AppLogger.error('NavigationService: Fallback navigation also failed', fallbackError);
      }
    }
  }

  /// Navigate to notification page
  Future<void> navigateToNotifications() async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      currentRouter.pushNamed(AppRoutes.notifications);
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to notifications', e);
    }
  }

  /// Navigate to home page
  Future<void> navigateToHome() async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      currentRouter.goNamed('home');
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to home', e);
    }
  }

  /// Navigate to overtime request page (if exists)
  Future<void> navigateToOvertimeRequest({String? requestId}) async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      // Tạm thời navigate đến home page vì chưa có overtime request page riêng
      currentRouter.goNamed('home');
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to overtime request', e);
    }
  }

  /// Generic navigation method
  Future<void> navigateTo(String routeName, {
    Map<String, String>? pathParameters,
    Object? extra,
  }) async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      if (pathParameters != null) {
        currentRouter.pushNamed(
          routeName,
          pathParameters: pathParameters,
          extra: extra,
        );
      } else {
        currentRouter.pushNamed(routeName, extra: extra);
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to $routeName', e);
    }
  }

  /// Handle notification tap based on notification data
  Future<void> handleNotificationTap(Map<String, dynamic> data) async {
    AppLogger.info('NavigationService: Handling notification tap with data: $data');

    try {
      // Xử lý dựa trên type của notification
      final String? type = data['type'];
      final String? notificationId = data['notificationId'];
      final String? overtimeRequestId = data['overtimeRequestId'];

      switch (type) {
        case 'overtime_request':
        case 'overtime_approved':
        case 'overtime_rejected':
          if (notificationId != null) {
            await navigateToNotificationDetail(notificationId: notificationId);
          } else if (overtimeRequestId != null) {
            await navigateToOvertimeRequest(requestId: overtimeRequestId);
          } else {
            await navigateToNotifications();
          }
          break;

        case 'attendance':
        case 'leave':
        case 'announcement':
        case 'system':
        default:
          if (notificationId != null) {
            await navigateToNotificationDetail(notificationId: notificationId);
          } else {
            await navigateToNotifications();
          }
          break;
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error handling notification tap', e);
      // Fallback: navigate to notifications page
      await navigateToNotifications();
    }
  }
}
