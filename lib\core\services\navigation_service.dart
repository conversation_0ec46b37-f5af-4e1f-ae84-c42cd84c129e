import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:iconsax/iconsax.dart';
import '../routes/app_routes.dart';
import '../../features/notification/data/model/notification_model.dart';
import '../logger/app_logger.dart';

/// Global navigation service để điều hướng mà không cần BuildContext
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  /// Global navigator key
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  /// Pending navigation data để xử lý khi app ready
  Map<String, dynamic>? _pendingNavigationData;

  /// Get current context
  BuildContext? get currentContext => navigatorKey.currentContext;

  /// Get current GoRouter
  GoRouter? get router {
    final context = currentContext;
    if (context != null) {
      try {
        return GoRouter.of(context);
      } catch (e) {
        AppLogger.error('NavigationService: Error getting GoRouter', e);
        return null;
      }
    }
    return null;
  }

  /// Check if navigation is ready
  bool get isNavigationReady => currentContext != null && router != null;

  /// Navigate to notification detail page
  Future<void> navigateToNotificationDetail({
    required String notificationId,
    NotificationModel? notification,
    Map<String, dynamic>? firebaseData,
  }) async {
    final context = currentContext;
    final currentRouter = router;

    if (context == null || currentRouter == null) {
      AppLogger.error('NavigationService: No context or router available');
      return;
    }

    try {
      NotificationModel? notificationToUse = notification;

      // Nếu không có notification object nhưng có Firebase data, tạo từ data
      if (notificationToUse == null && firebaseData != null) {
        notificationToUse = _createNotificationFromFirebaseData(firebaseData);
      }

      // Nếu có notification object, sử dụng nó
      if (notificationToUse != null) {
        currentRouter.pushNamed(
          AppRoutes.notificationDetailRelative,
          pathParameters: {'id': notificationId},
          extra: notificationToUse,
        );
      } else {
        // Nếu không có notification object, navigate đến notification page
        currentRouter.pushNamed(AppRoutes.notifications);
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to notification detail', e);
      // Fallback: navigate to notification page
      try {
        currentRouter.pushNamed(AppRoutes.notifications);
      } catch (fallbackError) {
        AppLogger.error('NavigationService: Fallback navigation also failed', fallbackError);
      }
    }
  }

  /// Navigate to notification page
  Future<void> navigateToNotifications() async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      currentRouter.pushNamed(AppRoutes.notifications);
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to notifications', e);
    }
  }

  /// Navigate to home page
  Future<void> navigateToHome() async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      currentRouter.goNamed('home');
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to home', e);
    }
  }

  /// Navigate to overtime request page (if exists)
  Future<void> navigateToOvertimeRequest({String? requestId}) async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      // Tạm thời navigate đến home page vì chưa có overtime request page riêng
      currentRouter.goNamed('home');
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to overtime request', e);
    }
  }

  /// Generic navigation method
  Future<void> navigateTo(String routeName, {
    Map<String, String>? pathParameters,
    Object? extra,
  }) async {
    final currentRouter = router;
    
    if (currentRouter == null) {
      AppLogger.error('NavigationService: No router available');
      return;
    }

    try {
      if (pathParameters != null) {
        currentRouter.pushNamed(
          routeName,
          pathParameters: pathParameters,
          extra: extra,
        );
      } else {
        currentRouter.pushNamed(routeName, extra: extra);
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error navigating to $routeName', e);
    }
  }

  /// Handle notification tap based on notification data
  Future<void> handleNotificationTap(Map<String, dynamic> data) async {
    AppLogger.info('NavigationService: Handling notification tap with data: $data');

    // Nếu navigation chưa ready, lưu data để xử lý sau
    if (!isNavigationReady) {
      AppLogger.info('NavigationService: Navigation not ready, storing pending data');
      _pendingNavigationData = data;
      return;
    }

    await _performNavigation(data);
  }

  /// Xử lý pending navigation khi app đã ready
  Future<void> processPendingNavigation() async {
    if (_pendingNavigationData != null && isNavigationReady) {
      AppLogger.info('NavigationService: Processing pending navigation');
      final data = _pendingNavigationData!;
      _pendingNavigationData = null;
      await _performNavigation(data);
    }
  }

  /// Thực hiện navigation dựa trên data
  Future<void> _performNavigation(Map<String, dynamic> data) async {
    try {
      // Xử lý dựa trên type của notification
      final String? type = data['type'];
      final String? notificationId = data['notificationId'];
      final String? overtimeRequestId = data['overtimeRequestId'];

      switch (type) {
        case 'overtime_request':
        case 'overtime_approved':
        case 'overtime_rejected':
          if (notificationId != null) {
            await navigateToNotificationDetail(
              notificationId: notificationId,
              firebaseData: data,
            );
          } else if (overtimeRequestId != null) {
            await navigateToOvertimeRequest(requestId: overtimeRequestId);
          } else {
            await navigateToNotifications();
          }
          break;

        case 'attendance':
        case 'leave':
        case 'announcement':
        case 'system':
        default:
          if (notificationId != null) {
            await navigateToNotificationDetail(
              notificationId: notificationId,
              firebaseData: data,
            );
          } else {
            await navigateToNotifications();
          }
          break;
      }
    } catch (e) {
      AppLogger.error('NavigationService: Error performing navigation', e);
      // Fallback: navigate to notifications page
      await navigateToNotifications();
    }
  }

  /// Tạo NotificationModel từ Firebase data
  NotificationModel? _createNotificationFromFirebaseData(Map<String, dynamic> data) {
    try {
      final String? notificationId = data['notificationId'];
      final String? title = data['title'];
      final String? message = data['message'];
      final String? timestampStr = data['timestamp'];
      final String? isReadStr = data['isRead'];
      final String? isImportantStr = data['isImportant'];
      final String? category = data['category'];
      final String? iconStr = data['icon'];
      final String? colorStr = data['color'];

      if (notificationId == null || title == null || message == null) {
        AppLogger.error('NavigationService: Missing required fields in Firebase data');
        return null;
      }

      // Parse timestamp
      DateTime timestamp = DateTime.now();
      if (timestampStr != null) {
        try {
          timestamp = DateTime.parse(timestampStr);
        } catch (e) {
          AppLogger.error('NavigationService: Error parsing timestamp', e);
        }
      }

      // Parse boolean values
      bool isRead = isReadStr?.toLowerCase() == 'true';
      bool isImportant = isImportantStr?.toLowerCase() == 'true';

      // Map icon string to IconData
      IconData icon = _getIconFromString(iconStr ?? 'notifications');

      // Parse color
      Color color = _getColorFromString(colorStr ?? '#2196F3');

      // Map category
      NotificationCategory notificationCategory = _getCategoryFromString(category ?? 'system');

      return NotificationModel(
        id: notificationId,
        title: title,
        message: message,
        timestamp: timestamp,
        icon: icon,
        color: color,
        isRead: isRead,
        isImportant: isImportant,
        category: notificationCategory,
      );
    } catch (e) {
      AppLogger.error('NavigationService: Error creating notification from Firebase data', e);
      return null;
    }
  }

  /// Map icon string to IconData
  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'notifications':
        return Iconsax.notification;
      case 'access_time':
        return Iconsax.clock;
      case 'event_available':
        return Iconsax.calendar;
      case 'campaign':
        return Iconsax.speaker;
      case 'alarm':
        return Iconsax.alarm;
      case 'info':
        return Iconsax.info_circle;
      case 'schedule':
        return Iconsax.timer;
      case 'check_circle':
        return Iconsax.tick_circle;
      case 'cancel':
        return Iconsax.close_circle;
      case 'event':
        return Iconsax.calendar_1;
      default:
        return Iconsax.notification;
    }
  }

  /// Parse color from hex string
  Color _getColorFromString(String colorStr) {
    try {
      if (colorStr.startsWith('#')) {
        return Color(int.parse(colorStr.substring(1), radix: 16) + 0xFF000000);
      }
      return const Color(0xFF2196F3); // Default blue
    } catch (e) {
      return const Color(0xFF2196F3); // Default blue
    }
  }

  /// Map category string to NotificationCategory
  NotificationCategory _getCategoryFromString(String categoryStr) {
    switch (categoryStr.toLowerCase()) {
      case 'system':
        return NotificationCategory.system;
      case 'customer':
        return NotificationCategory.customer;
      case 'promotion':
        return NotificationCategory.promotion;
      case 'attendance':
        return NotificationCategory.attendance;
      case 'leave':
        return NotificationCategory.leave;
      case 'announcement':
        return NotificationCategory.announcement;
      case 'reminder':
        return NotificationCategory.reminder;
      case 'custom':
        return NotificationCategory.custom;
      default:
        return NotificationCategory.system;
    }
  }
}
