// lib/features/auth/data/models/login_response_model.dart

import 'package:golderhr/features/auth/data/models/user_model.dart';

class LoginResponseModel {
  final bool success;
  final String message;
  final String token;
  final UserModel user;

  const LoginResponseModel({
    required this.success,
    required this.message,
    required this.token,
    required this.user,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    // Đọc các giá trị ở tầng ngoài cùng một cách an toàn
    final bool isSuccess = json['success'] as bool? ?? false;
    final String msg =
        json['message'] as String? ?? 'An unknown error occurred';

    final data = json['data'];

    if (isSuccess && data != null && data is Map<String, dynamic>) {
      final userJson = data['user']; // Sửa từ 'userId' thành 'user'

      return LoginResponseModel(
        success: true,
        message: msg,
        token: data['token'] as String? ?? '',
        user: (userJson != null && userJson is Map<String, dynamic>)
            ? UserModel.fromJson(userJson)
            : UserModel.empty(),
      );
    } else {
      return LoginResponseModel(
        success: false,
        message: msg,
        token: '',
        user: UserModel.empty(),
      );
    }
  }
}
