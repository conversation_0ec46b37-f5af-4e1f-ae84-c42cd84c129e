import 'package:flutter/material.dart';
import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/responsive/responsive.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../../domain/entities/leave_request.dart';

class LeaveRequestDetailsPage extends StatelessWidget {
  final LeaveRequest request;

  const LeaveRequestDetailsPage({super.key, required this.request});

  @override
  Widget build(BuildContext context) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.background,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.textPrimary),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          context.l10n.requestDetails,
          style: theme.textTheme.titleLarge?.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (request.isPending)
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: AppColors.textPrimary),
              onSelected: (value) {
                if (value == 'cancel') {
                  _showCancelDialog(context);
                } else if (value == 'edit') {
                  _editRequest(context);
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      const Icon(Icons.edit_outlined),
                      const SizedBox(width: 8),
                      Text(context.l10n.editRequest),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      const Icon(Icons.cancel_outlined, color: AppColors.error),
                      const SizedBox(width: 8),
                      Text(
                        context.l10n.cancelRequest,
                        style: const TextStyle(color: AppColors.error),
                      ),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: responsive.padding(all: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Container(
              width: double.infinity,
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: _getStatusColor(request.status).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: _getStatusColor(request.status).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    _getStatusIcon(request.status),
                    size: 48,
                    color: _getStatusColor(request.status),
                  ),
                  SizedBox(height: responsive.heightPercentage(1)),
                  Text(
                    _getStatusDisplayName(context, request.status),
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: _getStatusColor(request.status),
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  if (request.approverName != null) ...[
                    SizedBox(height: responsive.heightPercentage(0.5)),
                    Text(
                      'by ${request.approverName}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            SizedBox(height: responsive.heightPercentage(3)),

            // Request Details Card
            Container(
              width: double.infinity,
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.textSecondary.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.l10n.requestDetails,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontSize: responsive.fontSize(18),
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: responsive.heightPercentage(2)),

                  _buildDetailRow(
                    context,
                    context.l10n.leaveType,
                    _getTypeDisplayName(context, request.type),
                    _getTypeIcon(request.type),
                    _getTypeColor(request.type),
                  ),

                  _buildDetailRow(
                    context,
                    context.l10n.startDate,
                    _formatDate(request.startDate),
                    Icons.calendar_today_outlined,
                    AppColors.primaryBlue,
                  ),

                  _buildDetailRow(
                    context,
                    context.l10n.endDate,
                    _formatDate(request.endDate),
                    Icons.event_outlined,
                    AppColors.primaryBlue,
                  ),

                  _buildDetailRow(
                    context,
                    context.l10n.duration,
                    '${request.duration} ${request.duration > 1 ? context.l10n.days : context.l10n.day}',
                    Icons.schedule_outlined,
                    AppColors.primaryGreen,
                  ),

                  _buildDetailRow(
                    context,
                    context.l10n.submittedOn,
                    _formatDateTime(request.createdAt),
                    Icons.send_outlined,
                    AppColors.textSecondary,
                  ),

                  if (request.approvedAt != null)
                    _buildDetailRow(
                      context,
                      context.l10n.approvedOn,
                      _formatDateTime(request.approvedAt!),
                      Icons.check_circle_outline,
                      AppColors.success,
                    ),
                ],
              ),
            ),

            SizedBox(height: responsive.heightPercentage(3)),

            // Reason Card
            Container(
              width: double.infinity,
              padding: responsive.padding(all: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.textSecondary.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.description_outlined,
                        color: AppColors.warning,
                        size: 20,
                      ),
                      SizedBox(width: responsive.widthPercentage(2)),
                      Text(
                        context.l10n.reason,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontSize: responsive.fontSize(16),
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: responsive.heightPercentage(1.5)),
                  Container(
                    width: double.infinity,
                    padding: responsive.padding(all: 16),
                    decoration: BoxDecoration(
                      color: AppColors.background,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      request.reason,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.textPrimary,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            if (request.rejectionReason != null) ...[
              SizedBox(height: responsive.heightPercentage(3)),

              // Rejection Reason Card
              Container(
                width: double.infinity,
                padding: responsive.padding(all: 20),
                decoration: BoxDecoration(
                  color: AppColors.error.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.error.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: AppColors.error,
                          size: 20,
                        ),
                        SizedBox(width: responsive.widthPercentage(2)),
                        Text(
                          context.l10n.rejectionReason,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontSize: responsive.fontSize(16),
                            color: AppColors.error,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: responsive.heightPercentage(1.5)),
                    Text(
                      request.rejectionReason!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: AppColors.textPrimary,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            SizedBox(height: responsive.heightPercentage(3)),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final responsive = Responsive.of(context);
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.only(bottom: responsive.heightPercentage(1.5)),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          SizedBox(width: responsive.widthPercentage(3)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: responsive.fontSize(12),
                  ),
                ),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                    fontSize: responsive.fontSize(14),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(context.l10n.cancelRequest),
        content: Text(context.l10n.cancelRequestConfirm),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(context.l10n.no),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement cancel logic
              showTopSnackBar(
                context,
                title: context.l10n.success,
                message: context.l10n.requestCancelled,
                isError: false,
              );
            },
            child: Text(
              context.l10n.yes,
              style: const TextStyle(color: AppColors.error),
            ),
          ),
        ],
      ),
    );
  }

  void _editRequest(BuildContext context) {
    // Navigate to edit page
    showTopSnackBar(
      context,
      title: context.l10n.notification,
      message: context.l10n.editFunctionalityComingSoon,
      isError: false,
    );
  }

  Color _getStatusColor(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.approved:
        return AppColors.success;
      case LeaveStatus.pending:
        return AppColors.warning;
      case LeaveStatus.rejected:
        return AppColors.error;
      case LeaveStatus.cancelled:
        return AppColors.textSecondary;
    }
  }

  IconData _getStatusIcon(LeaveStatus status) {
    switch (status) {
      case LeaveStatus.approved:
        return Icons.check_circle_outline;
      case LeaveStatus.pending:
        return Icons.pending_actions_outlined;
      case LeaveStatus.rejected:
        return Icons.cancel_outlined;
      case LeaveStatus.cancelled:
        return Icons.block_outlined;
    }
  }

  String _getStatusDisplayName(BuildContext context, LeaveStatus status) {
    switch (status) {
      case LeaveStatus.approved:
        return context.l10n.approved;
      case LeaveStatus.pending:
        return context.l10n.pendingApproval;
      case LeaveStatus.rejected:
        return context.l10n.rejected;
      case LeaveStatus.cancelled:
        return context.l10n.cancelled;
    }
  }

  Color _getTypeColor(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return AppColors.primaryBlue;
      case LeaveType.sick:
        return AppColors.error;
      case LeaveType.personal:
        return AppColors.warning;
      case LeaveType.maternity:
        return AppColors.primaryGreen;
      case LeaveType.unpaid:
        return AppColors.textSecondary;
    }
  }

  IconData _getTypeIcon(LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return Icons.beach_access_outlined;
      case LeaveType.sick:
        return Icons.local_hospital_outlined;
      case LeaveType.personal:
        return Icons.person_outline;
      case LeaveType.maternity:
        return Icons.child_care_outlined;
      case LeaveType.unpaid:
        return Icons.money_off_outlined;
    }
  }

  String _getTypeDisplayName(BuildContext context, LeaveType type) {
    switch (type) {
      case LeaveType.annual:
        return context.l10n.annualLeave;
      case LeaveType.sick:
        return context.l10n.sickLeave;
      case LeaveType.personal:
        return context.l10n.personalLeave;
      case LeaveType.maternity:
        return context.l10n.maternityLeave;
      case LeaveType.unpaid:
        return context.l10n.unpaidLeave;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${_formatDate(dateTime)} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
