import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:golderhr/features/faceDetection/domain/entities/attendance_record_entity.dart';
import 'package:golderhr/features/faceDetection/domain/entities/location_entity.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/attendance_repository.dart';

class CheckInUseCase
    implements UseCase<AttendanceRecordEntity, CheckInOutParams> {
  final AttendanceRepository repository;

  CheckInUseCase(this.repository);

  @override
  Future<Either<Failure, AttendanceRecordEntity>> call(
    CheckInOutParams params,
  ) async {
    return await repository.checkIn(
      image: params.image,
      location: params.location,
    );
  }
}

class CheckInOutParams {
  final File image;
  final LocationEntity location;

  CheckInOutParams({required this.image, required this.location});
}
