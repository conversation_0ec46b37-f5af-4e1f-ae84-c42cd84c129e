import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/task_entity.dart';
import '../repositories/task_repository.dart';

class CreateTask implements UseCase<TaskEntity, CreateTaskParams> {
  final TaskRepository repository;

  CreateTask(this.repository);

  @override
  Future<Either<Failure, TaskEntity>> call(CreateTaskParams params) async {
    return await repository.createTask(params.toJson());
  }
}

class CreateTaskParams extends Equatable {
  final String title;
  final String? description;
  final String assignedTo;
  final String? teamId;
  final TaskPriority priority;
  final DateTime? dueDate;
  final List<String>? tags;
  final double? estimatedHours;

  const CreateTaskParams({
    required this.title,
    this.description,
    required this.assignedTo,
    this.teamId,
    this.priority = TaskPriority.medium,
    this.dueDate,
    this.tags,
    this.estimatedHours,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'teamId': teamId,
      'priority': priority.name,
      'dueDate': dueDate?.toIso8601String(),
      'tags': tags,
      'estimatedHours': estimatedHours,
    };
  }

  @override
  List<Object?> get props => [
        title,
        description,
        assignedTo,
        teamId,
        priority,
        dueDate,
        tags,
        estimatedHours,
      ];
}
