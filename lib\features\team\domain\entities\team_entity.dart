import 'package:equatable/equatable.dart';

class TeamEntity extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? avatar;
  final String leaderId;
  final String? departmentId;
  final bool isActive;
  final String? userRole; // leader, member, viewer
  final int? memberCount;
  final TeamStatsEntity? stats;
  final List<TeamMemberEntity>? members;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const TeamEntity({
    required this.id,
    required this.name,
    this.description,
    this.avatar,
    required this.leaderId,
    this.departmentId,
    this.isActive = true,
    this.userRole,
    this.memberCount,
    this.stats,
    this.members,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        avatar,
        leaderId,
        departmentId,
        isActive,
        userRole,
        memberCount,
        stats,
        members,
        createdAt,
        updatedAt,
      ];

  bool get isLeader => userRole == 'leader';
  bool get isMember => userRole == 'member';
  bool get isViewer => userRole == 'viewer';
  bool get canManage => userRole == 'leader';
  bool get canEdit => userRole == 'leader' || userRole == 'member';
}

class TeamStatsEntity extends Equatable {
  final int totalTasks;
  final int completedTasks;
  final int pendingTasks;
  final int totalMembers;
  final int totalMeetings;
  final int totalDocuments;
  final int totalMessages;
  final int completionRate;

  const TeamStatsEntity({
    required this.totalTasks,
    required this.completedTasks,
    required this.pendingTasks,
    required this.totalMembers,
    required this.totalMeetings,
    required this.totalDocuments,
    required this.totalMessages,
    required this.completionRate,
  });

  Map<String, dynamic> toJson() {
    return {
      'totalTasks': totalTasks,
      'completedTasks': completedTasks,
      'pendingTasks': pendingTasks,
      'totalMembers': totalMembers,
      'totalMeetings': totalMeetings,
      'totalDocuments': totalDocuments,
      'totalMessages': totalMessages,
      'completionRate': completionRate,
    };
  }

  @override
  List<Object?> get props => [
        totalTasks,
        completedTasks,
        pendingTasks,
        totalMembers,
        totalMeetings,
        totalDocuments,
        totalMessages,
        completionRate,
      ];
}

class TeamMemberEntity extends Equatable {
  final String id;
  final String fullname;
  final String? avatar;
  final String? email;
  final String? department;
  final String? position;
  final String? role; // Team role: leader, member, viewer
  final DateTime? joinedAt;
  final bool isActive;

  const TeamMemberEntity({
    required this.id,
    required this.fullname,
    this.avatar,
    this.email,
    this.department,
    this.position,
    this.role,
    this.joinedAt,
    this.isActive = true,
  });

  @override
  List<Object?> get props => [
        id,
        fullname,
        avatar,
        email,
        department,
        position,
        role,
        joinedAt,
        isActive,
      ];

  bool get isTeamLeader => role == 'leader';
  bool get isTeamMember => role == 'member';
  bool get isTeamViewer => role == 'viewer';
}
