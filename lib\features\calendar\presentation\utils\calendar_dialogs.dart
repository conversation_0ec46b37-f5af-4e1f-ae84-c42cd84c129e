import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../widgets/add_event_dialog.dart';
import '../widgets/search_events_dialog.dart';
import '../cubit/calendar_cubit.dart';
import '../../domain/entities/calendar_event.dart';
import '../../../upload_employee_face/presentation/cubit/upload_face_cubit.dart';
import '../../../../injection_container.dart' as di;


class CalendarDialogs {

  static void showAddEventDialog(BuildContext context, {DateTime? selectedDate, CalendarEventEntity? eventToEdit}) {
    final calendarCubit = context.read<CalendarCubit>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di.sl<UploadFaceCubit>()),
          BlocProvider.value(value: calendarCubit), // Use the cubit we got earlier
        ],
        child: AddEventDialog(
          selectedDate: selectedDate,
          eventToEdit: eventToEdit,
        ),
      ),
    );
  }


  static void showAddEventDialogWithCubit(
    BuildContext context, {
    required CalendarCubit calendarCubit,
    DateTime? selectedDate,
    CalendarEventEntity? eventToEdit,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider(create: (context) => di.sl<UploadFaceCubit>()),
          BlocProvider.value(value: calendarCubit),
        ],
        child: AddEventDialog(
          selectedDate: selectedDate,
          eventToEdit: eventToEdit,
        ),
      ),
    );
  }

  /// Show search dialog
  static void showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => const SearchEventsDialog(),
    );
  }

  /// Show delete confirmation dialog
  static Future<bool> showDeleteConfirmDialog(
      BuildContext context,
      String eventTitle,
      ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text(
          'Delete Event',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        content: Text(
          'Are you sure you want to delete "$eventTitle"?',
          style: const TextStyle(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}