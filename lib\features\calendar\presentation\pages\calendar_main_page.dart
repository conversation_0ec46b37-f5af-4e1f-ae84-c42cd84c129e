import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/extensions/l10n_extension.dart';
import '../../../../core/extensions/responsive_extension.dart';
import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/show_custom_snackBar.dart';
import '../cubit/calendar_cubit.dart';
import '../cubit/calendar_state.dart';
import '../utils/calendar_dialogs.dart';
import '../widgets/search_events_dialog.dart';
import 'calendar_view.dart' as calendar_view;

/// Calendar Page with UI only
class CalendarPage extends StatelessWidget {
  const CalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const CalendarPageView();
  }
}

/// Main Calendar Page View with responsive design
class CalendarPageView extends StatefulWidget {
  const CalendarPageView({super.key});

  @override
  State<CalendarPageView> createState() => _CalendarPageViewState();
}

class _CalendarPageViewState extends State<CalendarPageView> {
  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildResponsiveAppBar(context, l10n),
      body: BlocListener<CalendarCubit, CalendarState>(
        listener: (context, state) {
          if (state.error != null) {
            showTopSnackBar(
              context,
              title: l10n.error,
              message: state.error!,
              isError: true,
            );
            // Clear error message after showing
            context.read<CalendarCubit>().clearMessages();
          }
          if (state.successMessage != null) {
            showTopSnackBar(
              context,
              title: l10n.success,
              message: state.successMessage!,
              isError: false,
            );
            // Clear message after showing
            context.read<CalendarCubit>().clearMessages();
          }
        },
        child: const calendar_view.CalendarView(),
      ),
      floatingActionButton: _buildResponsiveFloatingActionButton(context, l10n),
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  PreferredSizeWidget _buildResponsiveAppBar(
    BuildContext context,
    dynamic l10n,
  ) {
    return AppBar(
      backgroundColor: AppColors.background,
      elevation: 0,
      scrolledUnderElevation: 0,
      leading: IconButton(
        icon: Icon(
          Icons.arrow_back,
          color: AppColors.textPrimary,
          size: context.rf(24),
        ),
        onPressed: () => context.pop(),
        padding: EdgeInsets.all(context.rw(8)),
      ),
      title: Text(
        l10n.calendarPageTitle,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: AppColors.textPrimary,
          fontWeight: FontWeight.bold,
          fontSize: context.rf(20),
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          icon: Icon(
            Icons.search,
            color: AppColors.textPrimary,
            size: context.rf(24),
          ),
          onPressed: () => _showSearchDialog(context),
          padding: EdgeInsets.all(context.rw(8)),
          tooltip: l10n.searchEvents,
        ),
        IconButton(
          icon: Icon(
            Icons.refresh,
            color: AppColors.textPrimary,
            size: context.rf(24),
          ),
          onPressed: () {}, // No-op for refresh
          padding: EdgeInsets.all(context.rw(8)),
          tooltip: l10n.refresh,
        ),
        SizedBox(width: context.rw(8)),
      ],
    );
  }

  Widget _buildResponsiveFloatingActionButton(
    BuildContext context,
    dynamic l10n,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: context.rh(16), right: context.rw(8)),
      child: FloatingActionButton.extended(
        onPressed: () => _showAddEventDialog(context),
        backgroundColor: AppColors.primaryGreen,
        foregroundColor: Colors.white,
        elevation: 8,
        highlightElevation: 12,
        icon: Icon(Icons.add, size: context.rf(20)),
        label: Text(
          l10n.addEvent,
          style: TextStyle(
            fontSize: context.rf(14),
            fontWeight: FontWeight.w600,
          ),
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  void _showAddEventDialog(BuildContext context) {
    CalendarDialogs.showAddEventDialog(
      context,
      selectedDate: context.read<CalendarCubit>().selectedDate,
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<CalendarCubit>(),
        child: const SearchEventsDialog(),
      ),
    );
  }
}
