import 'package:dartz/dartz.dart';
import 'package:golderhr/core/error/failures.dart';
import 'package:golderhr/core/usecases/usecase.dart';
import 'package:golderhr/features/faceDetection/domain/repositories/attendance_repository.dart';

import '../entities/attendance_status_entity.dart';

class GetTodayAttendanceUseCase
    implements UseCase<AttendanceStatusEntity?, NoParams> {
  final AttendanceRepository repository;

  GetTodayAttendanceUseCase(this.repository);

  @override
  Future<Either<Failure, AttendanceStatusEntity?>> call(NoParams params) async {
    return await repository.getTodayAttendance();
  }
}
