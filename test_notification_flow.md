# Test Notification Flow

## <PERSON><PERSON> tả
Test toàn bộ flow từ overtime request -> notification -> tap notification từ system tray -> navigate to detail page

## Các bước test

### 1. <PERSON><PERSON><PERSON> bị
- <PERSON><PERSON><PERSON> bảo backend đang chạy
- <PERSON><PERSON><PERSON> bảo Flutter app đang chạy trên device/emulator
- <PERSON><PERSON><PERSON> bảo Firebase đã được cấu hình đúng
- <PERSON><PERSON><PERSON> bảo có user với role admin/hr để nhận notification

### 2. Test Overtime Request Notification

#### Bước 1: Gửi overtime request
1. Mở Flutter app
2. Login với user thường (không phải admin)
3. Vào page overtime request
4. <PERSON><PERSON><PERSON> một overtime request mới
5. Kiểm tra console backend xem có log gửi notification không

#### Bước 2: Kiểm tra notification được tạo
1. Check database xem notification đã được tạo chưa
2. Check FCM token của admin user
3. Check log Firebase service

#### Bước 3: Test notification từ system tray
1. <PERSON><PERSON><PERSON> bảo app đang chạy ở background hoặc foreground
2. Khi notification xuất hiện ở system tray, tap vào nó
3. Kiểm tra xem app có navigate đến notification detail page không
4. Kiểm tra thông tin trong notification detail có đúng không

### 3. Test Overtime Approval Notification

#### Bước 1: Approve overtime request
1. Login với admin user
2. Vào admin panel
3. Approve overtime request đã tạo ở bước trước
4. Kiểm tra user gửi request có nhận được notification không

#### Bước 2: Test navigation
1. Tap vào notification từ system tray
2. Kiểm tra navigation đến notification detail

### 4. Test Cases cần kiểm tra

#### Test Case 1: App ở foreground
- Gửi notification
- Tap vào local notification
- Verify navigation

#### Test Case 2: App ở background
- Gửi notification
- Tap vào system notification
- Verify app mở và navigate đúng

#### Test Case 3: App bị terminate
- Kill app hoàn toàn
- Gửi notification
- Tap vào notification
- Verify app mở và navigate đúng

### 5. Kiểm tra dữ liệu

#### Firebase Data Structure
Notification data phải chứa:
```json
{
  "notificationId": "string",
  "type": "overtime_request|overtime_approved|overtime_rejected",
  "title": "string",
  "message": "string",
  "timestamp": "ISO string",
  "isRead": "false",
  "isImportant": "true|false",
  "category": "system",
  "icon": "schedule|check_circle|cancel",
  "color": "#3F51B5|#4CAF50|#F44336",
  "overtimeRequestId": "string" (optional),
  "employeeId": "string" (optional),
  "employeeName": "string" (optional)
}
```

#### Notification Detail Page
Phải hiển thị:
- Title đúng
- Message đúng
- Timestamp đúng
- Icon và color đúng
- Có thể delete notification

### 6. Debug Commands

#### Check notification trong database
```javascript
// MongoDB query
db.notifications.find().sort({createdAt: -1}).limit(5)
```

#### Check FCM tokens
```javascript
// MongoDB query
db.fcmtokens.find({isActive: true})
```

#### Test Firebase notification manually
```bash
# Sử dụng Firebase Admin SDK hoặc Firebase Console
```

### 7. Expected Results

1. ✅ Overtime request tạo notification cho admin
2. ✅ Notification chứa đầy đủ data structure
3. ✅ Tap notification từ system tray navigate đến detail page
4. ✅ Notification detail hiển thị đúng thông tin
5. ✅ Overtime approval tạo notification cho employee
6. ✅ All test cases (foreground, background, terminated) work

### 8. Troubleshooting

#### Nếu notification không xuất hiện:
- Check Firebase configuration
- Check FCM token registration
- Check user permissions
- Check backend logs

#### Nếu navigation không work:
- Check NavigationService registration
- Check Firebase data structure
- Check AppRouter configuration
- Check notification detail route

#### Nếu notification detail không hiển thị đúng:
- Check NotificationModel creation
- Check Firebase data mapping
- Check icon/color mapping
