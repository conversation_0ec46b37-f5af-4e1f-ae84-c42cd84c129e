import 'package:flutter/material.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';
import '../theme/app_colors.dart';

class ModernTimePicker extends StatefulWidget {
  final TimeOfDay? initialTime;
  final String title;
  final Function(TimeOfDay) onTimeSelected;

  const ModernTimePicker({
    super.key,
    this.initialTime,
    required this.title,
    required this.onTimeSelected,
  });

  @override
  State<ModernTimePicker> createState() => _ModernTimePickerState();
}

class _ModernTimePickerState extends State<ModernTimePicker>
    with TickerProviderStateMixin {
  late int selectedHour;
  late int selectedMinute;
  late bool isAM;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    final time = widget.initialTime ?? TimeOfDay.now();
    selectedHour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    selectedMinute = time.minute;
    isAM = time.period == DayPeriod.am;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final responsive = context.responsive;
    final theme = Theme.of(context);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: responsive.padding(all: 24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with gradient background
                  Container(
                    width: double.infinity,
                    padding: responsive.padding(all: 20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.primaryBlue,
                          AppColors.primaryBlue.withValues(alpha: 0.8),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.access_time_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        SizedBox(width: responsive.widthPercentage(3)),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.title,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              Text(
                                'Select your preferred time',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: responsive.heightPercentage(3)),

                  // Time Display with large numbers
                  Container(
                    padding: responsive.padding(all: 24),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppColors.primaryBlue.withValues(alpha: 0.1),
                        width: 2,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Hour display
                        _buildTimeDisplay(
                          selectedHour.toString().padLeft(2, '0'),
                          responsive,
                          theme,
                        ),
                        
                        // Animated colon
                        AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            return Padding(
                              padding: responsive.padding(horizontal: 16),
                              child: Text(
                                ':',
                                style: theme.textTheme.displayMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.primaryBlue.withValues(
                                    alpha: 0.5 + 0.5 * _animationController.value,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                        
                        // Minute display
                        _buildTimeDisplay(
                          selectedMinute.toString().padLeft(2, '0'),
                          responsive,
                          theme,
                        ),
                        
                        SizedBox(width: responsive.widthPercentage(4)),
                        
                        // AM/PM toggle with modern design
                        _buildModernAmPmToggle(responsive, theme),
                      ],
                    ),
                  ),
                  
                  SizedBox(height: responsive.heightPercentage(3)),

                  // Time adjustment controls
                  Row(
                    children: [
                      // Hour controls
                      Expanded(
                        child: _buildTimeControls(
                          'Hour',
                          selectedHour,
                          () => _adjustHour(1),
                          () => _adjustHour(-1),
                          responsive,
                          theme,
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(4)),
                      // Minute controls
                      Expanded(
                        child: _buildTimeControls(
                          'Minute',
                          selectedMinute,
                          () => _adjustMinute(15),
                          () => _adjustMinute(-15),
                          responsive,
                          theme,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: responsive.heightPercentage(3)),

                  // Quick time presets
                  _buildQuickPresets(responsive, theme),
                  
                  SizedBox(height: responsive.heightPercentage(3)),

                  // Action buttons with modern style
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: responsive.padding(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Text(
                            'Cancel',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: AppColors.textSecondary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: responsive.widthPercentage(3)),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _confirmTime,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryBlue,
                            padding: responsive.padding(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 4,
                            shadowColor: AppColors.primaryBlue.withValues(alpha: 0.3),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.check_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                              SizedBox(width: responsive.widthPercentage(2)),
                              Text(
                                'Confirm Time',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimeDisplay(String value, dynamic responsive, ThemeData theme) {
    return Container(
      padding: responsive.padding(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        value,
        style: theme.textTheme.displaySmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.primaryBlue,
        ),
      ),
    );
  }

  Widget _buildModernAmPmToggle(dynamic responsive, ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryBlue.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildAmPmOption('AM', isAM, () => setState(() => isAM = true), responsive, theme),
          Container(height: 1, color: AppColors.primaryBlue.withValues(alpha: 0.1)),
          _buildAmPmOption('PM', !isAM, () => setState(() => isAM = false), responsive, theme),
        ],
      ),
    );
  }

  Widget _buildAmPmOption(String text, bool isSelected, VoidCallback onTap, dynamic responsive, ThemeData theme) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60,
        padding: responsive.padding(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: theme.textTheme.titleMedium?.copyWith(
            color: isSelected ? Colors.white : AppColors.primaryBlue,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildTimeControls(String label, int value, VoidCallback onIncrement, VoidCallback onDecrement, dynamic responsive, ThemeData theme) {
    return Column(
      children: [
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildControlButton(Icons.remove_rounded, onDecrement, responsive),
            Text(
              value.toString().padLeft(2, '0'),
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.primaryBlue,
              ),
            ),
            _buildControlButton(Icons.add_rounded, onIncrement, responsive),
          ],
        ),
      ],
    );
  }

  Widget _buildControlButton(IconData icon, VoidCallback onTap, dynamic responsive) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: responsive.padding(all: 8),
        decoration: BoxDecoration(
          color: AppColors.primaryBlue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppColors.primaryBlue.withValues(alpha: 0.3),
          ),
        ),
        child: Icon(
          icon,
          color: AppColors.primaryBlue,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildQuickPresets(dynamic responsive, ThemeData theme) {
    final presets = [
      {'label': '9:00', 'hour': 9, 'minute': 0, 'isAM': true},
      {'label': '12:00', 'hour': 12, 'minute': 0, 'isAM': false},
      {'label': '1:00', 'hour': 1, 'minute': 0, 'isAM': false},
      {'label': '5:00', 'hour': 5, 'minute': 0, 'isAM': false},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Select',
          style: theme.textTheme.titleSmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: responsive.heightPercentage(1)),
        Row(
          children: presets.map((preset) {
            return Expanded(
              child: Padding(
                padding: responsive.padding(horizontal: 4),
                child: GestureDetector(
                  onTap: () => _setPresetTime(preset),
                  child: Container(
                    padding: responsive.padding(vertical: 12),
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.primaryBlue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      preset['label'] as String,
                      textAlign: TextAlign.center,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  void _adjustHour(int delta) {
    setState(() {
      selectedHour += delta;
      if (selectedHour > 12) selectedHour = 1;
      if (selectedHour < 1) selectedHour = 12;
    });
  }

  void _adjustMinute(int delta) {
    setState(() {
      selectedMinute += delta;
      if (selectedMinute >= 60) selectedMinute = 0;
      if (selectedMinute < 0) selectedMinute = 45;
    });
  }

  void _setPresetTime(Map<String, dynamic> preset) {
    setState(() {
      selectedHour = preset['hour'] as int;
      selectedMinute = preset['minute'] as int;
      isAM = preset['isAM'] as bool;
    });
  }

  void _confirmTime() {
    int hour24 = selectedHour;
    if (isAM && selectedHour == 12) {
      hour24 = 0;
    } else if (!isAM && selectedHour != 12) {
      hour24 = selectedHour + 12;
    }

    final timeOfDay = TimeOfDay(hour: hour24, minute: selectedMinute);
    widget.onTimeSelected(timeOfDay);
    Navigator.of(context).pop();
  }
}
