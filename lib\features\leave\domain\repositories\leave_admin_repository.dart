import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/leave_request.dart';

abstract class LeaveAdminRepository {
  /// Get all leave requests for admin with pagination and filtering
  Future<Either<Failure, List<LeaveRequest>>> getAllLeaveRequests({
    int page = 1,
    int limit = 10,
    String? status,
  });

  /// Approve a leave request
  Future<Either<Failure, LeaveRequest>> approveLeaveRequest(
    String requestId,
  );

  /// Reject a leave request with reason
  Future<Either<Failure, LeaveRequest>> rejectLeaveRequest(
    String requestId,
    String rejectionReason,
  );
}
