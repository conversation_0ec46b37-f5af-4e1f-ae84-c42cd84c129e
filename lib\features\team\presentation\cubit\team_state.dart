part of 'team_cubit.dart';

abstract class TeamState extends Equatable {
  const TeamState();

  @override
  List<Object?> get props => [];
}

class TeamInitial extends TeamState {}

class TeamLoading extends TeamState {}

class TeamsLoaded extends TeamState {
  final List<TeamEntity> teams;

  const TeamsLoaded(this.teams);

  @override
  List<Object> get props => [teams];
}

class TeamDetailsLoaded extends TeamState {
  final TeamEntity team;

  const TeamDetailsLoaded(this.team);

  @override
  List<Object> get props => [team];
}

class TeamSelected extends TeamState {
  final TeamEntity team;

  const TeamSelected(this.team);

  @override
  List<Object> get props => [team];
}

class TeamCreated extends TeamState {
  final TeamEntity team;

  const TeamCreated(this.team);

  @override
  List<Object> get props => [team];
}

class TeamStatsLoaded extends TeamState {
  final Map<String, dynamic> stats;

  const TeamStatsLoaded(this.stats);

  @override
  List<Object> get props => [stats];
}

class TeamError extends TeamState {
  final String message;

  const TeamError(this.message);

  @override
  List<Object> get props => [message];
}
