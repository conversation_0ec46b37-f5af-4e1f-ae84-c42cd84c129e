// file: features/home/<USER>/pages/home_page.dart

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/features/attendance/domain/usecases/get_weekly_summary.dart';
import 'package:golderhr/features/attendance/presentation/cubit/attendance_page/attendance_cubit.dart';
import 'package:golderhr/features/home/<USER>/cubit/home_cubit.dart';
import 'package:golderhr/features/notification/domain/usecases/get_notifications.dart';

import '../../../../injection_container.dart';
import '../../../attendance/domain/usecases/get_attendance_history.dart';
import '../../../attendance/domain/usecases/get_monthly_summary.dart';
import '../../../attendance/domain/usecases/get_today_summary.dart';
import '../../../cubit/clock_cubit.dart';
import '../../../cubit/greeting_cubit.dart';
import '../../../faceDetection/domain/usecase/check_in_usecase.dart';
import '../../../faceDetection/domain/usecase/check_out_usecase.dart';
import '../../../faceDetection/domain/usecase/get_today_attendance_usecase.dart';
import '../../../faceDetection/presentation/cubit/face_checkin_cubit.dart';
import 'home_view.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => GreetingCubit()),
        BlocProvider(create: (_) => ClockCubit()),
        BlocProvider(
          create: (_) => FaceDetectionCubit(
            getTodayAttendanceUseCase: sl<GetTodayAttendanceUseCase>(),
            checkInUseCase: sl<CheckInUseCase>(),
            checkOutUseCase: sl<CheckOutUseCase>(),

            l10n: context.l10n,
          )..initialize(),
        ),
        BlocProvider(
          create: (_) => AttendanceCubitV1(
            getTodaySummary: sl<GetTodaySummary>(),
            getWeeklySummary: sl<GetWeeklySummary>(),
            getMonthlySummary: sl<GetMonthlySummary>(),
            getAttendanceHistory: sl<GetAttendanceHistory>(),
          )..loadInitialData(),
        ),
        BlocProvider(
          create: (_) => HomeCubit(
            getNotificationsUseCase: sl<GetNotificationsUseCase>(),
          )..fetchNotificationsHome(l10n: context.l10n), // Initialize fetching notifications
        ),
      ],
      child: const HomeView(),
    );
  }
}
