// lib/features/attendance/domain/usecases/get_weekly_summary.dart
import 'package:dartz/dartz.dart';
import 'package:golderhr/features/attendance/domain/entities/weekly_summary.dart';
import 'package:golderhr/features/attendance/domain/repositories/attendance_repository.dart';

import '../../../../core/error/failures.dart';
import '../../../../core/usecases/usecase.dart';

class GetWeeklySummary implements UseCase<WeeklySummary, NoParams> {
  final AttendanceRepositoryV1 repository;

  GetWeeklySummary(this.repository);

  @override
  Future<Either<Failure, WeeklySummary>> call(NoParams params) async {
    return await repository.getWeeklySummary();
  }
}
