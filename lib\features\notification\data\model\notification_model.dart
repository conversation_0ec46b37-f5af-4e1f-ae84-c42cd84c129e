import 'package:flutter/material.dart';
import '../../domain/entities/notification_entity.dart';

enum NotificationCategory {
  system,
  customer,
  promotion,
  attendance,
  leave,
  announcement,
  reminder,
  custom,
}

class NotificationModel extends NotificationEntity {
  const NotificationModel({
    required super.id,
    required super.title,
    required super.message,
    required super.timestamp,
    required super.icon,
    required super.color,
    super.isRead = false,
    super.isImportant = false,
    required super.category,
  });

  // Parse từ API response
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['_id'] ?? json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      timestamp: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      icon: _getIconFromType(json['type'] ?? 'system'),
      color: _getColorFromPriority(json['priority'] ?? 'medium'),
      isRead: _isReadByCurrentUser(json['recipients'] ?? []),
      isImportant: json['priority'] == 'urgent' || json['priority'] == 'high',
      category: _getCategoryFromType(json['type'] ?? 'system'),
    );
  }

  // Helper methods
  static IconData _getIconFromType(String type) {
    switch (type) {
      case 'attendance':
        return Icons.access_time;
      case 'leave':
        return Icons.event_busy;
      case 'announcement':
        return Icons.campaign;
      case 'reminder':
        return Icons.notifications_active;
      case 'custom':
        return Icons.info;
      case 'system':
      default:
        return Icons.notifications;
    }
  }

  static Color _getColorFromPriority(String priority) {
    switch (priority) {
      case 'urgent':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.blue;
      case 'low':
      default:
        return Colors.grey;
    }
  }

  static NotificationCategory _getCategoryFromType(String type) {
    switch (type) {
      case 'attendance':
        return NotificationCategory.attendance;
      case 'leave':
        return NotificationCategory.leave;
      case 'announcement':
        return NotificationCategory.announcement;
      case 'reminder':
        return NotificationCategory.reminder;
      case 'custom':
        return NotificationCategory.custom;
      case 'customer':
        return NotificationCategory.customer;
      case 'system':
      default:
        return NotificationCategory.system;
    }
  }

  static bool _isReadByCurrentUser(List<dynamic> recipients) {
    // Trong thực tế, cần check với current user ID
    // Hiện tại return false để hiển thị là chưa đọc
    if (recipients.isEmpty) return false;

    // Giả sử recipient đầu tiên là current user
    final firstRecipient = recipients.first;
    return firstRecipient['isRead'] ?? false;
  }

  // Giúp cập nhật trạng thái một cách bất biến
  NotificationModel copyWith({bool? isRead}) {
    return NotificationModel(
      id: id,
      title: title,
      message: message,
      timestamp: timestamp,
      icon: icon,
      color: color,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant,
      category: category,
    );
  }
}
