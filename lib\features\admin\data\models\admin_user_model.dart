import '../../domain/entities/admin_user_entity.dart';

class AdminUserModel extends AdminUserEntity
    implements Comparable<AdminUserModel> {
  const AdminUserModel({
    required super.id,
    required super.fullname,
    required super.email,
    super.avatar,
    super.phone,
    super.department,
    super.position,
    super.point,
    required super.isdisable,
    required super.role,
    super.organization,
    required super.isdeleted,
    super.idMapper,
    super.codeMapper,
    required super.createdAt,
    required super.updatedAt,
  });

  factory AdminUserModel.fromJson(Map<String, dynamic> json) {
    return AdminUserModel(
      id: json['_id'] ?? json['id'] ?? '',
      fullname: json['fullname'] ?? '',
      email: json['email'] ?? '',
      avatar: json['avatar'],
      phone: json['phone'],
      department: json['department'],
      position: json['position'],
      point: (json['point'] ?? 0).toDouble(),
      isdisable: json['isdisable'] ?? false,
      role: _parseRole(json['role']),
      organization: _parseOrganization(json['organization']),
      isdeleted: json['isdeleted'] ?? false,
      idMapper: json['IdMapper'],
      codeMapper: json['CodeMapper'],
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'fullname': fullname,
      'email': email,
      'avatar': avatar,
      'phone': phone,
      'department': department,
      'position': position,
      'point': point,
      'isdisable': isdisable,
      'role': role.toJson(),
      'organization': organization?.toJson(),
      'isdeleted': isdeleted,
      'IdMapper': idMapper,
      'CodeMapper': codeMapper,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  static UserRole _parseRole(dynamic roleData) {
    if (roleData == null) {
      return const UserRole(id: '', name: 'user');
    }

    if (roleData is String) {
      return UserRole(id: '', name: roleData);
    }

    if (roleData is Map<String, dynamic>) {
      return UserRole(
        id: roleData['_id'] ?? roleData['id'] ?? '',
        name: roleData['name'] ?? 'user',
      );
    }

    return const UserRole(id: '', name: 'user');
  }

  static UserOrganization? _parseOrganization(dynamic orgData) {
    if (orgData == null) return null;

    if (orgData is String) {
      return UserOrganization(id: orgData, name: '');
    }

    if (orgData is Map<String, dynamic>) {
      return UserOrganization(
        id: orgData['_id'] ?? orgData['id'] ?? '',
        name: orgData['name'] ?? '',
      );
    }

    return null;
  }

  AdminUserModel copyWith({
    String? id,
    String? fullname,
    String? email,
    String? avatar,
    String? phone,
    String? department,
    String? position,
    double? point,
    bool? isdisable,
    UserRole? role,
    UserOrganization? organization,
    bool? isdeleted,
    int? idMapper,
    String? codeMapper,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminUserModel(
      id: id ?? this.id,
      fullname: fullname ?? this.fullname,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      phone: phone ?? this.phone,
      department: department ?? this.department,
      position: position ?? this.position,
      point: point ?? this.point,
      isdisable: isdisable ?? this.isdisable,
      role: role ?? this.role,
      organization: organization ?? this.organization,
      isdeleted: isdeleted ?? this.isdeleted,
      idMapper: idMapper ?? this.idMapper,
      codeMapper: codeMapper ?? this.codeMapper,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  int compareTo(AdminUserModel other) {
    // Default comparison by fullname
    return fullname.toLowerCase().compareTo(other.fullname.toLowerCase());
  }
}

class AdminUserListResponse {
  final List<AdminUserModel> users;
  final AdminUserPagination pagination;

  const AdminUserListResponse({required this.users, required this.pagination});

  factory AdminUserListResponse.fromJson(Map<String, dynamic> json) {
    return AdminUserListResponse(
      users:
          (json['users'] as List<dynamic>?)
              ?.map(
                (user) => AdminUserModel.fromJson(user as Map<String, dynamic>),
              )
              .toList() ??
          [],
      pagination: AdminUserPagination.fromJson(json['pagination'] ?? {}),
    );
  }
}

class AdminUserPagination {
  final int currentPage;
  final int totalPages;
  final int totalUsers;
  final bool hasNext;
  final bool hasPrev;

  const AdminUserPagination({
    required this.currentPage,
    required this.totalPages,
    required this.totalUsers,
    required this.hasNext,
    required this.hasPrev,
  });

  factory AdminUserPagination.fromJson(Map<String, dynamic> json) {
    return AdminUserPagination(
      currentPage: json['currentPage'] ?? 1,
      totalPages: json['totalPages'] ?? 1,
      totalUsers: json['totalUsers'] ?? 0,
      hasNext: json['hasNext'] ?? false,
      hasPrev: json['hasPrev'] ?? false,
    );
  }
}

class AdminUserStatistics {
  final AdminUserOverview overview;
  final List<RoleDistribution> roleDistribution;
  final List<DepartmentDistribution> departmentDistribution;

  const AdminUserStatistics({
    required this.overview,
    required this.roleDistribution,
    required this.departmentDistribution,
  });

  factory AdminUserStatistics.fromJson(Map<String, dynamic> json) {
    return AdminUserStatistics(
      overview: AdminUserOverview.fromJson(json['overview'] ?? {}),
      roleDistribution:
          (json['roleDistribution'] as List<dynamic>?)
              ?.map(
                (role) =>
                    RoleDistribution.fromJson(role as Map<String, dynamic>),
              )
              .toList() ??
          [],
      departmentDistribution:
          (json['departmentDistribution'] as List<dynamic>?)
              ?.map(
                (dept) => DepartmentDistribution.fromJson(
                  dept as Map<String, dynamic>,
                ),
              )
              .toList() ??
          [],
    );
  }
}

class AdminUserOverview {
  final int totalUsers;
  final int activeUsers;
  final int disabledUsers;
  final int deletedUsers;

  const AdminUserOverview({
    required this.totalUsers,
    required this.activeUsers,
    required this.disabledUsers,
    required this.deletedUsers,
  });

  factory AdminUserOverview.fromJson(Map<String, dynamic> json) {
    return AdminUserOverview(
      totalUsers: json['totalUsers'] ?? 0,
      activeUsers: json['activeUsers'] ?? 0,
      disabledUsers: json['disabledUsers'] ?? 0,
      deletedUsers: json['deletedUsers'] ?? 0,
    );
  }
}

class RoleDistribution {
  final String roleName;
  final int count;

  const RoleDistribution({required this.roleName, required this.count});

  factory RoleDistribution.fromJson(Map<String, dynamic> json) {
    return RoleDistribution(
      roleName: json['_id'] ?? '',
      count: json['count'] ?? 0,
    );
  }
}

class DepartmentDistribution {
  final String departmentName;
  final int count;

  const DepartmentDistribution({
    required this.departmentName,
    required this.count,
  });

  factory DepartmentDistribution.fromJson(Map<String, dynamic> json) {
    return DepartmentDistribution(
      departmentName: json['_id'] ?? '',
      count: json['count'] ?? 0,
    );
  }
}
