import 'package:equatable/equatable.dart';

class OvertimeSummaryEntity extends Equatable {
  final double thisMonthHours;
  final double thisWeekHours;
  final int pendingRequests;
  final int approvedRequests;
  final int rejectedRequests;
  final double totalHoursThisYear;

  const OvertimeSummaryEntity({
    required this.thisMonthHours,
    required this.thisWeekHours,
    required this.pendingRequests,
    required this.approvedRequests,
    required this.rejectedRequests,
    required this.totalHoursThisYear,
  });

  @override
  List<Object?> get props => [
    thisMonthHours,
    thisWeekHours,
    pendingRequests,
    approvedRequests,
    rejectedRequests,
    totalHoursThisYear,
  ];

  OvertimeSummaryEntity copyWith({
    double? thisMonthHours,
    double? thisWeekHours,
    int? pendingRequests,
    int? approvedRequests,
    int? rejectedRequests,
    double? totalHoursThisYear,
  }) {
    return OvertimeSummaryEntity(
      thisMonthHours: thisMonthHours ?? this.thisMonthHours,
      thisWeekHours: thisWeekHours ?? this.thisWeekHours,
      pendingRequests: pendingRequests ?? this.pendingRequests,
      approvedRequests: approvedRequests ?? this.approvedRequests,
      rejectedRequests: rejectedRequests ?? this.rejectedRequests,
      totalHoursThisYear: totalHoursThisYear ?? this.totalHoursThisYear,
    );
  }
}
