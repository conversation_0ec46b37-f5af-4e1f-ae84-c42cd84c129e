import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../domain/entities/task_entity.dart';
import '../../domain/repositories/task_repository.dart';
import '../datasources/task_remote_data_source.dart';

class TaskRepositoryImpl implements TaskRepository {
  final TaskRemoteDataSource remoteDataSource;

  TaskRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<TaskEntity>>> getUserTasks({
    String? status,
    String? priority,
    String? teamId,
    String? dueDate,
  }) async {
    try {
      final tasks = await remoteDataSource.getUserTasks(
        status: status,
        priority: priority,
        teamId: teamId,
        dueDate: dueDate,
      );
      return Right(tasks);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, List<TaskEntity>>> getTeamTasks(String teamId, {String? status}) async {
    try {
      final tasks = await remoteDataSource.getTeamTasks(teamId, status: status);
      return Right(tasks);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> createTask(Map<String, dynamic> taskData) async {
    try {
      final task = await remoteDataSource.createTask(taskData);
      return Right(task);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> updateTaskStatus(String taskId, String status) async {
    try {
      final task = await remoteDataSource.updateTaskStatus(taskId, status);
      return Right(task);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, TaskEntity>> addTaskComment(String taskId, String message) async {
    try {
      final task = await remoteDataSource.addTaskComment(taskId, message);
      return Right(task);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getTaskStats({String? teamId}) async {
    try {
      final stats = await remoteDataSource.getTaskStats(teamId: teamId);
      return Right(stats);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } catch (e) {
      return Left(const ServerFailure('Unexpected error occurred'));
    }
  }
}
