import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:golderhr/core/extensions/app_theme_extension.dart';
import 'package:golderhr/core/extensions/l10n_extension.dart';
import 'package:golderhr/core/extensions/responsive_extension.dart';

import 'package:golderhr/shared/widgets/show_custom_snackBar.dart';
import 'package:iconsax/iconsax.dart';

import '../cubit/role_cubit.dart';
import '../widgets/create_role_dialog.dart';
import '../widgets/edit_role_dialog.dart';
import '../widgets/role_search_widget.dart';
import '../widgets/role_card_widget.dart';
import '../widgets/role_empty_state_widget.dart';
import '../widgets/role_delete_confirmation_dialog.dart';
import '../widgets/role_system_warning_dialog.dart';
import '../../domain/entities/role_entity.dart';

class RoleManagementPage extends StatefulWidget {
  const RoleManagementPage({super.key});

  @override
  State<RoleManagementPage> createState() => _RoleManagementPageState();
}

class _RoleManagementPageState extends State<RoleManagementPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadInitialData();
    _setupScrollListener();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  void _loadInitialData() {
    context.read<RoleCubit>().loadRoles();
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent * 0.8) {
        context.read<RoleCubit>().loadMoreRoles(
          search: _searchController.text.trim().isEmpty
              ? null
              : _searchController.text.trim(),
        );
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.lightTheme;
    final responsive = context.responsive;
    final l10n = context.l10n;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Role Management', // TODO: Use l10n.roleManagement after running flutter gen-l10n
          style: theme.textTheme.displaySmall!.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => context.read<RoleCubit>().refreshRoles(
              search: _searchController.text.trim().isEmpty
                  ? null
                  : _searchController.text.trim(),
            ),
            icon: const Icon(Iconsax.refresh),
            tooltip: 'Refresh',
          ),
          IconButton(
            onPressed: () => _showCreateRoleDialog(context),
            icon: const Icon(Iconsax.add),
            tooltip: 'Add Role',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: BlocConsumer<RoleCubit, RoleState>(
            listener: (context, state) {
              if (state.hasError) {
                showTopSnackBar(
                  context,
                  title: l10n.error,
                  message: state.error!,
                  isError: true,
                );
                context.read<RoleCubit>().clearMessages();
              } else if (state.hasSuccess) {
                showTopSnackBar(
                  context,
                  title: l10n.success,
                  message: state.successMessage!,
                  isError: false,
                );
                context.read<RoleCubit>().clearMessages();
              }
            },
            builder: (context, state) {
              return Column(
                children: [
                  // Search Section
                  RoleSearchWidget(
                    searchController: _searchController,
                    onSearchChanged: () => setState(() {}),
                  ),

                  // Roles List
                  Expanded(
                    child: state.isLoading && state.roles.isEmpty
                        ? Center(
                            child: TweenAnimationBuilder<double>(
                              duration: const Duration(milliseconds: 800),
                              tween: Tween(begin: 0.0, end: 1.0),
                              curve: Curves.easeInOut,
                              builder: (context, value, child) {
                                return Transform.scale(
                                  scale: 0.8 + (0.2 * value),
                                  child: Opacity(
                                    opacity: value,
                                    child: const CircularProgressIndicator(),
                                  ),
                                );
                              },
                            ),
                          )
                        : state.roles.isEmpty
                        ? RoleEmptyStateWidget(
                            onCreateRole: () => _showCreateRoleDialog(context),
                          )
                        : RefreshIndicator(
                            onRefresh: () =>
                                context.read<RoleCubit>().refreshRoles(
                                  search: _searchController.text.trim().isEmpty
                                      ? null
                                      : _searchController.text.trim(),
                                ),
                            child: ListView.builder(
                              controller: _scrollController,
                              padding: responsive.padding(all: 16),
                              itemCount:
                                  state.roles.length +
                                  (state.isLoading ? 1 : 0),
                              itemBuilder: (context, index) {
                                if (index >= state.roles.length) {
                                  return Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: TweenAnimationBuilder<double>(
                                        duration: const Duration(
                                          milliseconds: 600,
                                        ),
                                        tween: Tween(begin: 0.0, end: 1.0),
                                        curve: Curves.easeInOut,
                                        builder: (context, value, child) {
                                          return Transform.scale(
                                            scale: 0.7 + (0.3 * value),
                                            child: Opacity(
                                              opacity: value,
                                              child:
                                                  const CircularProgressIndicator(),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  );
                                }

                                final role = state.roles[index];
                                return AnimatedContainer(
                                  duration: Duration(
                                    milliseconds: 300 + (index * 50),
                                  ),
                                  curve: Curves.easeOutCubic,
                                  child: TweenAnimationBuilder<double>(
                                    duration: Duration(
                                      milliseconds: 400 + (index * 100),
                                    ),
                                    tween: Tween(begin: 0.0, end: 1.0),
                                    curve: Curves.easeOutCubic,
                                    builder: (context, value, child) {
                                      return Transform.translate(
                                        offset: Offset(0, 20 * (1 - value)),
                                        child: Opacity(
                                          opacity: value,
                                          child: RoleCardWidget(
                                            role: role,
                                            onRoleAction: (action) =>
                                                _handleRoleAction(role, action),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              },
                            ),
                          ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  void _handleRoleAction(RoleEntity role, String action) {
    switch (action) {
      case 'edit':
        _showEditRoleDialog(context, role);
        break;
      case 'delete':
        _showDeleteConfirmation(context, role);
        break;
    }
  }

  void _showCreateRoleDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<RoleCubit>(),
        child: const CreateRoleDialog(),
      ),
    );
  }

  void _showEditRoleDialog(BuildContext context, RoleEntity role) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<RoleCubit>(),
        child: EditRoleDialog(role: role),
      ),
    );
  }

  void _showDeleteConfirmation(BuildContext context, RoleEntity role) {
    // Check if it's a system role
    final systemRoles = ['admin', 'hr', 'manager', 'user'];
    final isSystemRole = systemRoles.contains(role.name.toLowerCase());

    if (isSystemRole) {
      _showSystemRoleWarning(context, role);
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<RoleCubit>(),
        child: RoleDeleteConfirmationDialog(role: role),
      ),
    );
  }

  void _showSystemRoleWarning(BuildContext context, RoleEntity role) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => RoleSystemWarningDialog(role: role),
    );
  }
}
